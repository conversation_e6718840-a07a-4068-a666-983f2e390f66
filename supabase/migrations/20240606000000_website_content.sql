-- Enable UUID extension if not already enabled
DO $$ 
BEGIN
  CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
EXCEPTION
  WHEN insufficient_privilege THEN
    NULL; -- ignore if we don't have permission
END
$$;

-- Create website_content table if it doesn't exist
DO $$ 
BEGIN
  CREATE TABLE IF NOT EXISTS website_content (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    data JSONB NOT NULL DEFAULT '{}'::jsonb,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
  );
EXCEPTION
  WHEN insufficient_privilege THEN
    RAISE NOTICE 'Insufficient privileges to create table';
END
$$;

-- <PERSON><PERSON> function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_website_content_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update timestamp
DROP TRIGGER IF EXISTS website_content_update_timestamp ON website_content;
CREATE TRIGGER website_content_update_timestamp
    BEFORE UPDATE ON website_content
    FOR EACH ROW
    EXECUTE FUNCTION update_website_content_timestamp();
