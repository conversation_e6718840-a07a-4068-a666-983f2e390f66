# Dynamic Content Management System

## Overview

The Anantasagar Kshetramu website now features a comprehensive dynamic content management system that allows you to update all website content without touching the code. This system provides both a JSON-based approach and an admin panel interface.

## Features

### ✅ **Dynamic Content Areas**
- **Site Information** (name, tagline, description, logo)
- **Contact Details** (phone, email, address, coordinates)
- **Hero Section** (slides, images, titles, subtitles)
- **Announcements** (multilingual support)
- **About Page Content** (sections, images, text)
- **Social Media Links**
- **Navigation Menu Items**

### ✅ **Admin Panel**
- **User-friendly interface** at `/admin/dynamic`
- **Real-time content editing**
- **Live preview of changes**
- **Content validation**
- **Backup and restore functionality**

### ✅ **Content Storage**
- **JSON-based configuration** (`src/data/content.json`)
- **LocalStorage for admin changes**
- **Easy backup and migration**
- **Version control friendly**

## How to Use

### Method 1: Admin Panel (Recommended)

1. **Access the Admin Panel**
   ```
   https://yourwebsite.com/admin/login
   ```
   
2. **Navigate to Dynamic Content**
   ```
   Admin Dashboard → Dynamic Content
   ```

3. **Edit Content Sections**
   - **Site Info**: Update website name, tagline, description
   - **Contact**: Modify phone, email, address, coordinates
   - **Hero**: Manage hero slides and images
   - **Announcements**: Add/edit announcements in multiple languages

4. **Save Changes**
   - Click "Save" for each section
   - Changes are applied immediately
   - Content is stored in browser localStorage

### Method 2: Direct JSON Editing

1. **Edit Content File**
   ```
   src/data/content.json
   ```

2. **Update Desired Sections**
   ```json
   {
     "siteInfo": {
       "name": "Your Temple Name",
       "tagline": "Your Tagline",
       "description": "Your Description"
     },
     "hero": {
       "slides": [
         {
           "id": 1,
           "image": "/images/hero/your-image.jpg",
           "title": "Your Title",
           "subtitle": "Your Subtitle",
           "showText": false
         }
       ]
     }
   }
   ```

3. **Rebuild and Deploy**
   ```bash
   npm run build
   ```

## Content Structure

### Site Information
```json
{
  "siteInfo": {
    "name": "Anantasagar Kshetramu",
    "tagline": "Divine Spiritual Center",
    "description": "Devoted to spiritual growth...",
    "logo": "/images/logo.png",
    "favicon": "/favicon.ico"
  }
}
```

### Contact Information
```json
{
  "contact": {
    "phone": "+91 82477 21046",
    "email": "<EMAIL>",
    "address": {
      "line1": "Anantasagar Rajiv Rahadari Highway",
      "line2": "Siddipet, India"
    },
    "coordinates": {
      "lat": 18.2058612,
      "lng": 78.9885657
    },
    "socialMedia": {
      "facebook": "https://facebook.com",
      "instagram": "https://instagram.com",
      "youtube": "https://youtube.com"
    }
  }
}
```

### Hero Section
```json
{
  "hero": {
    "slides": [
      {
        "id": 1,
        "image": "/images/hero/hero_1.jpg",
        "title": "Welcome to Anantasagar Kshetramu",
        "subtitle": "A Sacred Place of Worship",
        "showText": false
      }
    ]
  }
}
```

### Announcements
```json
{
  "announcements": [
    {
      "id": 1,
      "text": "శ్రీ సరస్వతీ యజ్ఞం - మే 25, 2025",
      "priority": "high",
      "active": true,
      "language": "te"
    }
  ]
}
```

## Advanced Features

### Multilingual Support
- **Telugu (te)**: Primary language
- **English (en)**: Secondary language  
- **Hindi (hi)**: Additional language

### Priority Levels
- **high**: Important announcements (red notification)
- **medium**: Regular updates (orange notification)
- **low**: General information (blue notification)

### Image Management
- **Hero Images**: Place in `/public/images/hero/`
- **About Images**: Place in `/public/images/`
- **Event Images**: Place in `/public/images/events/`

## Technical Implementation

### Context Provider
```typescript
import { useContent } from '@/contexts/ContentContext';

const { content, loading, updateContent } = useContent();
```

### Component Usage
```typescript
// Access dynamic content in any component
const heroSlides = content?.hero?.slides || fallbackSlides;
const siteName = content?.siteInfo?.name || "Default Name";
```

### Update Content
```typescript
// Update content programmatically
await updateContent({
  siteInfo: {
    name: "New Site Name",
    tagline: "New Tagline"
  }
});
```

## Benefits

### ✅ **For Administrators**
- **No coding required** to update content
- **Real-time changes** without rebuilding
- **User-friendly interface**
- **Content validation** prevents errors

### ✅ **For Developers**
- **Centralized content management**
- **Type-safe content structure**
- **Easy to extend** with new content types
- **Version control friendly**

### ✅ **For Users**
- **Consistent experience** across all pages
- **Fast loading** with optimized content delivery
- **Mobile responsive** content management

## Future Enhancements

### Planned Features
- **Image upload interface**
- **Content scheduling**
- **Multi-user permissions**
- **Content versioning**
- **Database integration**
- **API endpoints for external updates**

## Support

For technical support or questions about the dynamic content system:
- Check the admin panel help section
- Review this documentation
- Contact the development team

---

**Note**: This dynamic content system is designed to be user-friendly while maintaining the website's performance and security. All changes are validated and sanitized before being applied.
