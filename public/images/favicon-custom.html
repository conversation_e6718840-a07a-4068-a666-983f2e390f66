<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Favicon Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            margin: 20px;
        }
        canvas {
            border: 1px solid #ccc;
            margin: 20px auto;
            display: block;
        }
        button {
            padding: 10px 20px;
            background-color: #f8a15f;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        button:hover {
            background-color: #e07e3c;
        }
        #preview {
            margin: 20px auto;
            width: 64px;
            height: 64px;
        }
    </style>
</head>
<body>
    <h1>Favicon Generator</h1>
    <p>This page generates a favicon based on the Anantasagareshwari logo</p>
    <canvas id="faviconCanvas" width="64" height="64"></canvas>
    <div>
        <button id="generateBtn">Generate Favicon</button>
        <button id="downloadBtn">Download Favicon</button>
    </div>
    <div>
        <h3>Preview:</h3>
        <img id="preview" src="" alt="Favicon Preview">
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const canvas = document.getElementById('faviconCanvas');
            const ctx = canvas.getContext('2d');
            const preview = document.getElementById('preview');
            const generateBtn = document.getElementById('generateBtn');
            const downloadBtn = document.getElementById('downloadBtn');
            
            function drawFavicon() {
                // Clear the canvas
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // Draw the orange circular base
                ctx.beginPath();
                ctx.arc(32, 32, 24, 0, Math.PI * 2);
                ctx.fillStyle = '#f8a15f';
                ctx.fill();
                
                // Draw the stylized "ఓం" character
                ctx.fillStyle = '#ffffff';
                
                // Main circle outline
                ctx.beginPath();
                ctx.arc(32, 32, 18, 0, Math.PI * 2);
                ctx.lineWidth = 3;
                ctx.strokeStyle = '#ffffff';
                ctx.stroke();
                
                // Inner details - left dot
                ctx.beginPath();
                ctx.arc(26, 28, 3, 0, Math.PI * 2);
                ctx.fill();
                
                // Inner details - right dot
                ctx.beginPath();
                ctx.arc(38, 28, 3, 0, Math.PI * 2);
                ctx.fill();
                
                // Curved line at bottom
                ctx.beginPath();
                ctx.moveTo(26, 36);
                ctx.quadraticCurveTo(32, 42, 38, 36);
                ctx.lineWidth = 3;
                ctx.strokeStyle = '#ffffff';
                ctx.stroke();
                
                // Update preview
                preview.src = canvas.toDataURL('image/png');
            }
            
            function downloadFavicon() {
                const link = document.createElement('a');
                link.download = 'favicon.png';
                link.href = canvas.toDataURL('image/png');
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
            
            // Initial draw
            drawFavicon();
            
            // Event listeners
            generateBtn.addEventListener('click', drawFavicon);
            downloadBtn.addEventListener('click', downloadFavicon);
        });
    </script>
</body>
</html>
