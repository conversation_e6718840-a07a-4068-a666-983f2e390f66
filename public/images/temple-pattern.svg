
<svg width="800" height="800" viewBox="0 0 800 800" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- <PERSON> -->
  <g opacity="0.1">
    <path d="M400 100 C450 150, 500 200, 600 250 C500 300, 450 350, 400 400 C350 350, 300 300, 200 250 C300 200, 350 150, 400 100 Z" fill="url(#lotus_gradient)"/>
    <path d="M400 400 C450 350, 500 300, 600 250 C500 200, 450 150, 400 100 C350 150, 300 200, 200 250 C300 300, 350 350, 400 400 Z" fill="url(#lotus_gradient)"/>
    <path d="M400 400 C450 450, 500 500, 600 550 C500 600, 450 650, 400 700 C350 650, 300 600, 200 550 C300 500, 350 450, 400 400 Z" fill="url(#lotus_gradient)"/>
    <path d="M400 400 C450 450, 500 500, 600 550 C500 600, 450 650, 400 700 C350 650, 300 600, 200 550 C300 500, 350 450, 400 400 Z" fill="url(#lotus_gradient)"/>
  </g>

  <!-- Mandala patterns -->
  <g opacity="0.1">
    <circle cx="200" cy="200" r="20" fill="#800020" />
    <circle cx="600" cy="200" r="20" fill="#FF7722" />
    <circle cx="200" cy="600" r="20" fill="#FF7722" />
    <circle cx="600" cy="600" r="20" fill="#800020" />
    
    <circle cx="100" cy="400" r="15" fill="#FFD700" />
    <circle cx="700" cy="400" r="15" fill="#FFD700" />
    <circle cx="400" cy="100" r="15" fill="#FFD700" />
    <circle cx="400" cy="700" r="15" fill="#FFD700" />
  </g>

  <!-- Om Symbol -->
  <g opacity="0.1" transform="translate(400, 400) scale(0.2)">
    <path d="M0,-300 C-165,-300 -300,-165 -300,0 C-300,165 -165,300 0,300 C165,300 300,165 300,0 C300,-165 165,-300 0,-300 Z M0,-200 C-110,-200 -200,-110 -200,0 C-200,110 -110,200 0,200 C110,200 200,110 200,0 C200,-110 110,-200 0,-200 Z" fill="#FFD700" />
    <path d="M0,-100 C-55,-100 -100,-55 -100,0 C-100,55 -55,100 0,100 C55,100 100,55 100,0 C100,-55 55,-100 0,-100 Z M0,-50 C-27.5,-50 -50,-27.5 -50,0 C-50,27.5 -27.5,50 0,50 C27.5,50 50,27.5 50,0 C50,-27.5 27.5,-50 0,-50 Z" fill="#FF7722" />
    <path d="M100,0 C100,55 55,100 0,100 C-55,100 -100,55 -100,0 C-100,-55 -55,-100 0,-100 C55,-100 100,-55 100,0 Z" stroke="#800020" stroke-width="10" fill="none"/>
    <path d="M50,0 C50,27.5 27.5,50 0,50 C-27.5,50 -50,27.5 -50,0 C-50,-27.5 -27.5,-50 0,-50 C27.5,-50 50,-27.5 50,0 Z" stroke="#800020" stroke-width="5" fill="none"/>
  </g>

  <defs>
    <linearGradient id="lotus_gradient" x1="0" y1="0" x2="800" y2="800" gradientUnits="userSpaceOnUse">
      <stop stop-color="#FFD700"/>
      <stop offset="0.5" stop-color="#FF7722"/>
      <stop offset="1" stop-color="#800020"/>
    </linearGradient>
  </defs>
</svg>
