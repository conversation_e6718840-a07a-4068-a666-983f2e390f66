
<svg width="800" height="800" viewBox="0 0 800 800" fill="none" xmlns="http://www.w3.org/2000/svg">
  <circle cx="400" cy="400" r="300" stroke="url(#paint0_linear)" stroke-width="2" fill="none"/>
  <circle cx="400" cy="400" r="250" stroke="url(#paint0_linear)" stroke-width="2" fill="none"/>
  <circle cx="400" cy="400" r="200" stroke="url(#paint0_linear)" stroke-width="2" fill="none"/>
  <circle cx="400" cy="400" r="150" stroke="url(#paint0_linear)" stroke-width="2" fill="none"/>
  <circle cx="400" cy="400" r="100" stroke="url(#paint0_linear)" stroke-width="2" fill="none"/>
  <path d="M400 100 L500 300 L700 400 L500 500 L400 700 L300 500 L100 400 L300 300 Z" stroke="url(#paint0_linear)" stroke-width="2" fill="none"/>
  <path d="M400 150 L480 300 L650 400 L480 500 L400 650 L320 500 L150 400 L320 300 Z" stroke="url(#paint0_linear)" stroke-width="2" fill="none"/>
  <path d="M400 200 L460 300 L600 400 L460 500 L400 600 L340 500 L200 400 L340 300 Z" stroke="url(#paint0_linear)" stroke-width="2" fill="none"/>
  <line x1="100" y1="400" x2="700" y2="400" stroke="url(#paint0_linear)" stroke-width="2"/>
  <line x1="400" y1="100" x2="400" y2="700" stroke="url(#paint0_linear)" stroke-width="2"/>
  <line x1="200" y1="200" x2="600" y2="600" stroke="url(#paint0_linear)" stroke-width="2"/>
  <line x1="600" y1="200" x2="200" y2="600" stroke="url(#paint0_linear)" stroke-width="2"/>
  <defs>
    <linearGradient id="paint0_linear" x1="100" y1="100" x2="700" y2="700" gradientUnits="userSpaceOnUse">
      <stop stop-color="#FFD700" stop-opacity="0.3"/>
      <stop offset="0.5" stop-color="#FF7722" stop-opacity="0.3"/>
      <stop offset="1" stop-color="#800020" stop-opacity="0.3"/>
    </linearGradient>
  </defs>
</svg>
