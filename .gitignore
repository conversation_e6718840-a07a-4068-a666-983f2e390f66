# Dependencies
node_modules
.pnp
.pnp.js

# Testing
coverage

# Production
build
dist
deploy

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Editor directories and files
.DS_Store
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Added by Task Master AI
# Logs
logs
*.log
dev-debug.log
# Dependency directories
node_modules/
# OS specific
# Task files
tasks.json
tasks/ 