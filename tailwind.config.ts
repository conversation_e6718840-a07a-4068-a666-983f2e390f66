import type { Config } from "tailwindcss";

export default {
	darkMode: ["class"],
	content: [
		"./pages/**/*.{ts,tsx}",
		"./components/**/*.{ts,tsx}",
		"./app/**/*.{ts,tsx}",
		"./src/**/*.{ts,tsx}",
	],
	prefix: "",
	theme: {
		container: {
			center: true,
			padding: '2rem',
			screens: {
				'2xl': '1400px'
			}
		},
		extend: {
			colors: {
				border: 'hsl(var(--border))',
				input: 'hsl(var(--input))',
				ring: 'hsl(var(--ring))',
				background: 'hsl(var(--background))',
				foreground: 'hsl(var(--foreground))',
				primary: {
					DEFAULT: 'hsl(var(--primary))',
					foreground: 'hsl(var(--primary-foreground))'
				},
				secondary: {
					DEFAULT: 'hsl(var(--secondary))',
					foreground: 'hsl(var(--secondary-foreground))'
				},
				destructive: {
					DEFAULT: 'hsl(var(--destructive))',
					foreground: 'hsl(var(--destructive-foreground))'
				},
				muted: {
					DEFAULT: 'hsl(var(--muted))',
					foreground: 'hsl(var(--muted-foreground))'
				},
				accent: {
					DEFAULT: 'hsl(var(--accent))',
					foreground: 'hsl(var(--accent-foreground))'
				},
				popover: {
					DEFAULT: 'hsl(var(--popover))',
					foreground: 'hsl(var(--popover-foreground))'
				},
				card: {
					DEFAULT: 'hsl(var(--card))',
					foreground: 'hsl(var(--card-foreground))'
				},
				temple: {
					saffron: "#FF9933", // Brighter saffron
					maroon: "#990033", // Richer maroon
					ivory: "#FFFFF0",
					gold: "#FFC107", // More vibrant gold
					"light-gold": "#FFE082",
					"dark-saffron": "#FF7043",
					"light-maroon": "#C2185B",
					"dark-maroon": "#5D0024",
					purple: "#9C27B0", // Adding purple for spiritual vibrance
					"deep-purple": "#673AB7",
					orange: "#FF5722",
					teal: "#009688",
					cyan: "#00BCD4"
				},
				sidebar: {
					DEFAULT: 'hsl(var(--sidebar-background))',
					foreground: 'hsl(var(--sidebar-foreground))',
					primary: 'hsl(var(--sidebar-primary))',
					'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
					accent: 'hsl(var(--sidebar-accent))',
					'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
					border: 'hsl(var(--sidebar-border))',
					ring: 'hsl(var(--sidebar-ring))'
				}
			},
			borderRadius: {
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)'
			},
			keyframes: {
				"accordion-down": {
					from: { height: "0" },
					to: { height: "var(--radix-accordion-content-height)" },
				},
				"accordion-up": {
					from: { height: "var(--radix-accordion-content-height)" },
					to: { height: "0" },
				},
				"fade-in": {
					"0%": {
						opacity: "0",
						transform: "translateY(10px)"
					},
					"100%": {
						opacity: "1",
						transform: "translateY(0)"
					}
				},
				"fade-out": {
					"0%": {
						opacity: "1",
						transform: "translateY(0)"
					},
					"100%": {
						opacity: "0",
						transform: "translateY(10px)"
					}
				},
				"scale-in": {
					"0%": {
						transform: "scale(0.95)",
						opacity: "0"
					},
					"100%": {
						transform: "scale(1)",
						opacity: "1"
					}
				},
				"scale-out": {
					from: { transform: "scale(1)", opacity: "1" },
					to: { transform: "scale(0.95)", opacity: "0" }
				},
				"slide-in-right": {
					"0%": { transform: "translateX(100%)" },
					"100%": { transform: "translateX(0)" }
				},
				"slide-out-right": {
					"0%": { transform: "translateX(0)" },
					"100%": { transform: "translateX(100%)" }
				},
				"float": {
					"0%, 100%": { transform: "translateY(0)" },
					"50%": { transform: "translateY(-10px)" }
				},
				"spin-slow": {
					"0%": { transform: "rotate(0deg)" },
					"100%": { transform: "rotate(360deg)" }
				},
				"pulse-glow": {
					"0%, 100%": {
						opacity: "1",
						boxShadow: "0 0 10px 2px rgba(255, 193, 7, 0.5)"
					},
					"50%": {
						opacity: "0.8",
						boxShadow: "0 0 20px 5px rgba(255, 193, 7, 0.8)"
					}
				},
				"ping-slow": {
					"0%": { transform: "scale(1)", opacity: "1" },
					"50%": { transform: "scale(1.5)", opacity: "0.5" },
					"100%": { transform: "scale(1)", opacity: "1" }
				},
				"levitate": {
					"0%, 100%": {
						transform: "translateY(0) rotate(0deg)"
					},
					"25%": {
						transform: "translateY(-5px) rotate(1deg)"
					},
					"75%": {
						transform: "translateY(-3px) rotate(-1deg)"
					}
				},
				"magnetic-cursor": {
					"0%": { transform: "scale(1)" },
					"50%": { transform: "scale(1.05)" },
					"100%": { transform: "scale(1)" }
				}
			},
			animation: {
				"accordion-down": "accordion-down 0.2s ease-out",
				"accordion-up": "accordion-up 0.2s ease-out",
				"fade-in": "fade-in 0.3s ease-out",
				"fade-out": "fade-out 0.3s ease-out",
				"scale-in": "scale-in 0.2s ease-out",
				"scale-out": "scale-out 0.2s ease-out",
				"slide-in-right": "slide-in-right 0.3s ease-out",
				"slide-out-right": "slide-out-right 0.3s ease-out",
				"enter": "fade-in 0.3s ease-out, scale-in 0.2s ease-out",
				"exit": "fade-out 0.3s ease-out, scale-out 0.2s ease-out",
				"float": "float 3s ease-in-out infinite",
				"spin-slow": "spin-slow 12s linear infinite",
				"pulse-glow": "pulse-glow 3s ease-in-out infinite",
				"levitate": "levitate 6s ease-in-out infinite",
				"magnetic-cursor": "magnetic-cursor 0.3s ease-in-out",
				"ping-slow": "ping-slow 2s cubic-bezier(0, 0, 0.2, 1) infinite"
			},
			fontFamily: {
				'sanskrit': ['Poppins', 'sans-serif'],
				'heading': ['Playfair Display', 'serif'],
				'sans': ['Montserrat', 'sans-serif'],
				'body': ['Lato', 'sans-serif'],
				'telugu': ['Noto Sans Telugu', 'sans-serif']
			},
			backgroundImage: {
				'mandala-pattern': "url('/images/mandala-bg.svg')",
				'temple-pattern': "linear-gradient(rgba(255, 255, 240, 0.9), rgba(255, 255, 240, 0.9)), url('/images/temple-pattern.svg')",
				'temple-gradient': "linear-gradient(to right, #990033, #FF9933)",
				'gold-gradient': "linear-gradient(to right, #FFC107, #FFE082)",
				'mystic-gradient': "linear-gradient(to right, #9C27B0, #673AB7)",
				'cosmic-gradient': "linear-gradient(135deg, #1A237E, #9C27B0, #FF9933)",
				'divine-gradient': "linear-gradient(to bottom, #FFC107, #FF5722)",
			},
			perspective: {
				'1000': '1000px',
				'none': 'none',
			},
			transformStyle: {
				'3d': 'preserve-3d',
				'flat': 'flat',
			}
		}
	},
	plugins: [
		require("tailwindcss-animate"),
		function({ addUtilities }) {
			const newUtilities = {
				'.perspective-1000': {
					perspective: '1000px',
				},
				'.perspective-none': {
					perspective: 'none',
				},
				'.transform-style-3d': {
					'transform-style': 'preserve-3d',
				},
				'.transform-style-flat': {
					'transform-style': 'flat',
				},
			}
			addUtilities(newUtilities)
		}
	],
} satisfies Config;
