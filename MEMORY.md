# Anantasagareshwari Temple Website - Memory File

## Project Overview

The Anantasagareshwari Temple website features a dynamic content management system built on Next.js with <PERSON><PERSON><PERSON> as the backend. The system allows for real-time content updates while maintaining high performance and security.

## Core Components

### 1. Content Management System
- **Backend**: Supabase with real-time subscriptions
- **Content Structure**: JSON-based with versioning support
- **Admin Interface**: Modern, responsive dashboard at `/admin/dynamic`
- **Storage**: Supabase `website_content` table with JSONB data type

### 2. Authentication System
- Supabase authentication integration
- Role-based access control
- Admin CLI for user management

### 3. File Structure
```
/src/
  /components/admin/
    DynamicContentManager.tsx   # Main admin interface
  /contexts/
    ContentContext.tsx          # Content state management
    SupabaseContext.tsx        # Supabase client context
  /integrations/supabase/
    client.ts                  # Supabase client configuration
    types.ts                   # TypeScript types for database
/scripts/
  init-website-content.js      # Content initialization
  supabase-admin-cli.cjs      # Admin management tools
```

## Content Structure

### Database Schema
```sql
CREATE TABLE website_content (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    data JSONB NOT NULL DEFAULT '{}'::jsonb,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
```

### Content Types
- Site Information (name, tagline, description)
- Contact Details (phone, email, address)
- Hero Section (slides, images)
- Announcements (multilingual)
- Navigation Menus
- About Page Content
- Gallery Items
- Social Media Links
- System Settings

## Features

### Implemented
- ✅ Real-time content editing
- ✅ Content versioning
- ✅ Live preview
- ✅ Multi-language support
- ✅ Role-based access control
- ✅ Content validation
- ✅ Admin dashboard

### In Progress
- 🔄 Image upload management
- 🔄 Content scheduling
- 🔄 Advanced caching
- 🔄 Backup/restore functionality

### Planned
- 📋 Multi-user permissions
- 📋 Advanced analytics
- 📋 Newsletter integration
- 📋 Event booking system
- 📋 E-commerce for donations

## Technical Notes

### Environment Configuration
Required environment variables:
```
VITE_SUPABASE_URL=https://xhtfzsydpihxuzedacsb.supabase.co
VITE_SUPABASE_ANON_KEY=<key>
SUPABASE_SERVICE_ROLE_KEY=<key>
```

### Deployment
The application can be deployed to:
1. Netlify (recommended)
   - Automatic deployments from Git
   - Built-in CI/CD
   - Handles client-side routing

2. cPanel
   - Manual deployment
   - Requires .htaccess configuration
   - Supports traditional hosting

## Development Guidelines

### Content Updates
1. Use ContentContext for state management
2. Implement proper error handling
3. Validate content before updates
4. Maintain version history
5. Use TypeScript interfaces

### Best Practices
- Keep content structure flat when possible
- Implement proper loading states
- Use optimistic updates for better UX
- Maintain proper error boundaries
- Follow TypeScript strict mode

## Known Issues & Solutions

### Authentication
- Issue: Supabase authentication token validation
- Solution: Implemented key validation in initialization scripts

### Performance
- Issue: Large content updates can be slow
- Solution: Implemented partial updates and optimistic UI

## Future Considerations

1. **Scalability**
   - Implement caching strategies
   - Add CDN integration
   - Optimize real-time updates

2. **Security**
   - Regular security audits
   - Rate limiting
   - Input sanitization

3. **User Experience**
   - Enhanced error messaging
   - Better loading states
   - Improved mobile experience

4. **Content Management**
   - Advanced versioning
   - Content scheduling
   - Automated backups

## Maintenance Tasks

- Regular database backups
- Monitor Supabase usage
- Update dependencies
- Security patches
- Performance monitoring

This memory file serves as a living document and should be updated as the project evolves.
