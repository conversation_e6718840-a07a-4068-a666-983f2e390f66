[build]
  publish = "dist"
  command = "npm run build"

# Handle SPA routing by redirecting all requests to index.html
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Headers for all pages
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self'"

# Set cache headers for assets
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

# Set cache headers for images
[[headers]]
  for = "/images/*"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

# Cache control for specific file types
[[headers]]
  for = "*.js"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "*.css"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "*.woff2"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

# Environment variables
[context.production.environment]
  NODE_VERSION = "18.x"

[context.deploy-preview.environment]
  NODE_VERSION = "18.x"

# Build plugins
[[plugins]]
  package = "@netlify/plugin-lighthouse"

  [plugins.inputs]
    output_path = "reports/lighthouse.html"
