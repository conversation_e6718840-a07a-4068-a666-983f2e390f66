# Deployment Guide for Anantasagareshwari Temple Website

This guide provides instructions for deploying the Anantasagareshwari Temple website to both cPanel hosting and Netlify.

## Prerequisites

- Node.js (v16 or higher)
- npm (v7 or higher)

## Building the Application

To build the application for deployment, run:

```bash
npm run build
```

This will create a `dist` directory with all the files needed for deployment.

## Deployment to cPanel

### Option 1: Manual Upload

1. Build the application:
   ```bash
   npm run build
   ```

2. Connect to your cPanel account using FTP or the File Manager.

3. Navigate to the public directory of your hosting (usually `public_html`).

4. Upload all files and folders from the `dist` directory to your public directory.

5. Make sure the `.htaccess` file was uploaded correctly (it might be hidden).

### Option 2: Using the Deployment Script

1. Run the deployment preparation script:
   ```bash
   npm run prepare-deploy
   ```

2. This will create a `deploy/cpanel` directory with all the files ready for cPanel.

3. Upload all files from the `deploy/cpanel` directory to your cPanel hosting.

## Deployment to Netlify

### Option 1: Manual Upload

1. Build the application:
   ```bash
   npm run build:netlify
   ```

2. Log in to your Netlify account.

3. Go to the "Sites" section and drag-and-drop the `dist` folder onto the Netlify dashboard.

4. Wait for the deployment to complete.

### Option 2: Connect to Git Repository

1. Push your code to a Git repository (GitHub, GitLab, or Bitbucket).

2. Log in to your Netlify account.

3. Click "New site from Git" and select your repository.

4. Configure the build settings:
   - Build command: `npm run build`
   - Publish directory: `dist`

5. Click "Deploy site" and wait for the deployment to complete.

### Option 3: Using the Deployment Script

1. Run the deployment preparation script:
   ```bash
   npm run prepare-deploy
   ```

2. This will create a `deploy/netlify` directory with all the files ready for Netlify.

3. Upload the `deploy/netlify` directory to Netlify using the drag-and-drop interface.

## Troubleshooting

### 404 Errors on Page Refresh (cPanel)

If you're experiencing 404 errors when refreshing the page or accessing routes directly, make sure:

1. The `.htaccess` file was uploaded correctly.
2. Your hosting has mod_rewrite enabled.
3. Contact your hosting provider to enable mod_rewrite if needed.

### 404 Errors on Page Refresh (Netlify)

If you're experiencing 404 errors on Netlify:

1. Make sure the `_redirects` file is in the root of your deployed site.
2. Alternatively, make sure the `netlify.toml` file is in the root of your repository.

## Additional Notes

- The application is configured to use client-side routing, which requires special server configuration to handle direct URL access.
- Both cPanel and Netlify deployments include the necessary configuration files to handle client-side routing.
- For cPanel, the `.htaccess` file handles URL rewriting.
- For Netlify, the `_redirects` file and `netlify.toml` handle URL rewriting.
