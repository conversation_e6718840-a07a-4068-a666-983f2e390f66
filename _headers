# Netlify headers file

# Security headers for all pages
/*
  X-Frame-Options: DENY
  X-XSS-Protection: 1; mode=block
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self'

# Cache control for assets
/assets/*
  Cache-Control: public, max-age=31536000, immutable

# Cache control for images
/images/*
  Cache-Control: public, max-age=31536000, immutable

# Cache control for specific file types
*.js
  Cache-Control: public, max-age=31536000, immutable

*.css
  Cache-Control: public, max-age=31536000, immutable

*.woff2
  Cache-Control: public, max-age=31536000, immutable
