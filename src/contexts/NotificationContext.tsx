
import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

interface Notification {
  id: string;
  title: string;
  content: string;
  type: 'event' | 'announcement' | 'general';
  read: boolean;
  data?: any;
  createdAt: Date;
}

interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  addNotification: (notification: Omit<Notification, 'id' | 'createdAt'>) => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const unreadCount = notifications.filter(n => !n.read).length;

  // Fetch notifications from Supabase on mount
  useEffect(() => {
    const fetchNotifications = async () => {
      try {
        const { data, error } = await supabase
          .from('notifications')
          .select('*')
          .order('created_at', { ascending: false })
          .limit(10);

        if (error) {
          console.error('Error fetching notifications:', error);
          return;
        }

        if (data) {
          setNotifications(data.map(n => ({
            id: n.id,
            title: n.title,
            content: n.content,
            type: n.type as 'event' | 'announcement' | 'general',
            read: n.is_read,
            data: n.data,
            createdAt: new Date(n.created_at)
          })));
        }
      } catch (error) {
        console.error('Failed to fetch notifications:', error);
        toast.error("Failed to load notifications. Please try again later.");
      }
    };

    fetchNotifications();

    // Subscribe to new notifications
    const channel = supabase
      .channel('schema-db-changes')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'notifications'
        },
        (payload) => {
          const newNotification = payload.new as any;
          setNotifications(prev => [{
            id: newNotification.id,
            title: newNotification.title,
            content: newNotification.content,
            type: newNotification.type,
            read: newNotification.is_read,
            data: newNotification.data,
            createdAt: new Date(newNotification.created_at)
          }, ...prev]);
          
          // Show toast notification for new notifications
          toast(newNotification.title, {
            description: newNotification.content
          });
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  // Add notification to state (used for client-side notifications)
  const addNotification = async (notification: Omit<Notification, 'id' | 'createdAt'>) => {
    try {
      const { data, error } = await supabase
        .from('notifications')
        .insert({
          title: notification.title,
          content: notification.content,
          type: notification.type,
          is_read: false,
          data: notification.data
        })
        .select()
        .single();
        
      if (error) {
        console.error('Failed to add notification:', error);
        return;
      }
      
      // The notification will be added via the realtime subscription
    } catch (error) {
      console.error('Failed to add notification:', error);
      
      // Fallback if database insert fails - add to local state
      const newNotification: Notification = {
        ...notification,
        id: Math.random().toString(36).substring(2, 11),
        createdAt: new Date(),
        read: false
      };
      setNotifications(prev => [newNotification, ...prev]);
    }
  };

  const markAsRead = async (id: string) => {
    setNotifications(prev => 
      prev.map(n => (n.id === id ? { ...n, read: true } : n))
    );

    // Update in Supabase
    try {
      await supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('id', id);
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
    }
  };

  const markAllAsRead = async () => {
    setNotifications(prev => 
      prev.map(n => ({ ...n, read: true }))
    );

    // Update all in Supabase
    try {
      await supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('is_read', false);
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
    }
  };

  return (
    <NotificationContext.Provider value={{ 
      notifications, 
      unreadCount, 
      markAsRead, 
      markAllAsRead,
      addNotification
    }}>
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};
