import React, { createContext, useContext, useState, useEffect } from 'react';

interface EngagementEvent {
  type: 'announcement_view' | 'announcement_click' | 'popup_view' | 'popup_click' | 'popup_close';
  id: string; // announcement or popup id
  timestamp: number;
  location?: string;
}

interface AnalyticsContextType {
  logEvent: (event: EngagementEvent) => void;
  events: EngagementEvent[];
  locationStats: Record<string, number>;
}

const AnalyticsContext = createContext<AnalyticsContextType | undefined>(undefined);

export const AnalyticsProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [events, setEvents] = useState<EngagementEvent[]>([]);
  const [locationStats, setLocationStats] = useState<Record<string, number>>({});

  useEffect(() => {
    // On mount, try to load from localStorage (for demo)
    const stored = localStorage.getItem('analytics_events');
    if (stored) setEvents(JSON.parse(stored));
  }, []);

  useEffect(() => {
    localStorage.setItem('analytics_events', JSON.stringify(events));
    // Aggregate location stats
    const stats: Record<string, number> = {};
    events.forEach(e => {
      if (e.location) stats[e.location] = (stats[e.location] || 0) + 1;
    });
    setLocationStats(stats);
  }, [events]);

  const logEvent = (event: EngagementEvent) => {
    setEvents(prev => [...prev, event]);
  };

  return (
    <AnalyticsContext.Provider value={{ logEvent, events, locationStats }}>
      {children}
    </AnalyticsContext.Provider>
  );
};

export const useAnalytics = () => {
  const ctx = useContext(AnalyticsContext);
  if (!ctx) throw new Error('useAnalytics must be used within AnalyticsProvider');
  return ctx;
};
