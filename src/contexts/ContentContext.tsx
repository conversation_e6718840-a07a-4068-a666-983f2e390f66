import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { WebsiteContent } from '../types/content';
import contentManager from '@/utils/localContentManager';
import { useToast } from '@/components/ui/use-toast';

export type ContentData = WebsiteContent;

interface ContentContextType {
  content: ContentData | null;
  loading: boolean;
  error: string | null;
  updateContent: (newContent: Partial<ContentData>) => Promise<void>;
  refreshContent: () => Promise<void>;
  createBackup: () => Promise<string>;
  restoreBackup: (timestamp: string) => Promise<void>;
  getBackups: () => Array<{ timestamp: string; content: ContentData }>;
  rollbackContent: (version: number) => Promise<void>;
}

const ContentContext = createContext<ContentContextType | undefined>(undefined);

interface ContentProviderProps {
  children: ReactNode;
}

export const ContentProvider: React.FC<ContentProviderProps> = ({ children }): React.ReactElement => {
  const [content, setContent] = useState<ContentData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  // Initialize content manager and load initial content
  useEffect(() => {
    const loadInitialContent = async () => {
      setLoading(true);
      try {
        const initialContent = await contentManager.loadContent();
        setContent(initialContent);
      } catch (err) {
        setError((err as Error).message);
        toast({
          title: "Error loading content",
          description: "Failed to load website content. Please try again.",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };
    void loadInitialContent();
  }, [toast]);

  const refreshContent = async () => {
    setLoading(true);
    try {
      const freshContent = await contentManager.loadContent();
      setContent(freshContent);
    } catch (err) {
      setError((err as Error).message);
      toast({
        title: "Error refreshing content",
        description: "Failed to refresh website content. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const updateContent = async (newContent: Partial<ContentData>) => {
    if (!content) return;
    try {
      const prevHistory = content._history || [];
      const prevTimestamps = content._historyTimestamps || [];
      const updatedContent = { ...content, ...newContent };
      
      // Keep version history
      updatedContent._history = [content, ...prevHistory].slice(0, 10);
      updatedContent._historyTimestamps = [new Date().toISOString(), ...prevTimestamps].slice(0, 10);
      
      await contentManager.saveContent(updatedContent);
      setContent(updatedContent);
      toast({
        title: "Content updated",
        description: "Website content has been successfully updated.",
      });
    } catch (err) {
      setError((err as Error).message);
      toast({
        title: "Error updating content",
        description: "Failed to update website content. Please try again.",
        variant: "destructive"
      });
    }
  };

  const createBackup = async () => {
    try {
      const timestamp = await contentManager.createBackup();
      toast({
        title: "Backup created",
        description: "Content backup has been created successfully.",
      });
      return timestamp;
    } catch (err) {
      toast({
        title: "Error creating backup",
        description: "Failed to create content backup. Please try again.",
        variant: "destructive"
      });
      throw err;
    }
  };

  const restoreBackup = async (timestamp: string) => {
    try {
      const restoredContent = await contentManager.restoreBackup(timestamp);
      setContent(restoredContent);
      toast({
        title: "Backup restored",
        description: "Content has been restored from backup.",
      });
    } catch (err) {
      toast({
        title: "Error restoring backup",
        description: "Failed to restore content from backup. Please try again.",
        variant: "destructive"
      });
      throw err;
    }
  };

  const getBackups = () => {
    return contentManager.getBackups();
  };

  const rollbackContent = async (version: number): Promise<void> => {
    if (!content || !content._history || version >= content._history.length) {
      throw new Error('Invalid version or no history available');
    }

    try {
      const historicContent = { ...content._history[version] };
      // Update history arrays in the historic content
      historicContent._history = [content, ...(content._history || [])].slice(0, 10);
      historicContent._historyTimestamps = [new Date().toISOString(), ...(content._historyTimestamps || [])].slice(0, 10);
      
      await contentManager.saveContent(historicContent);
      setContent(historicContent);
      toast({
        title: "Content rolled back",
        description: "Content has been rolled back to previous version.",
      });
    } catch (err) {
      toast({
        title: "Error rolling back",
        description: "Failed to roll back content. Please try again.",
        variant: "destructive"
      });
      throw err;
    }
  };

  const value: ContentContextType = {
    content,
    loading,
    error,
    updateContent,
    refreshContent,
    createBackup,
    restoreBackup,
    getBackups,
    rollbackContent
  };

  return (
    <ContentContext.Provider value={value}>
      {children}
    </ContentContext.Provider>
  );
};

export const useContent = (): ContentContextType => {
  const context = useContext(ContentContext);
  if (context === undefined) {
    throw new Error('useContent must be used within a ContentProvider');
  }
  return context;
};
