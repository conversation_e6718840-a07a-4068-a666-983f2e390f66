import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { WebsiteContent } from '../types/content';
import contentManager from '@/utils/localContentManager';
import contentSync from '@/utils/contentSynchronization';
import contentMigration from '@/utils/contentMigration';
import { useToast } from '@/components/ui/use-toast';

export type ContentData = WebsiteContent;

interface ContentContextType {
  content: ContentData | null;
  loading: boolean;
  error: string | null;
  updateContent: (newContent: Partial<ContentData>) => Promise<void>;
  updateSection: (sectionName: string, sectionData: any) => Promise<void>;
  batchUpdate: (updates: Record<string, any>) => Promise<void>;
  refreshContent: () => Promise<void>;
  createBackup: () => Promise<string>;
  restoreBackup: (timestamp: string) => Promise<void>;
  getBackups: () => Array<{ timestamp: string; content: ContentData }>;
  rollbackContent: (version: number) => Promise<void>;
  migrateContent: () => Promise<void>;
  exportContent: () => Promise<string>;
  importContent: (contentJson: string) => Promise<void>;
  syncStatus: {
    isInitialized: boolean;
    lastSync: string | null;
    activeListeners: number;
  };
}

const ContentContext = createContext<ContentContextType | undefined>(undefined);

interface ContentProviderProps {
  children: ReactNode;
}

export const ContentProvider: React.FC<ContentProviderProps> = ({ children }): React.ReactElement => {
  const [content, setContent] = useState<ContentData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  // Initialize content manager, synchronization, and load initial content
  useEffect(() => {
    const initializeContent = async () => {
      setLoading(true);
      try {
        // Initialize synchronization service
        await contentSync.initialize();

        // Check if migration is needed
        const migrationNeeded = await contentMigration.isMigrationNeeded();
        if (migrationNeeded) {
          console.log('Performing content migration...');
          await contentMigration.migrateAllContent();
          toast({
            title: "Content Updated",
            description: "Website content has been migrated to the new system.",
          });
        }

        // Load initial content
        const initialContent = await contentManager.loadContent();
        setContent(initialContent);

        // Subscribe to real-time content changes
        const unsubscribe = contentSync.subscribe('content', (event) => {
          if (event.type === 'update' && event.data) {
            setContent(event.data);
          }
        });

        // Cleanup subscription on unmount
        return () => {
          unsubscribe();
          contentSync.cleanup();
        };

      } catch (err) {
        setError((err as Error).message);
        toast({
          title: "Error loading content",
          description: "Failed to load website content. Please try again.",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    void initializeContent();
  }, [toast]);

  const refreshContent = async () => {
    setLoading(true);
    try {
      const freshContent = await contentManager.loadContent();
      setContent(freshContent);
    } catch (err) {
      setError((err as Error).message);
      toast({
        title: "Error refreshing content",
        description: "Failed to refresh website content. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const updateContent = async (newContent: Partial<ContentData>) => {
    if (!content) return;
    try {
      const prevHistory = content._history || [];
      const prevTimestamps = content._historyTimestamps || [];
      const updatedContent = { ...content, ...newContent };
      
      // Keep version history
      updatedContent._history = [content, ...prevHistory].slice(0, 10);
      updatedContent._historyTimestamps = [new Date().toISOString(), ...prevTimestamps].slice(0, 10);
      
      await contentManager.saveContent(updatedContent);
      setContent(updatedContent);
      toast({
        title: "Content updated",
        description: "Website content has been successfully updated.",
      });
    } catch (err) {
      setError((err as Error).message);
      toast({
        title: "Error updating content",
        description: "Failed to update website content. Please try again.",
        variant: "destructive"
      });
    }
  };

  const createBackup = async () => {
    try {
      const timestamp = await contentManager.createBackup();
      toast({
        title: "Backup created",
        description: "Content backup has been created successfully.",
      });
      return timestamp;
    } catch (err) {
      toast({
        title: "Error creating backup",
        description: "Failed to create content backup. Please try again.",
        variant: "destructive"
      });
      throw err;
    }
  };

  const restoreBackup = async (timestamp: string) => {
    try {
      const restoredContent = await contentManager.restoreBackup(timestamp);
      setContent(restoredContent);
      toast({
        title: "Backup restored",
        description: "Content has been restored from backup.",
      });
    } catch (err) {
      toast({
        title: "Error restoring backup",
        description: "Failed to restore content from backup. Please try again.",
        variant: "destructive"
      });
      throw err;
    }
  };

  const getBackups = () => {
    return contentManager.getBackups();
  };

  const rollbackContent = async (version: number): Promise<void> => {
    if (!content || !content._history || version >= content._history.length) {
      throw new Error('Invalid version or no history available');
    }

    try {
      const historicContent = { ...content._history[version] };
      // Update history arrays in the historic content
      historicContent._history = [content, ...(content._history || [])].slice(0, 10);
      historicContent._historyTimestamps = [new Date().toISOString(), ...(content._historyTimestamps || [])].slice(0, 10);
      
      await contentManager.saveContent(historicContent);
      setContent(historicContent);
      toast({
        title: "Content rolled back",
        description: "Content has been rolled back to previous version.",
      });
    } catch (err) {
      toast({
        title: "Error rolling back",
        description: "Failed to roll back content. Please try again.",
        variant: "destructive"
      });
      throw err;
    }
  };

  // New enhanced methods
  const updateSection = async (sectionName: string, sectionData: any) => {
    try {
      await contentSync.updateSection(sectionName, sectionData, 'frontend');
      toast({
        title: "Section updated",
        description: `${sectionName} section has been updated successfully.`,
      });
    } catch (err) {
      setError((err as Error).message);
      toast({
        title: "Update failed",
        description: `Failed to update ${sectionName} section.`,
        variant: "destructive"
      });
    }
  };

  const batchUpdate = async (updates: Record<string, any>) => {
    try {
      await contentSync.batchUpdate(updates, 'frontend');
      toast({
        title: "Content updated",
        description: "Multiple sections have been updated successfully.",
      });
    } catch (err) {
      setError((err as Error).message);
      toast({
        title: "Batch update failed",
        description: "Failed to update content sections.",
        variant: "destructive"
      });
    }
  };

  const migrateContent = async () => {
    try {
      await contentMigration.migrateAllContent();
      const updatedContent = await contentManager.loadContent();
      setContent(updatedContent);
      toast({
        title: "Migration completed",
        description: "Content has been successfully migrated.",
      });
    } catch (err) {
      setError((err as Error).message);
      toast({
        title: "Migration failed",
        description: "Failed to migrate content.",
        variant: "destructive"
      });
    }
  };

  const exportContent = async (): Promise<string> => {
    try {
      return await contentMigration.exportContent();
    } catch (err) {
      setError((err as Error).message);
      toast({
        title: "Export failed",
        description: "Failed to export content.",
        variant: "destructive"
      });
      throw err;
    }
  };

  const importContent = async (contentJson: string) => {
    try {
      await contentMigration.importContent(contentJson);
      const updatedContent = await contentManager.loadContent();
      setContent(updatedContent);
      toast({
        title: "Import completed",
        description: "Content has been successfully imported.",
      });
    } catch (err) {
      setError((err as Error).message);
      toast({
        title: "Import failed",
        description: "Failed to import content.",
        variant: "destructive"
      });
    }
  };

  const syncStatus = contentSync.getStatus();

  const value: ContentContextType = {
    content,
    loading,
    error,
    updateContent,
    updateSection,
    batchUpdate,
    refreshContent,
    createBackup,
    restoreBackup,
    getBackups,
    rollbackContent,
    migrateContent,
    exportContent,
    importContent,
    syncStatus
  };

  return (
    <ContentContext.Provider value={value}>
      {children}
    </ContentContext.Provider>
  );
};

export const useContent = (): ContentContextType => {
  const context = useContext(ContentContext);
  if (context === undefined) {
    throw new Error('useContent must be used within a ContentProvider');
  }
  return context;
};
