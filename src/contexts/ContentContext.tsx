import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// Types for our content structure
export interface SiteInfo {
  name: string;
  tagline: string;
  description: string;
  logo: string;
  favicon: string;
}

export interface Contact {
  phone: string;
  email: string;
  address: {
    line1: string;
    line2: string;
  };
  coordinates: {
    lat: number;
    lng: number;
  };
  socialMedia: {
    facebook: string;
    instagram: string;
    youtube: string;
    twitter: string;
  };
}

export interface HeroSlide {
  id: number;
  image: string;
  title: string;
  subtitle: string;
  showText: boolean;
}

export interface Announcement {
  id: number;
  text: string;
  priority: 'high' | 'medium' | 'low';
  active: boolean;
  language: 'en' | 'te' | 'hi';
}

export interface AboutSection {
  id: string;
  title: string;
  content: string;
  image: string;
  order: number;
}

export interface About {
  hero: {
    showContent: boolean;
    title: string;
    subtitle: string;
    backgroundImage: string;
  };
  sections: AboutSection[];
}

export interface ContentData {
  siteInfo: SiteInfo;
  contact: Contact;
  hero: {
    slides: HeroSlide[];
  };
  announcements: Announcement[];
  about: About;
}

interface ContentContextType {
  content: ContentData | null;
  loading: boolean;
  error: string | null;
  updateContent: (newContent: Partial<ContentData>) => Promise<void>;
  refreshContent: () => Promise<void>;
}

const ContentContext = createContext<ContentContextType | undefined>(undefined);

interface ContentProviderProps {
  children: ReactNode;
}

export const ContentProvider: React.FC<ContentProviderProps> = ({ children }) => {
  const [content, setContent] = useState<ContentData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadContent = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Try to load from localStorage first (for admin updates)
      const savedContent = localStorage.getItem('siteContent');
      if (savedContent) {
        const parsedContent = JSON.parse(savedContent);
        setContent(parsedContent);
        setLoading(false);
        return;
      }

      // Fallback to default content.json
      const response = await fetch('/data/content.json');
      if (!response.ok) {
        throw new Error('Failed to load content');
      }
      
      const contentData = await response.json();
      setContent(contentData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load content');
      console.error('Error loading content:', err);
    } finally {
      setLoading(false);
    }
  };

  const updateContent = async (newContent: Partial<ContentData>) => {
    try {
      if (!content) return;
      
      const updatedContent = { ...content, ...newContent };
      
      // Save to localStorage
      localStorage.setItem('siteContent', JSON.stringify(updatedContent));
      
      // Update state
      setContent(updatedContent);
      
      // In a real implementation, you would also save to a backend/database
      console.log('Content updated successfully');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update content');
      console.error('Error updating content:', err);
    }
  };

  const refreshContent = async () => {
    // Clear localStorage and reload from default
    localStorage.removeItem('siteContent');
    await loadContent();
  };

  useEffect(() => {
    loadContent();
  }, []);

  const value: ContentContextType = {
    content,
    loading,
    error,
    updateContent,
    refreshContent,
  };

  return (
    <ContentContext.Provider value={value}>
      {children}
    </ContentContext.Provider>
  );
};

export const useContent = (): ContentContextType => {
  const context = useContext(ContentContext);
  if (context === undefined) {
    throw new Error('useContent must be used within a ContentProvider');
  }
  return context;
};
