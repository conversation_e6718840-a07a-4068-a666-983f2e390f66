
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Telugu:wght@300;400;500;600;700&display=swap');
/* Import mobile enhancements */
@import './styles/mobile-enhancements.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 40 70% 98%;
    --foreground: 350 50% 20%;

    --card: 40 100% 99%;
    --card-foreground: 350 50% 20%;

    --popover: 40 100% 99%;
    --popover-foreground: 350 50% 20%;

    --primary: 25 100% 50%;
    --primary-foreground: 40 100% 97%;

    --secondary: 25 80% 95%;
    --secondary-foreground: 350 50% 20%;

    --muted: 40 30% 90%;
    --muted-foreground: 350 35% 45%;

    --accent: 43 100% 65%;
    --accent-foreground: 350 50% 20%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 40 100% 97%;

    --border: 350 30% 80%;
    --input: 40 30% 90%;
    --ring: 25 100% 50%;

    --radius: 0.75rem;

    --sidebar-background: 350 50% 20%;
    --sidebar-foreground: 40 100% 97%;
    --sidebar-primary: 43 100% 65%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 25 100% 50%;
    --sidebar-accent-foreground: 40 100% 97%;
    --sidebar-border: 350 30% 30%;
    --sidebar-ring: 43 100% 65%;
  }

  .dark {
    --background: 350 40% 10%;
    --foreground: 40 30% 90%;

    --card: 350 35% 15%;
    --card-foreground: 40 30% 90%;

    --popover: 350 35% 15%;
    --popover-foreground: 40 30% 90%;

    --primary: 25 100% 60%;
    --primary-foreground: 350 40% 10%;

    --secondary: 350 30% 20%;
    --secondary-foreground: 40 30% 90%;

    --muted: 350 30% 20%;
    --muted-foreground: 40 10% 75%;

    --accent: 43 100% 55%;
    --accent-foreground: 350 40% 10%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 40 100% 97%;

    --border: 350 30% 30%;
    --input: 350 30% 20%;
    --ring: 25 100% 60%;

    --sidebar-background: 350 40% 15%;
    --sidebar-foreground: 40 30% 90%;
    --sidebar-primary: 43 100% 55%;
    --sidebar-primary-foreground: 350 40% 10%;
    --sidebar-accent: 25 100% 60%;
    --sidebar-accent-foreground: 350 40% 10%;
    --sidebar-border: 350 30% 25%;
    --sidebar-ring: 43 100% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-heading tracking-tight;
  }

  /* Special typography */
  .sanskrit-text {
    @apply font-sanskrit;
  }
  
  .telugu-text {
    @apply font-telugu;
  }

  /* Cursor Effects */
  .cursor-effect {
    @apply relative overflow-hidden;
  }
  
  .cursor-effect::after {
    content: '';
    @apply absolute inset-0 opacity-0 bg-white/10 transition-opacity pointer-events-none rounded-md;
  }
  
  .cursor-effect:hover::after {
    @apply opacity-100;
  }

  /* Decorative elements */
  .mandala-bg {
    background-image: radial-gradient(circle at center, 
      rgba(255, 193, 7, 0.3) 0%, 
      rgba(255, 153, 51, 0.2) 25%, 
      rgba(153, 0, 51, 0.1) 50%, 
      transparent 75%);
  }

  .temple-divider {
    height: 4px;
    background: linear-gradient(to right, 
      transparent, 
      theme('colors.temple.gold'), 
      theme('colors.temple.saffron'), 
      theme('colors.temple.maroon'), 
      theme('colors.temple.saffron'), 
      theme('colors.temple.gold'), 
      transparent);
  }

  /* Enhanced animations */
  .hover-float {
    @apply transition-transform duration-300 ease-in-out;
  }
  
  .hover-float:hover {
    @apply transform -translate-y-2 scale-105;
  }
  
  .hover-glow {
    @apply transition-all duration-300 ease-in-out;
  }
  
  .hover-glow:hover {
    @apply brightness-125 shadow-lg shadow-temple-gold/30;
  }
  
  .hover-pulse {
    @apply transition-all duration-300;
  }
  
  .hover-pulse:hover {
    @apply animate-pulse-glow;
  }
  
  .hover-expand {
    @apply transition-transform duration-300;
  }
  
  .hover-expand:hover {
    @apply scale-105;
  }
  
  .hover-border {
    @apply transition-all duration-300 border-transparent;
  }
  
  .hover-border:hover {
    @apply border-temple-gold;
  }

  /* Divine scrollbar */
  ::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-secondary rounded-full;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-temple-gold rounded-full;
    border: 2px solid transparent;
    background-clip: padding-box;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-temple-orange;
  }

  /* Modern UI elements */
  .glass-card {
    @apply backdrop-blur-md bg-white/30 dark:bg-black/30 border border-white/20 dark:border-white/10 shadow-lg;
  }
  
  .text-gradient {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-temple-maroon via-temple-purple to-temple-saffron dark:from-temple-gold dark:to-temple-orange;
  }
  
  .button-gradient {
    background: linear-gradient(135deg, 
      theme('colors.temple.gold') 0%, 
      theme('colors.temple.saffron') 100%);
    @apply text-temple-maroon font-medium hover:shadow-lg transition-all duration-300;
  }
  
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }
  
  /* 3D effects */
  .threed-card {
    @apply transition-all duration-300 transform perspective-1000;
  }
  
  .threed-card:hover {
    transform: perspective(1000px) rotateX(5deg) rotateY(5deg) scale3d(1.05, 1.05, 1.05);
    @apply shadow-xl;
  }
  
  .threed-button {
    @apply transition-all duration-300 transform perspective-1000 relative;
    @apply transform-style-3d;
  }
  
  .threed-button:hover {
    transform: perspective(1000px) translateZ(10px);
    @apply shadow-lg;
  }
  
  .threed-button::before {
    content: '';
    @apply absolute inset-0 -z-10 opacity-0 transition-opacity duration-300 rounded-md;
    background: linear-gradient(135deg, theme('colors.temple.gold'), theme('colors.temple.saffron'));
    transform: translateZ(-10px);
  }
  
  .threed-button:hover::before {
    @apply opacity-70;
  }
}

@layer components {
  .temple-card {
    @apply rounded-lg overflow-hidden bg-card shadow-md hover:shadow-xl transition-all duration-300 hover:scale-105;
  }
  
  .temple-button {
    @apply px-6 py-2 rounded-full text-sm font-medium transition-all duration-300 
           bg-primary text-primary-foreground hover:bg-primary/80 hover:shadow-md hover:-translate-y-1
           focus:outline-none focus:ring-2 focus:ring-accent focus:ring-offset-2;
  }

  .temple-link {
    @apply text-primary hover:text-accent font-medium transition-colors duration-300 relative;
    @apply after:absolute after:bottom-0 after:left-0 after:w-0 after:h-0.5 after:bg-accent 
           after:transition-all after:duration-300 hover:after:w-full;
  }
  
  /* Modern card styles */
  .premium-card {
    @apply rounded-xl p-6 transition-all duration-300 hover:shadow-xl hover:shadow-temple-gold/20 hover:-translate-y-1;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.1);
  }
  
  .premium-button {
    @apply px-8 py-3 rounded-full font-medium text-white transition-all duration-300
           shadow-md hover:shadow-xl transform hover:-translate-y-1 
           focus:outline-none focus:ring-2 focus:ring-offset-2;
    background: linear-gradient(135deg, 
      theme('colors.temple.maroon') 0%, 
      theme('colors.temple.saffron') 100%);
  }
  
  .admin-nav-item {
    @apply flex items-center gap-3 px-4 py-2 rounded-lg transition-colors
           hover:bg-white/10 text-white/80 hover:text-white;
  }
  
  .admin-nav-item.active {
    @apply bg-white/15 text-white;
  }

  /* 3D elements */
  .meditation-orb {
    @apply rounded-full bg-gradient-to-br from-temple-gold via-temple-orange to-temple-maroon;
    @apply shadow-lg shadow-temple-gold/30 animate-pulse-glow;
  }
  
  .spiritual-card {
    @apply rounded-xl overflow-hidden relative;
    @apply before:absolute before:inset-0 before:bg-gradient-to-br before:from-temple-purple/30 before:to-transparent before:z-10;
  }
}
