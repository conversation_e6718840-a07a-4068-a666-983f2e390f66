
import { useState, useRef, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Music, Pause } from "lucide-react";
import { cn } from "@/lib/utils";
import { HoverCard, HoverCardContent, HoverCardTrigger } from "@/components/ui/hover-card";
import { useNotifications } from '@/contexts/NotificationContext';
import { toast } from '@/components/ui/use-toast';

// Use the actual audio file from the public folder
const DEVOTIONAL_MUSIC_URL = "/audio/AS_Audio.mp3";

export function MusicPlayer() {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const [visualizerData, setVisualizerData] = useState<number[]>(Array(10).fill(0));
  const { addNotification } = useNotifications();

  useEffect(() => {
    // Create audio element
    audioRef.current = new Audio(DEVOTIONAL_MUSIC_URL);
    audioRef.current.loop = true;
    audioRef.current.volume = 0.3;

    // Set autoplay attribute
    audioRef.current.autoplay = true;

    // Set up event listeners
    audioRef.current.addEventListener('canplaythrough', () => {
      setIsLoaded(true);
      // Force auto-play when loaded
      forcePlayAudio();
    });

    // Handle autoplay policy restrictions
    audioRef.current.addEventListener('play', () => {
      setIsPlaying(true);
    });

    audioRef.current.addEventListener('pause', () => {
      // Only update state if it wasn't triggered by our own pause method
      if (isPlaying) {
        setIsPlaying(false);
      }
    });

    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.src = '';
      }
    };
  }, []);

  // Force play audio - used for initial autoplay
  const forcePlayAudio = () => {
    if (!audioRef.current || !isLoaded) return;

    // Try to play and handle any autoplay policy restrictions
    audioRef.current.play().then(() => {
      setIsPlaying(true);

      // Add notification when music starts playing
      addNotification({
        id: Date.now().toString(),
        title: "Temple Music Playing",
        content: "Divine temple music is now playing. Click the music icon to control playback.",
        type: "music",
        data: { music_id: "temple-music" },
        read: false,
        createdAt: new Date()
      });

      toast({
        title: "Temple Music",
        description: "Divine temple music is now playing",
        duration: 3000,
      });
    }).catch(error => {
      console.error("Autoplay failed:", error);
      // We'll show a visual indicator that music needs to be manually started
      setIsPlaying(false);
    });
  };

  useEffect(() => {
    if (!isPlaying) {
      return;
    }

    // Simple audio visualizer simulation
    const interval = setInterval(() => {
      const newData = Array(10).fill(0).map(() => Math.random() * 0.8 + 0.2);
      setVisualizerData(newData);
    }, 200);

    return () => clearInterval(interval);
  }, [isPlaying]);

  const playAudio = () => {
    if (!audioRef.current || !isLoaded) return;

    // Use the same approach as forcePlayAudio
    audioRef.current.play().then(() => {
      setIsPlaying(true);

      // Add notification when music starts playing
      addNotification({
        id: Date.now().toString(),
        title: "Temple Music Playing",
        content: "Divine temple music is now playing. Click the music icon to control playback.",
        type: "music",
        data: { music_id: "temple-music" },
        read: false,
        createdAt: new Date()
      });

      toast({
        title: "Temple Music",
        description: "Divine temple music is now playing",
        duration: 3000,
      });
    }).catch(error => {
      console.error("Audio playback failed:", error);
      setIsPlaying(false);
    });
  };

  const pauseAudio = () => {
    if (!audioRef.current || !isLoaded) return;

    audioRef.current.pause();
    setIsPlaying(false);

    toast({
      title: "Temple Music Paused",
      description: "Temple music has been paused",
      duration: 3000,
    });
  };

  const togglePlay = () => {
    if (isPlaying) {
      pauseAudio();
    } else {
      playAudio();
    }
  };

  return (
    <HoverCard>
      <HoverCardTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          onClick={togglePlay}
          disabled={!isLoaded}
          className={cn(
            "rounded-full w-9 h-9 relative overflow-hidden transition-all duration-300",
            isPlaying
              ? "bg-temple-gold/30 ring-2 ring-temple-gold/50 animate-pulse after:absolute after:inset-0 after:animate-ping after:rounded-full after:bg-temple-saffron/30 after:opacity-75 shadow-[0_0_10px_2px_rgba(255,193,7,0.5)]"
              : "hover:bg-temple-gold/20 animate-pulse"
          )}
          aria-label={isPlaying ? "Pause devotional music" : "Play devotional music"}
        >
          <div className="absolute -top-1 -right-1 flex items-center justify-center">
            <span className={cn(
              "absolute w-3 h-3 rounded-full bg-red-500",
              isPlaying ? "opacity-100" : "hidden"
            )}></span>
            <span className={cn(
              "absolute w-3 h-3 rounded-full bg-red-500",
              isPlaying ? "animate-ping opacity-75" : "hidden"
            )}></span>
          </div>
          {isPlaying ? (
            <Pause className="h-5 w-5 text-temple-gold animate-pulse" />
          ) : (
            <Music className="h-5 w-5 text-temple-maroon dark:text-temple-gold animate-pulse" />
          )}

          {isPlaying && (
            <div className="absolute inset-x-0 bottom-0 h-1 flex items-end justify-around">
              {visualizerData.map((value, index) => (
                <div
                  key={index}
                  className="w-0.5 mx-px bg-temple-gold"
                  style={{
                    height: `${value * 100}%`,
                    transform: 'scaleY(1)',
                    transformOrigin: 'bottom',
                    transition: 'transform 0.2s ease-out'
                  }}
                />
              ))}
            </div>
          )}
        </Button>
      </HoverCardTrigger>

      <HoverCardContent
        className="w-64 glass-card border-temple-gold/20"
        side="bottom"
      >
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Temple Music</h4>
          <p className="text-xs text-muted-foreground">
            {isPlaying
              ? "Currently playing divine temple music. Click to pause."
              : "Click to play divine temple music for a spiritual experience."}
          </p>
          <div className="pt-2">
            <span className="text-xs telugu-text">
              దేవాలయ సంగీతం - {isPlaying ? "ప్రస్తుతం ప్లే అవుతోంది" : "ప్లే చేయడానికి క్లిక్ చేయండి"}
            </span>
          </div>
          <div className="pt-2 flex justify-center">
            <Button
              size="sm"
              onClick={togglePlay}
              className={cn(
                "w-full transition-all",
                isPlaying
                  ? "bg-temple-maroon hover:bg-temple-dark-maroon text-white"
                  : "bg-temple-gradient text-white"
              )}
            >
              {isPlaying ? "Pause Music" : "Play Music"}
            </Button>
          </div>
        </div>
      </HoverCardContent>
    </HoverCard>
  );
}
