import React from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { X } from "lucide-react";

interface StotramModalProps {
  children: React.ReactNode;
}

export const StotramModal: React.FC<StotramModalProps> = ({ children }) => {
  const fullStotram = `శ్రీసరస్వతీ స్తోత్రమ్

అయివిధి వాదిని! వాదన మోదిని! మోదవిధాయిని! ధాతృసుతే! సురనర వానర చేతన చేతని! సూక్తిరసాయన సూక్తనుతే! పరమ పదాస్పద వాక్యపదీయ విభూతివిధాయన వాక్సుకృతే! జయ జయహే పరమేశ్వరి! భారతి! బ్రహ్మపురస్కృతి వాగ్దయితే!

పరిపరి యాచిత దైవతహర్షిణి! దైత్యనిరోషిణి! ధర్మరతే! కణకణకంకణకింకిణికాంకిణి! వైణికతోషిణి! వాద్యరతే! కిణికిణి రూపిణి! కీచకశోషిణి! సుస్వరపోషిణి! కీర్తిరతే! జయ జయహే పరమేశ్వరి! భారతి! బ్రహ్మపురస్కృతి వాగ్దయితే!

అయి జగదంబ! సదంబుజడంబరచుంబినిజావయ వాధిగతే! సతతనితంబవిలంబితకేశకదంబ! కదంబిని! కామ్యకృతే! గుణనికురుంబ! విరించికుటుంబిని! మోదకదంబిని! సాంబనుతే! జయ జయహే పరమేశ్వరి! భారతి! బ్రహ్మపురస్కృతి వాగ్దయితే!

విరచిత పుష్ప గుళుచ్ఛ లసచ్ఛర దచ్ఛ మనోహరణోచ్చపదే! విలసిత కచ్ఛపికచ్ఛవిమచ్ఛృతి సత్ప్రవణాదృత సచ్ఛవణే! ప్రణవ మనూదిత చిచ్ఛతి మిచ్ఛత మిచ్చిత ముచ్ఛితకల్పలతే! జయ జయహే పరమేశ్వరి! భారతి! బ్రహ్మపురస్కృతి వాగ్దయితే!

అయి పరిరక్షిత సర్వ జగత్ క్షితి సంస్థితి! చిత్త్రిృతి రూపధృతే! మధు మధురోద్ధర మాధ్యమికోద్దుర వాగ్ధుని మధ్యగతే! సుగతే! తథిత తదిద్ధిమి దిద్ధిమి దిద్ధిమి తీర్ధపదోద్ధత నాట్యరతే! జయ జయహే పరమేశ్వరి! భారతి! బ్రహ్మపురస్కృతి వాగ్దయితే!

కటితటితాచ్ఛపటీరపటార్భటిపాటిత సర్వతమో ఘటితే! నిటల తటీ ఘటితేందు విలాస విలాసిత కుంకుమ పంక ధృతే! పటు పట హాద్భుత వాదన నాద వినోదవినోదిత వాద్య హితే! జయ జయహే పరమేశ్వరి! భారతి! బ్రహ్మపురస్కృతి వాగ్దయితే!

అయికరుణా వరణా వరణా భరణారుణి తాభయహస్తయుతే! పరమ సుధీ ప్రథితాద్భుత వైద్యుత ధీపరివర్ధన పుస్తయుతే! నిజమహిమాన హిమాని హిమానిత మానిత తాపసతాపమితే! జయ జయహే పరమేశ్వరి! భారతి! బ్రహ్మపురస్కృతి వాగ్దయితే!

సురథ మరాళ మరాళ గళే లులితాబ్జ బిసాళి కిసాళివృతే! శుక పిక సుస్వర నిస్వన నిస్స్వన నిశ్వసితాశ్వసితాస్యయుతే! విజిత సహస్రకరైక సహస్ర కరైందవ బైందవ బృందగతే! జయ జయహే పరమేశ్వరి! భారతి! బ్రహ్మపురస్కృతి వాగ్దయితే!

అయి జననీ! జననీతివినీతి పునీత జనీతి విధూతిమతే! పరిణత శారద సారదరీదృశ సార పరీవృత పారమితే! తవ చరణం శరణం కరుణా స్ఫురణం మమతారణ మస్ఖలనం జయ జయహే పరమేశ్వరి! భారతి! బ్రహ్మపురస్కృతి వాగ్దయితే!

సరస సరస్వతి! సౌమ్యమహస్వతి! సాధువచస్వతి భూతికృతే! నిరతిశయాస్పద ఋక్వతి! యాజుష సామవతీ! మహతీ! సుకృతే! సమరస భావ మనస్విని! భవ్యయశస్విని! మంత్రమనో2 ధిగతే! జయ జయహే పరమేశ్వరి! భారతి! బ్రహ్మపురస్కృతి వాగ్దయితే!

స్తిమిత మానస తామస రోధినా! స్తుతి మిమా మనుపాఠసమాధినా పఠతి చే ద్యది కిం నలభేత్సుఖం। నిరుపమాన కృపామయి వాణినా! అష్టకాల నరసింహరామ శర్మాభిధాన కృతిరాత్మశోధనీ! వాగ్విధాన వర మాదరా దలం। వాణినా సదయయాగ్రహీత్విదమ్`;

  return (
    <Dialog>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh] bg-gradient-to-br from-temple-ivory to-white dark:from-temple-dark-maroon dark:to-temple-maroon border-2 border-temple-gold">
        <DialogHeader className="border-b border-temple-gold/30 pb-4">
          <DialogTitle className="text-2xl font-heading text-temple-maroon dark:text-temple-gold text-center">
            శ్రీసరస్వతీ స్తోత్రమ్
          </DialogTitle>
        </DialogHeader>
        <ScrollArea className="h-[60vh] pr-4">
          <div className="space-y-4 p-4">
            <div className="text-center mb-6">
              <div className="w-16 h-1 bg-temple-gold mx-auto mb-4"></div>
              <p className="text-sm text-temple-maroon/70 dark:text-temple-ivory/70">
                Complete Saraswati Stotram
              </p>
            </div>
            <div className="prose prose-lg max-w-none">
              <div className="text-temple-maroon dark:text-temple-ivory leading-relaxed whitespace-pre-line font-serif text-base sm:text-lg">
                {fullStotram}
              </div>
            </div>
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
};
