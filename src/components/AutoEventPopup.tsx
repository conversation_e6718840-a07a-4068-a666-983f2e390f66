import { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { useNotifications } from '@/contexts/NotificationContext';

interface AutoEventPopupProps {
  onClose?: () => void;
}

export function AutoEventPopup({ onClose }: AutoEventPopupProps) {
  const [open, setOpen] = useState(false);
  const { addNotification } = useNotifications();

  useEffect(() => {
    // Show popup after a short delay when component mounts
    const timer = setTimeout(() => {
      setOpen(true);

      // Add a notification for this event
      addNotification({
        title: "Sri Saraswati Yagnam - 25 May 2025",
        content: "Sri Saraswati Yagnam on 25 May 2025 at Anantasagar Kshetramu",
        type: "event",
        data: { event_id: "may-25-2025" },
        read: false
      });
    }, 1500);

    return () => clearTimeout(timer);
  }, [addNotification]);

  const handleClose = () => {
    setOpen(false);
    if (onClose) onClose();
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="w-[95vw] max-w-[95vw] sm:max-w-3xl max-h-[90vh] overflow-y-auto p-4 sm:p-6 rounded-xl" closeButton={false}>
        <DialogHeader className="relative">
          <Button
            variant="ghost"
            size="icon"
            className="absolute right-2 top-2 rounded-full z-10 hover:bg-temple-gold/20 transition-colors"
            onClick={handleClose}
          >
            <X className="h-5 w-5" />
          </Button>
          <DialogTitle className="text-center text-xl sm:text-2xl font-bold text-temple-maroon dark:text-temple-gold mb-2 pr-6">
            Sri Saraswati Yagnam - 25 May 2025
          </DialogTitle>
        </DialogHeader>

        <div className="flex flex-col items-center">
          <div className="rounded-md overflow-hidden mb-6 w-full flex justify-center items-center">
            <img
              src="/images/events/25-5-2025.jpeg"
              alt="Sri Saraswati Yagnam - 25 May 2025"
              className="w-full h-auto object-contain max-h-[50vh] sm:max-h-[60vh] md:max-h-[70vh]"
              style={{
                objectFit: 'contain',
                maxWidth: '100%'
              }}
            />
          </div>
        </div>

        <DialogFooter className="mt-2 sm:mt-4">
          <Button
            className="w-full bg-temple-maroon hover:bg-temple-dark-maroon text-white py-2 sm:py-3 text-base
            hover:shadow-md transition-all transform hover:scale-[1.02] duration-200 rounded-full"
            onClick={handleClose}
          >
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
