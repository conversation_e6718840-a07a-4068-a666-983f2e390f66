
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON> } from "lucide-react";
import { <PERSON> } from "react-router-dom";

export function TempleIntro() {
  return (
    <div className="py-12 overflow-hidden relative">
      {/* Background mandala decoration */}
      <div className="absolute top-0 right-0 -translate-y-1/2 translate-x-1/2 opacity-10">
        <div className="w-[400px] h-[400px] mandala-bg"></div>
      </div>

      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row items-center gap-8 md:gap-12">
          {/* Temple image */}
          <div className="w-full md:w-1/2 mb-8 md:mb-0">
            <div className="relative">
              <div className="absolute -left-4 -top-4 w-24 h-24 border-t-4 border-l-4 border-temple-gold opacity-60"></div>
              <div className="absolute -right-4 -bottom-4 w-24 h-24 border-b-4 border-r-4 border-temple-gold opacity-60"></div>
              <div className="relative group">
                <video
                  src="/images/temple_video.mp4"
                  poster="/images/AS_Overview.jpg"
                  className="w-full h-auto rounded-lg object-cover shadow-lg cursor-pointer"
                  onClick={(e) => {
                    const video = e.target as HTMLVideoElement;
                    if (video.paused) {
                      video.play();
                    } else {
                      video.pause();
                    }
                  }}
                  muted
                  loop
                />
                <div className="absolute inset-0 flex items-center justify-center bg-black/30 group-hover:bg-black/10 transition-all duration-300 rounded-lg">
                  <div className="w-16 h-16 bg-temple-gold/80 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-white">
                      <polygon points="5 3 19 12 5 21 5 3"></polygon>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Text content */}
          <div className="w-full md:w-1/2">
            <h2 className="text-3xl font-heading text-temple-maroon dark:text-temple-gold mb-4">
              Welcome to Anantasagar Kshetramu
            </h2>

            <div className="temple-divider mb-6 w-24"></div>

            <p className="mb-4 text-foreground">
              Anantasagar Kshetramu is a sacred sanctuary dedicated to Goddess
              Anantasagareshwari, an embodiment of divine feminine energy and infinite cosmic power.
            </p>

            <p className="mb-6 text-foreground">
              Our temple serves as a spiritual haven where devotees come together to worship,
              meditate, and find inner peace. We are committed to preserving and sharing the rich
              cultural and spiritual heritage of Hinduism through traditional rituals, community
              service, and educational programs.
            </p>

            <div className="flex flex-wrap gap-4">
              <Button
                asChild
                className="bg-temple-maroon hover:bg-temple-dark-maroon text-temple-ivory"
              >
                <Link to="/about">
                  Learn More About Temple
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>

              <Button
                asChild
                variant="outline"
                className="border-temple-maroon/20 hover:border-temple-maroon dark:border-temple-gold/20 dark:hover:border-temple-gold"
              >
                <Link to="/contact">
                  Contact Us
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
