
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import { Link } from "react-router-dom";

// Temple services from the old website
const services = [
  {
    id: 1,
    title: "<PERSON><PERSON><PERSON>",
    description: "Daily worship rituals performed by our temple priests to invoke divine blessings.",
    icon: "🕯️",
  },
  {
    id: 2,
    title: "Homam & Yagnam",
    description: "Sacred fire ceremonies performed for specific purposes and spiritual benefits.",
    icon: "🔥",
  },
  {
    id: 3,
    title: "Abhis<PERSON><PERSON><PERSON>",
    description: "Ritual bathing of the deity with sacred substances to invoke divine energy.",
    icon: "💧",
  },
];

export function TempleServices() {
  return (
    <div className="py-12 bg-secondary/50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-10">
          <h2 className="text-3xl font-heading text-temple-maroon dark:text-temple-gold mb-4">
            Temple Services
          </h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Experience divine blessings through our traditional services and religious ceremonies
            performed by experienced priests.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {services.map((service) => (
            <Card
              key={service.id}
              className="hover-glow border-temple-gold/20 hover:border-temple-gold/50 dark:border-temple-gold/20 dark:hover:border-temple-gold/50"
            >
              <CardHeader className="pb-2">
                <div className="text-4xl mb-2">{service.icon}</div>
                <CardTitle className="font-heading text-xl text-temple-maroon dark:text-temple-gold">
                  {service.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-muted-foreground">
                  {service.description}
                </CardDescription>
              </CardContent>
              <CardFooter>
                <Button asChild variant="link" className="p-0 text-temple-saffron dark:text-temple-gold">
                  <Link to="/services" className="flex items-center">
                    Learn More <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>

        <div className="text-center mt-10">
          <Button
            asChild
            className="bg-temple-maroon hover:bg-temple-dark-maroon text-temple-ivory"
          >
            <Link to="/services">View All Services</Link>
          </Button>
        </div>
      </div>
    </div>
  );
}
