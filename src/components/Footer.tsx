
import { Link } from "react-router-dom";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Facebook, Instagram, Youtube, Home, Mail, Phone, Twitter, Linkedin, MapPin } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import { Separator } from "@/components/ui/separator";
import { QRCode } from "@/components/QRCode";

export function Footer() {
  const [email, setEmail] = useState("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!email || !/\S+@\S+\.\S+/.test(email)) {
      toast("Invalid email", {
        description: "Please enter a valid email address",
      });
      return;
    }

    // In a real implementation, this would send the email to a server
    toast("Success!", {
      description: "You've been subscribed to our newsletter",
    });
    setEmail("");
  };

  return (
    <footer className="bg-temple-maroon text-temple-ivory">
      <div className="temple-divider"></div>

      <div className="container mx-auto px-4 sm:px-6 py-8 sm:py-12">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6 sm:gap-8 lg:gap-6">
          {/* About */}
          <div className="space-y-3 sm:space-y-4">
            <div className="mb-2 sm:mb-4">
              <img
                src="/images/logo.png"
                alt="Anantasagar Kshetramu Logo"
                className="h-12 sm:h-16 w-auto object-contain"
              />
            </div>

            <p className="text-sm">
              Devoted to spiritual growth, community service, and preserving Hindu traditions.
            </p>

            <div className="flex space-x-6 pt-2">
              <a href="https://facebook.com" className="hover-float text-temple-gold hover:text-temple-saffron transition-colors" target="_blank" rel="noreferrer">
                <Facebook className="h-6 w-6" />
                <span className="sr-only">Facebook</span>
              </a>
              <a href="https://instagram.com" className="hover-float text-temple-gold hover:text-temple-saffron transition-colors" target="_blank" rel="noreferrer">
                <Instagram className="h-6 w-6" />
                <span className="sr-only">Instagram</span>
              </a>
              <a href="https://youtube.com" className="hover-float text-temple-gold hover:text-temple-saffron transition-colors" target="_blank" rel="noreferrer">
                <Youtube className="h-6 w-6" />
                <span className="sr-only">YouTube</span>
              </a>
              <a href="https://twitter.com" className="hover-float text-temple-gold hover:text-temple-saffron transition-colors" target="_blank" rel="noreferrer">
                <Twitter className="h-6 w-6" />
                <span className="sr-only">Twitter</span>
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-3 sm:space-y-4">
            <h3 className="font-heading text-temple-gold text-lg sm:text-xl mb-2 sm:mb-4">Quick Links</h3>
            <div className="grid grid-cols-2 sm:grid-cols-1 gap-x-4 gap-y-2 sm:gap-y-3">
              <div>
                <Link to="/about" className="text-sm flex items-center gap-2 hover-float hover:text-temple-gold transition-colors">
                  <span className="w-1 h-1 bg-temple-gold rounded-full"></span>
                  About Temple
                </Link>
              </div>
              <div>
                <Link to="/founders" className="text-sm flex items-center gap-2 hover-float hover:text-temple-gold transition-colors">
                  <span className="w-1 h-1 bg-temple-gold rounded-full"></span>
                  Founders
                </Link>
              </div>
              <div>
                <Link to="/services" className="text-sm flex items-center gap-2 hover-float hover:text-temple-gold transition-colors">
                  <span className="w-1 h-1 bg-temple-gold rounded-full"></span>
                  Puja Services
                </Link>
              </div>
              <div>
                <Link to="/events" className="text-sm flex items-center gap-2 hover-float hover:text-temple-gold transition-colors">
                  <span className="w-1 h-1 bg-temple-gold rounded-full"></span>
                  Upcoming Events
                </Link>
              </div>
              <div>
                <Link to="/gallery" className="text-sm flex items-center gap-2 hover-float hover:text-temple-gold transition-colors">
                  <span className="w-1 h-1 bg-temple-gold rounded-full"></span>
                  Temple Gallery
                </Link>
              </div>
              <div>
                <Link to="/contact" className="text-sm flex items-center gap-2 hover-float hover:text-temple-gold transition-colors">
                  <span className="w-1 h-1 bg-temple-gold rounded-full"></span>
                  Contact Us
                </Link>
              </div>
            </div>
          </div>

          {/* Temple Timing */}
          <div className="space-y-3 sm:space-y-4">
            <h3 className="font-heading text-temple-gold text-lg sm:text-xl mb-2 sm:mb-4">Temple Timings</h3>
            <div className="space-y-2 sm:space-y-3">
              <div className="flex justify-between text-sm">
                <span className="text-temple-ivory/80">Morning Aarti:</span>
                <span className="font-medium">5:30 AM - 6:30 AM</span>
              </div>
              <Separator className="bg-temple-gold/20" />
              <div className="flex justify-between text-sm">
                <span className="text-temple-ivory/80">Temple Opening:</span>
                <span className="font-medium">5:30 AM</span>
              </div>
              <Separator className="bg-temple-gold/20" />
              <div className="flex justify-between text-sm">
                <span className="text-temple-ivory/80">Temple Closing:</span>
                <span className="font-medium">9:00 PM</span>
              </div>
              <Separator className="bg-temple-gold/20" />
              <div className="flex justify-between text-sm">
                <span className="text-temple-ivory/80">Evening Aarti:</span>
                <span className="font-medium">7:00 PM - 8:00 PM</span>
              </div>
              <Separator className="bg-temple-gold/20" />
              <div className="flex justify-between font-medium text-temple-gold mt-2">
                <span>Special Puja:</span>
                <Link to="/events" className="underline underline-offset-2">Check Events</Link>
              </div>
            </div>
          </div>

          {/* Newsletter */}
          <div className="space-y-3 sm:space-y-4">
            <h3 className="font-heading text-temple-gold text-lg sm:text-xl mb-2 sm:mb-4">Newsletter</h3>
            <p className="text-sm">
              Subscribe to receive updates on upcoming events and temple activities.
            </p>
            <form onSubmit={handleSubmit} className="space-y-2 sm:space-y-3 mt-3 sm:mt-4">
              <div className="sm:flex sm:space-x-2">
                <Input
                  type="email"
                  placeholder="Your email address"
                  className="bg-temple-maroon/50 border-temple-gold/30 text-temple-ivory placeholder:text-temple-ivory/60 focus:border-temple-gold mb-2 sm:mb-0"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
                <Button
                  type="submit"
                  className="w-full sm:w-auto bg-temple-gold hover:bg-temple-saffron text-temple-maroon font-medium">
                  Subscribe
                </Button>
              </div>
              <p className="text-xs text-temple-ivory/60">
                We respect your privacy and will never share your information.
              </p>
            </form>
          </div>

          {/* QR Code for Location */}
          <div className="space-y-3 sm:space-y-4 flex flex-col items-center lg:items-start">
            <h3 className="font-heading text-temple-gold text-lg sm:text-xl mb-2 sm:mb-4">Find Us</h3>
            <div className="text-center lg:text-left">
              <p className="text-sm mb-3">
                Scan QR code for directions
              </p>
              <QRCode
                value="https://maps.google.com/?q=18.2058612,78.9885657"
                size={96}
                className="mx-auto lg:mx-0"
              />
              <div className="flex items-center justify-center lg:justify-start gap-2 mt-2">
                <MapPin className="h-4 w-4 text-temple-gold" />
                <span className="text-xs text-temple-ivory/80">Temple Location</span>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-8 sm:mt-12 pt-6 sm:pt-8 border-t border-temple-gold/30">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
            <div className="space-y-3 sm:space-y-0 sm:flex items-center flex-wrap gap-3 sm:gap-4 text-xs sm:text-sm text-temple-ivory/70">
              <div className="flex items-center gap-2">
                <Home className="h-4 w-4 min-w-4 text-temple-gold" />
                <span className="text-temple-ivory/90">Anantasagar Rajiv Rahadari Highway, Siddipet, India</span>
              </div>
              <div className="flex items-center gap-2">
                <Phone className="h-4 w-4 min-w-4 text-temple-gold" />
                <a href="tel:+************" className="hover:text-temple-gold transition-colors text-temple-ivory/90 underline-offset-2 hover:underline">+91 82477 21046</a>
              </div>
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4 min-w-4 text-temple-gold" />
                <a href="mailto:<EMAIL>" className="hover:text-temple-gold transition-colors text-temple-ivory/90 underline-offset-2 hover:underline"><EMAIL></a>
              </div>
            </div>
            <div className="text-xs sm:text-sm text-temple-ivory/70 sm:text-right">
              © {new Date().getFullYear()} Anantasagar Kshetramu. All rights reserved.
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
