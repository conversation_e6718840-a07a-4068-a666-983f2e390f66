
import { useState, useEffect, useRef } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { PlayCircle, PauseCircle, ChevronUp, ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface Announcement {
  id: string;
  content: string;
}

export function DynamicAnnouncements() {
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isPaused, setIsPaused] = useState(false);
  const [speed, setSpeed] = useState(0.4);
  const [animationClass, setAnimationClass] = useState("animate-marquee-very-slow");

  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // Update animation class when speed changes
  useEffect(() => {
    if (speed <= 0.3) setAnimationClass("animate-marquee-extremely-slow");
    else if (speed > 0.3 && speed <= 0.5) setAnimationClass("animate-marquee-very-slow");
    else if (speed > 0.5 && speed <= 0.7) setAnimationClass("animate-marquee-slow");
    else if (speed > 0.7 && speed <= 0.9) setAnimationClass("animate-marquee-moderate");
    else if (speed > 0.9 && speed <= 1.2) setAnimationClass("animate-marquee");
    else if (speed > 1.2 && speed <= 1.5) setAnimationClass("animate-marquee-fast");
    else if (speed > 1.5) setAnimationClass("animate-marquee-very-fast");
  }, [speed]);

  useEffect(() => {
    // Add hardcoded multilingual announcements for the upcoming yagnam with decorative elements
    const yagnamAnnouncementEnglish = {
      id: "yagnam-2025-english",
      content: "🌸 504th Sri Saraswati Yagnam - 25-05-2025 Sunday 🌸 All are welcome! 🌺"
    };

    const yagnamAnnouncementTelugu = {
      id: "yagnam-2025-telugu",
      content: "🌸 504వ శ్రీ సరస్వతి యజ్ఞము - 25-05-2025 ఆదివారం 🌸 అందరూ ఆహ్వానితులే! 🌺"
    };

    const yagnamAnnouncementHindi = {
      id: "yagnam-2025-hindi",
      content: "🌸 504वां श्री सरस्वती यज्ञम - 25-05-2025 रविवार 🌸 सभी का स्वागत है! 🌺"
    };

    // Set only the yagnam announcements, no need to fetch from database
    setAnnouncements([
      yagnamAnnouncementEnglish,
      yagnamAnnouncementTelugu,
      yagnamAnnouncementHindi
    ]);

    setIsLoading(false);

    // No need to subscribe to changes since we're only using hardcoded announcements
    return () => {
      // No cleanup needed
    };
  }, []);

  // If there are no active announcements, don't render anything
  if (!isLoading && announcements.length === 0 && !error) {
    return null;
  }

  return (
    <div className={cn(
      "py-3 bg-gradient-to-r from-temple-maroon via-temple-maroon/95 to-temple-maroon/90 dark:from-temple-dark-maroon dark:via-temple-dark-maroon/95 dark:to-temple-dark-maroon/90 overflow-hidden",
      "relative transition-all border-y border-temple-gold/30 shadow-md -mt-1"
    )}>
      <div className="container mx-auto px-4 flex flex-col sm:flex-row items-center">
        {/* Controls */}
        <div className="flex items-center space-x-2 mb-2 sm:mb-0 sm:mr-4">
          <Button
            variant="ghost"
            size="icon"
            className="text-temple-gold hover:text-temple-light-gold hover:bg-temple-maroon/50 dark:hover:bg-temple-dark-maroon/70 transition-colors"
            onClick={() => setIsPaused(!isPaused)}
            aria-label={isPaused ? "Play announcements" : "Pause announcements"}
          >
            {isPaused ? <PlayCircle className="h-5 w-5" /> : <PauseCircle className="h-5 w-5" />}
          </Button>

          <div className="flex flex-col">
            <Button
              variant="ghost"
              size="icon"
              className="h-5 w-5 p-0 text-temple-gold hover:text-temple-light-gold hover:bg-transparent transition-colors"
              onClick={() => setSpeed(prev => Math.min(prev + 0.1, 2.0))}
              disabled={speed >= 2.0}
              aria-label="Increase speed"
            >
              <ChevronUp className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-5 w-5 p-0 text-temple-gold hover:text-temple-light-gold hover:bg-transparent transition-colors"
              onClick={() => setSpeed(prev => Math.max(prev - 0.1, 0.2))}
              disabled={speed <= 0.2}
              aria-label="Decrease speed"
            >
              <ChevronDown className="h-4 w-4" />
            </Button>
          </div>

          <div className="text-xs font-mono bg-temple-maroon/50 dark:bg-temple-dark-maroon/70 text-temple-gold px-2 py-1 rounded border border-temple-gold/20">
            {speed.toFixed(1)}x
          </div>
        </div>

        {/* Announcements */}
        <div className="overflow-hidden flex-1 w-full">
          {isLoading ? (
            <Skeleton className="h-8 w-full bg-temple-gold/20" />
          ) : error ? (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          ) : (
            <div
              ref={scrollContainerRef}
              className="whitespace-nowrap overflow-hidden py-2"
            >
              <div
                className={cn(
                  "inline-block",
                  !isPaused && animationClass,
                  isPaused && "animate-none"
                )}
              >
                {/* Display the first announcement statically */}
                <span className="mx-8 text-base md:text-lg font-medium text-shimmer">
                  {announcements.length > 0 ? announcements[0].content : ""}
                </span>
                <span className="mx-4 text-white">•</span>

                {/* Display all announcements in the scrolling marquee */}
                {announcements.map((announcement, i) => (
                  <span
                    key={announcement.id}
                    className={cn(
                      "mx-8 text-base md:text-lg font-medium",
                      "text-shimmer",
                      announcement.id.includes("telugu") && "font-telugu",
                      announcement.id.includes("hindi") && "font-sanskrit"
                    )}
                  >
                    {announcement.content}
                    {i < announcements.length - 1 && (
                      <span className="mx-4 text-white">•</span>
                    )}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
