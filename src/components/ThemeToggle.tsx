
import { useTheme } from "./ThemeProvider";
import { But<PERSON> } from "@/components/ui/button";
import { Moon, Sun } from "lucide-react";

export function ThemeToggle() {
  const { theme, setTheme } = useTheme();

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
      className="rounded-full w-9 h-9"
      aria-label="Toggle theme"
    >
      {theme === "dark" ? (
        <Sun className="h-5 w-5 text-temple-gold" />
      ) : (
        <Moon className="h-5 w-5 text-temple-maroon" />
      )}
    </Button>
  );
}
