
import { useState, useEffect, useRef } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { ArrowRight, Play, X } from "lucide-react";
import { Link } from 'react-router-dom';
import { useContent } from '@/contexts/ContentContext';
import { FloatingElements, ParallaxElement } from '@/components/3d/FloatingElements';
import { MobileOptimized, IntersectionAnimation } from '@/components/mobile/MobileOptimized';

// Video Popup Component
const VideoPopup = ({ isOpen, onClose, videoSrc }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4" onClick={onClose}>
      <div className="relative w-full max-w-4xl max-h-[90vh]" onClick={e => e.stopPropagation()}>
        <button
          onClick={onClose}
          className="absolute -top-10 right-0 text-white hover:text-temple-gold transition-colors"
          aria-label="Close video"
        >
          <X size={24} />
        </button>
        <video
          src={videoSrc}
          className="w-full h-full max-h-[80vh] object-contain"
          controls
          autoPlay
          playsInline
        />
      </div>
    </div>
  );
};

// Decorative SVG Elements
const DecorativeElements = () => (
  <>
    {/* Floating Elements */}
    <div className="absolute top-1/4 left-1/4 w-16 h-16 opacity-20">
      <svg viewBox="0 0 100 100" className="w-full h-full">
        <circle cx="50" cy="50" r="40" fill="none" stroke="#FFD700" strokeWidth="2" strokeDasharray="5,5" />
      </svg>
    </div>
    <div className="absolute bottom-1/4 right-1/4 w-24 h-24 opacity-20">
      <svg viewBox="0 0 100 100" className="w-full h-full">
        <rect x="20" y="20" width="60" height="60" fill="none" stroke="#FFD700" strokeWidth="2" />
      </svg>
    </div>
    {/* Mandala Pattern */}
    <div className="absolute inset-0 overflow-hidden opacity-5 pointer-events-none">
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[200%] h-[200%]">
        <svg viewBox="0 0 200 200" className="w-full h-full">
          <defs>
            <pattern id="mandala" x="0" y="0" width="0.1" height="0.1">
              <circle cx="100" cy="100" r="10" fill="#FFD700" fillOpacity="0.5" />
            </pattern>
          </defs>
          <rect x="0" y="0" width="100%" height="100%" fill="url(#mandala)" />
        </svg>
      </div>
    </div>
  </>
);

export function Hero() {
  const { content, loading } = useContent();
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [showVideo, setShowVideo] = useState(false);
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);
  const autoplayRef = useRef<NodeJS.Timeout | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const [windowWidth, setWindowWidth] = useState(typeof window !== 'undefined' ? window.innerWidth : 0);

  // Get hero slides from content or use fallback
  const heroSlides = content?.hero?.slides || [
    {
      id: 1,
      image: "/images/hero/hero_1.jpg",
      title: "Welcome to Anantasagar Kshetramu",
      subtitle: "A Sacred Place of Worship",
      showText: false
    },
    {
      id: 2,
      image: "/images/hero/hero_2.jpg",
      title: "Sri Saraswati Yagnam",
      subtitle: "500+ Yagnams Performed",
      showText: false
    },
    {
      id: 3,
      image: "/images/hero/hero_3.jpeg",
      title: "Spiritual Heritage",
      subtitle: "Preserving Ancient Traditions",
      showText: false
    }
  ];

  // Handle video play/pause when slide changes
  useEffect(() => {
    if (videoRef.current) {
      if (heroSlides[currentSlide].type === 'video') {
        // Just prepare the video, don't autoplay
        videoRef.current.load();
      }
    }
  }, [currentSlide]);

  // Reset the autoplay timer whenever the slide changes
  const resetAutoplayTimer = () => {
    if (autoplayRef.current) {
      clearTimeout(autoplayRef.current);
    }

    autoplayRef.current = setTimeout(() => {
      nextSlide();
    }, 7000);
  };

  // Initialize autoplay
  useEffect(() => {
    resetAutoplayTimer();

    return () => {
      if (autoplayRef.current) {
        clearTimeout(autoplayRef.current);
      }
    };
  }, [currentSlide]);

  // Handle slide change
  const changeSlide = (index: number) => {
    if (index !== currentSlide && !isTransitioning) {
      setIsTransitioning(true);
      setTimeout(() => {
        setCurrentSlide(index);
        setIsTransitioning(false);
      }, 500);
    }
  };

  // Next slide function
  const nextSlide = () => {
    changeSlide((currentSlide + 1) % heroSlides.length);
  };

  // Previous slide function
  const prevSlide = () => {
    changeSlide((currentSlide - 1 + heroSlides.length) % heroSlides.length);
  };

  // Touch handlers for mobile swiping
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe) {
      nextSlide();
    } else if (isRightSwipe) {
      prevSlide();
    }

    setTouchStart(null);
    setTouchEnd(null);
  };

  // Handle video playback
  const handleVideoPlay = () => {
    setShowVideo(true);
  };

  const handleCloseVideo = () => {
    setShowVideo(false);
    if (videoRef.current) {
      videoRef.current.pause();
    }
  };

  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Determine optimal height based on screen size
  const getOptimalHeight = () => {
    if (windowWidth < 640) {
      // Mobile: use aspect ratio to maintain image proportions
      return 'min(100vw, 75vh)';
    } else if (windowWidth < 1024) {
      // Tablet
      return 'min(100vw, 65vh)';
    } else {
      // Desktop
      return 'min(100vw, 70vh)';
    }
  };

  // Show loading state after all hooks are declared
  if (loading) {
    return (
      <div className="relative w-full h-[70vh] bg-temple-maroon/10 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-temple-gold mx-auto mb-4"></div>
          <p className="text-temple-maroon">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <MobileOptimized>
      <div
        className={`relative w-full overflow-hidden bg-temple-maroon/10`}
        style={{
          height: getOptimalHeight(),
        }}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {/* Floating 3D Elements */}
        <FloatingElements
          count={6}
          size="md"
          color="gold"
          speed="slow"
          className="opacity-20"
        />
      {/* Hero Content */}
      {heroSlides.map((slide, index) => {
        const isActive = index === currentSlide && !isTransitioning;

        return (
          <div
            key={slide.id}
            className={cn(
              "absolute inset-0 transition-opacity duration-1000 overflow-hidden",
              isActive ? "opacity-100" : "opacity-0 pointer-events-none"
            )}
          >
            {slide.type === 'video' ? (
              <div className="relative w-full h-full">
                {/* Video Thumbnail */}
                <div
                  className="absolute inset-0 bg-cover bg-center"
                  style={{
                    backgroundImage: `linear-gradient(to bottom, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.7)), url(${slide.thumbnail || slide.image})`,
                  }}
                >
                  <button
                    onClick={handleVideoPlay}
                    className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-20 h-20 bg-temple-gold/80 hover:bg-temple-gold rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110 group"
                    aria-label="Play video"
                  >
                    <Play className="w-10 h-10 text-white ml-1" fill="currentColor" />
                    <span className="sr-only">Play Video</span>
                  </button>
                </div>

                {/* Hidden video element for the popup */}
                <video
                  ref={videoRef}
                  className="hidden"
                  preload="metadata"
                >
                  <source src={slide.video} type="video/mp4" />
                  Your browser does not support the video tag.
                </video>

                {/* Video Popup */}
                <VideoPopup
                  isOpen={showVideo && isActive}
                  onClose={handleCloseVideo}
                  videoSrc={slide.video}
                />
              </div>
            ) : (
              <div className="absolute inset-0 flex items-center justify-center overflow-hidden">
                <div className="relative w-full h-full">
                  <img
                    src={slide.image}
                    alt={slide.title || `Slide ${index + 1}`}
                    className="absolute top-0 left-0 w-full h-full transition-transform duration-1000"
                    style={{
                      transform: isActive ? 'scale(1)' : 'scale(1.05)',
                      objectFit: 'contain',
                      objectPosition: 'center center',
                      width: '100%',
                      height: '100%',
                      backgroundColor: 'rgba(153, 0, 51, 0.05)',
                      maxHeight: '100%',
                      maxWidth: '100%'
                    }}
                    onLoad={(e) => {
                      // Ensure all images are displayed fully without cropping
                      const img = e.target as HTMLImageElement;
                      const container = img.parentElement;

                      if (container) {
                        // Always use 'contain' to show full image without cropping
                        img.style.objectFit = 'contain';
                        img.style.objectPosition = 'center center';
                        img.style.display = 'block';
                        img.style.width = '100%';
                        img.style.height = '100%';
                        img.style.maxHeight = '100%';
                        img.style.maxWidth = '100%';
                      }
                    }}
                  />
                </div>
              </div>
            )}
          </div>
        );
      })}

      {/* Decorative Elements - Modern Geometry */}
      <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 opacity-20 animate-spin-slow">
          <svg viewBox="0 0 200 200" className="w-full h-full">
            <defs>
              <linearGradient id="mandalaGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#FFD700" />
                <stop offset="100%" stopColor="#800020" />
              </linearGradient>
            </defs>
            <path
              d="M100,10 L110,50 L150,30 L130,70 L170,90 L130,110 L150,150 L110,130 L100,170 L90,130 L50,150 L70,110 L30,90 L70,70 L50,30 L90,50 Z"
              fill="none"
              stroke="url(#mandalaGradient)"
              strokeWidth="1"
            />
            <circle cx="100" cy="90" r="60" fill="none" stroke="url(#mandalaGradient)" strokeWidth="1" />
            <circle cx="100" cy="90" r="40" fill="none" stroke="url(#mandalaGradient)" strokeWidth="1" />
            <circle cx="100" cy="90" r="20" fill="none" stroke="url(#mandalaGradient)" strokeWidth="1" />
          </svg>
        </div>

        {/* Abstract shapes */}
        <div className="absolute bottom-0 right-0 w-80 h-80 opacity-10">
          <svg viewBox="0 0 100 100" className="w-full h-full">
            <circle cx="50" cy="50" r="40" fill="url(#mandalaGradient)" />
          </svg>
        </div>

        <div className="absolute top-0 right-0 w-60 h-60 opacity-10">
          <svg viewBox="0 0 100 100" className="w-full h-full">
            <rect x="20" y="20" width="60" height="60" fill="url(#mandalaGradient)" />
          </svg>
        </div>
      </div>

      {/* Decorative Elements */}
      <DecorativeElements />

      {/* Navigation Arrows */}
      <div className="absolute inset-y-0 left-4 sm:left-6 flex items-center">
        <button
          onClick={prevSlide}
          className="bg-black/30 hover:bg-black/50 text-white p-2 sm:p-3 rounded-full transition-colors shadow-lg"
          aria-label="Previous slide"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round">
            <path d="M15 18l-6-6 6-6" />
          </svg>
        </button>
      </div>

      <div className="absolute inset-y-0 right-4 sm:right-6 flex items-center">
        <button
          onClick={nextSlide}
          className="bg-black/30 hover:bg-black/50 text-white p-2 sm:p-3 rounded-full transition-colors shadow-lg"
          aria-label="Next slide"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round">
            <path d="M9 18l6-6-6-6" />
          </svg>
        </button>
      </div>

      {/* Dots navigation */}
      <div className="absolute bottom-4 sm:bottom-8 left-0 right-0 flex justify-center space-x-2 sm:space-x-3 z-10">
        {heroSlides.map((_, index) => (
          <button
            key={index}
            onClick={() => changeSlide(index)}
            className={cn(
              "rounded-full transition-all shadow-md",
              index === currentSlide
                ? "bg-temple-gold w-6 sm:w-8 h-2 sm:h-3"
                : "bg-temple-ivory/70 hover:bg-temple-ivory w-2 sm:w-3 h-2 sm:h-3"
            )}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div>
      </div>
    </MobileOptimized>
  );
}
