import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Image, Upload } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';

interface ImageUploaderProps {
  onUpload: (file: File) => void;
  maxSize?: number; // in bytes
  acceptedTypes?: string[];
}

export const ImageUploader: React.FC<ImageUploaderProps> = ({
  onUpload,
  maxSize = 5 * 1024 * 1024, // 5MB default
  acceptedTypes = ['image/jpeg', 'image/png', 'image/webp']
}) => {
  const [preview, setPreview] = useState<string | null>(null);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [error, setError] = useState<string | null>(null);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    setError(null);
    const file = acceptedFiles[0];

    if (file.size > maxSize) {
      setError(`File size should be less than ${Math.round(maxSize / 1024 / 1024)}MB`);
      return;
    }

    if (!acceptedTypes.includes(file.type)) {
      setError('Invalid file type. Please upload an image file.');
      return;
    }

    // Create preview
    const objectUrl = URL.createObjectURL(file);
    setPreview(objectUrl);

    // Simulate upload progress
    let progress = 0;
    const interval = setInterval(() => {
      progress += 10;
      setUploadProgress(progress);
      if (progress >= 100) {
        clearInterval(interval);
        onUpload(file);
      }
    }, 100);

    // Cleanup
    return () => {
      URL.revokeObjectURL(objectUrl);
      clearInterval(interval);
    };
  }, [maxSize, acceptedTypes, onUpload]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': acceptedTypes
    },
    maxFiles: 1,
    multiple: false
  });

  return (
    <div className="space-y-4">
      <div
        {...getRootProps()}
        className={`
          border-2 border-dashed rounded-lg p-8 text-center cursor-pointer
          transition-colors duration-200
          ${isDragActive ? 'border-primary bg-primary/5' : 'border-border'}
          hover:border-primary hover:bg-primary/5
        `}
      >
        <input {...getInputProps()} />
        <div className="flex flex-col items-center gap-2">
          <Upload className="h-8 w-8 text-muted-foreground" />
          {isDragActive ? (
            <p>Drop the image here...</p>
          ) : (
            <>
              <p>Drag & drop an image here, or click to select</p>
              <p className="text-sm text-muted-foreground">
                Supported formats: JPEG, PNG, WebP (Max {Math.round(maxSize / 1024 / 1024)}MB)
              </p>
            </>
          )}
        </div>
      </div>

      {error && (
        <div className="text-destructive text-sm">{error}</div>
      )}

      {preview && (
        <div className="space-y-4">
          <div className="relative aspect-video rounded-lg overflow-hidden border">
            <img
              src={preview}
              alt="Preview"
              className="object-cover w-full h-full"
            />
          </div>
          {uploadProgress < 100 && (
            <Progress value={uploadProgress} className="w-full" />
          )}
        </div>
      )}

      {preview && uploadProgress === 100 && (
        <Button
          variant="outline"
          className="w-full"
          onClick={() => {
            setPreview(null);
            setUploadProgress(0);
          }}
        >
          Upload Another Image
        </Button>
      )}
    </div>
  );
};
