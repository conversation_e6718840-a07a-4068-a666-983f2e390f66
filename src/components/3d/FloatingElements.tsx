import React, { useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';

interface FloatingElementsProps {
  className?: string;
  count?: number;
  size?: 'sm' | 'md' | 'lg';
  color?: 'gold' | 'saffron' | 'maroon';
  speed?: 'slow' | 'medium' | 'fast';
}

export const FloatingElements: React.FC<FloatingElementsProps> = ({
  className = '',
  count = 6,
  size = 'md',
  color = 'gold',
  speed = 'medium'
}) => {
  const containerRef = useRef<HTMLDivElement>(null);

  const sizeClasses = {
    sm: 'w-2 h-2',
    md: 'w-3 h-3',
    lg: 'w-4 h-4'
  };

  const colorClasses = {
    gold: 'bg-temple-gold/30',
    saffron: 'bg-temple-saffron/30',
    maroon: 'bg-temple-maroon/30'
  };

  const speedClasses = {
    slow: 'animate-pulse',
    medium: 'animate-bounce',
    fast: 'animate-ping'
  };

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const elements = container.querySelectorAll('.floating-element');
    
    elements.forEach((element, index) => {
      const htmlElement = element as HTMLElement;
      const delay = index * 0.5;
      const duration = 3 + Math.random() * 2;
      
      htmlElement.style.animationDelay = `${delay}s`;
      htmlElement.style.animationDuration = `${duration}s`;
      
      // Random positioning
      htmlElement.style.left = `${Math.random() * 90}%`;
      htmlElement.style.top = `${Math.random() * 90}%`;
    });
  }, [count]);

  return (
    <div 
      ref={containerRef}
      className={cn(
        "absolute inset-0 pointer-events-none overflow-hidden",
        className
      )}
    >
      {Array.from({ length: count }, (_, index) => (
        <div
          key={index}
          className={cn(
            "floating-element absolute rounded-full opacity-60",
            sizeClasses[size],
            colorClasses[color],
            speedClasses[speed]
          )}
          style={{
            animationName: 'float',
            animationTimingFunction: 'ease-in-out',
            animationIterationCount: 'infinite',
            animationDirection: index % 2 === 0 ? 'normal' : 'reverse'
          }}
        />
      ))}
      
      <style jsx>{`
        @keyframes float {
          0%, 100% {
            transform: translateY(0px) translateX(0px);
          }
          25% {
            transform: translateY(-20px) translateX(10px);
          }
          50% {
            transform: translateY(-10px) translateX(-10px);
          }
          75% {
            transform: translateY(-30px) translateX(5px);
          }
        }
      `}</style>
    </div>
  );
};

interface Card3DProps {
  children: React.ReactNode;
  className?: string;
  intensity?: 'subtle' | 'medium' | 'strong';
  glowEffect?: boolean;
}

export const Card3D: React.FC<Card3DProps> = ({
  children,
  className = '',
  intensity = 'medium',
  glowEffect = false
}) => {
  const cardRef = useRef<HTMLDivElement>(null);

  const intensityValues = {
    subtle: { rotateX: 2, rotateY: 2, translateZ: 5 },
    medium: { rotateX: 5, rotateY: 5, translateZ: 10 },
    strong: { rotateX: 10, rotateY: 10, translateZ: 20 }
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!cardRef.current) return;

    const card = cardRef.current;
    const rect = card.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    const mouseX = e.clientX - centerX;
    const mouseY = e.clientY - centerY;
    
    const rotateX = (mouseY / rect.height) * intensityValues[intensity].rotateX;
    const rotateY = (mouseX / rect.width) * intensityValues[intensity].rotateY;
    
    card.style.transform = `
      perspective(1000px) 
      rotateX(${-rotateX}deg) 
      rotateY(${rotateY}deg) 
      translateZ(${intensityValues[intensity].translateZ}px)
    `;
  };

  const handleMouseLeave = () => {
    if (!cardRef.current) return;
    cardRef.current.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) translateZ(0px)';
  };

  return (
    <div
      ref={cardRef}
      className={cn(
        "transition-transform duration-300 ease-out transform-gpu",
        glowEffect && "relative overflow-hidden",
        className
      )}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
      style={{
        transformStyle: 'preserve-3d',
      }}
    >
      {glowEffect && (
        <div className="absolute inset-0 bg-gradient-to-r from-temple-gold/20 via-transparent to-temple-saffron/20 opacity-0 transition-opacity duration-300 hover:opacity-100 pointer-events-none" />
      )}
      {children}
    </div>
  );
};

interface ParallaxElementProps {
  children: React.ReactNode;
  speed?: number;
  className?: string;
}

export const ParallaxElement: React.FC<ParallaxElementProps> = ({
  children,
  speed = 0.5,
  className = ''
}) => {
  const elementRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const handleScroll = () => {
      const scrolled = window.pageYOffset;
      const rate = scrolled * -speed;
      element.style.transform = `translateY(${rate}px)`;
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [speed]);

  return (
    <div
      ref={elementRef}
      className={cn("transform-gpu", className)}
    >
      {children}
    </div>
  );
};

interface GlassMorphismProps {
  children: React.ReactNode;
  className?: string;
  blur?: 'sm' | 'md' | 'lg';
  opacity?: number;
}

export const GlassMorphism: React.FC<GlassMorphismProps> = ({
  children,
  className = '',
  blur = 'md',
  opacity = 0.1
}) => {
  const blurValues = {
    sm: 'backdrop-blur-sm',
    md: 'backdrop-blur-md',
    lg: 'backdrop-blur-lg'
  };

  return (
    <div
      className={cn(
        "border border-white/20 shadow-lg",
        blurValues[blur],
        className
      )}
      style={{
        background: `rgba(255, 255, 255, ${opacity})`,
      }}
    >
      {children}
    </div>
  );
};

interface AnimatedCounterProps {
  end: number;
  duration?: number;
  suffix?: string;
  className?: string;
}

export const AnimatedCounter: React.FC<AnimatedCounterProps> = ({
  end,
  duration = 2000,
  suffix = '',
  className = ''
}) => {
  const [count, setCount] = React.useState(0);
  const countRef = useRef<HTMLSpanElement>(null);

  useEffect(() => {
    const element = countRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          let start = 0;
          const increment = end / (duration / 16);
          
          const timer = setInterval(() => {
            start += increment;
            if (start >= end) {
              setCount(end);
              clearInterval(timer);
            } else {
              setCount(Math.floor(start));
            }
          }, 16);

          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    observer.observe(element);
    return () => observer.disconnect();
  }, [end, duration]);

  return (
    <span ref={countRef} className={className}>
      {count}{suffix}
    </span>
  );
};
