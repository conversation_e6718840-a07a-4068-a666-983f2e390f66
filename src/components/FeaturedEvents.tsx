
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, <PERSON>Header } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowRight, Calendar } from "lucide-react";
import { Link } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { EventPopup } from "./EventPopup";

interface Event {
  id: string;
  title: string;
  date: string;
  time: string | null;
  description: string | null;
  image_url: string | null;
}

export function FeaturedEvents() {
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedEventId, setSelectedEventId] = useState<string | null>(null);

  useEffect(() => {
    // Use a hardcoded event for the Yagnam
    const yagnamEvent = {
      id: "yagnam-2025",
      title: "504th Sri Saraswati Yagnam",
      date: "2025-05-25",
      time: "8:00 AM - 1:00 PM",
      description: "Join us for the sacred 504th Sri Saraswati Yagnam ceremony and receive divine blessings. This auspicious event includes special rituals, prayers, and prasadam distribution.",
      image_url: "/images/events/yagnam.jpg"
    };

    setEvents([yagnamEvent]);
    setLoading(false);
  }, []);

  const handleEventClick = (id: string) => {
    setSelectedEventId(id);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="py-12">
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row justify-between items-center mb-10">
          <div>
            <h2 className="text-3xl font-heading text-temple-maroon dark:text-temple-gold mb-2">
              504th Sri Saraswati Yagnam
            </h2>
            <p className="text-muted-foreground">
              Join us for the sacred yagnam ceremony on May 25, 2025 and receive divine blessings
            </p>
          </div>
          <Button
            asChild
            className="mt-4 md:mt-0 bg-temple-gold hover:bg-temple-saffron text-temple-maroon"
          >
            <Link to="/events">
              View All Events
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </div>

        {loading ? (
          <div className="max-w-4xl mx-auto">
            <Card className="overflow-hidden border-temple-gold/20">
              <div className="h-64 bg-slate-200 dark:bg-slate-700 animate-pulse" />
              <CardHeader className="pb-2">
                <div className="h-8 w-3/4 bg-slate-200 dark:bg-slate-700 animate-pulse rounded mb-2" />
                <div className="h-5 w-2/3 bg-slate-200 dark:bg-slate-700 animate-pulse rounded" />
              </CardHeader>
              <CardContent>
                <div className="h-5 w-full bg-slate-200 dark:bg-slate-700 animate-pulse rounded mb-2" />
                <div className="h-5 w-4/5 bg-slate-200 dark:bg-slate-700 animate-pulse rounded mb-2" />
                <div className="h-5 w-3/4 bg-slate-200 dark:bg-slate-700 animate-pulse rounded" />
              </CardContent>
              <CardFooter>
                <div className="h-10 w-40 bg-slate-200 dark:bg-slate-700 animate-pulse rounded" />
              </CardFooter>
            </Card>
          </div>
        ) : events.length > 0 ? (
          <div className="max-w-4xl mx-auto">
            {events.map((event) => (
              <Card
                key={event.id}
                className="overflow-hidden hover-glow border-temple-gold/20 transition-all duration-300 hover:shadow-xl"
              >
                <div className="w-full overflow-hidden">
                  <img
                    src={event.image_url || '/images/events/yagnam.jpg'}
                    alt={event.title}
                    className="w-full object-contain max-h-[400px]"
                  />
                </div>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <h3 className="font-heading text-2xl md:text-3xl text-temple-maroon dark:text-temple-gold">
                      {event.title}
                    </h3>
                  </div>
                  <div className="flex items-center text-base text-muted-foreground mt-2">
                    <Calendar className="h-5 w-5 mr-2 text-temple-gold" />
                    <span className="font-medium">{formatDate(event.date)}</span>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-base text-muted-foreground">
                    {event.description || "Join us for this special event at our temple."}
                  </p>
                </CardContent>
                <CardFooter>
                  <Button
                    className="bg-temple-gold hover:bg-temple-saffron text-white"
                    onClick={() => handleEventClick(event.id)}
                  >
                    View Event Details
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        ) : (
          <div className="max-w-4xl mx-auto">
            <Card className="overflow-hidden hover-glow border-temple-gold/20 transition-all duration-300 hover:shadow-xl">
              <div className="w-full overflow-hidden">
                <img
                  src="/images/events/yagnam.jpg"
                  alt="504th Sri Saraswati Yagnam"
                  className="w-full object-contain max-h-[400px]"
                />
              </div>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <h3 className="font-heading text-2xl md:text-3xl text-temple-maroon dark:text-temple-gold">
                    504th Sri Saraswati Yagnam
                  </h3>
                </div>
                <div className="flex items-center text-base text-muted-foreground mt-2">
                  <Calendar className="h-5 w-5 mr-2 text-temple-gold" />
                  <span className="font-medium">May 25, 2025</span>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-base text-muted-foreground">
                  Join us for the sacred 504th Sri Saraswati Yagnam ceremony and receive divine blessings. This auspicious event includes special rituals, prayers, and prasadam distribution.
                </p>
              </CardContent>
              <CardFooter>
                <Button asChild className="bg-temple-gold hover:bg-temple-saffron text-white">
                  <Link to="/events">
                    View Event Details
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          </div>
        )}
      </div>

      {/* Event Popup */}
      {selectedEventId && (
        <EventPopup
          eventId={selectedEventId}
          onClose={() => setSelectedEventId(null)}
        />
      )}
    </div>
  );
}
