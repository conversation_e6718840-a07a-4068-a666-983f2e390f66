
import { useEffect, useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { StotramModal } from "@/components/StotramModal";
import { BookOpen } from "lucide-react";

// Short excerpts from <PERSON><PERSON><PERSON> Stotram for display
const quotes = [
  {
    text: "శ్రీసరస్వతీ స్తోత్రమ్",
    source: "శ్రీసరస్వతీ స్తోత్రమ్"
  },
  {
    text: "అయివిధి వాదిని! వాదన మోదిని! మోదవిధాయిని! ధాతృసుతే!",
    source: "శ్రీసరస్వతీ స్తోత్రమ్"
  },
  {
    text: "జయ జయహే పరమేశ్వరి! భారతి! బ్రహ్మపురస్కృతి వాగ్దయితే!",
    source: "శ్రీసరస్వతీ స్తోత్రమ్"
  },
  {
    text: "సురనర వానర చేతన చేతని! సూక్తిరసాయన సూక్తనుతే!",
    source: "శ్రీసరస్వతీ స్తోత్రమ్"
  },
  {
    text: "పరిపరి యాచిత దైవతహర్షిణి! దైత్యనిరోషిణి! ధర్మరతే!",
    source: "శ్రీసరస్వతీ స్తోత్రమ్"
  }
];

export function QuoteOfTheDay() {
  const [quote, setQuote] = useState(quotes[0]);
  const [fadeIn, setFadeIn] = useState(true);

  // Change quote daily based on date
  useEffect(() => {
    const today = new Date();
    // Fix: Convert dates to timestamps (numbers) before subtraction
    const start = new Date(today.getFullYear(), 0, 0);
    const diff = today.getTime() - start.getTime();
    const dayOfYear = Math.floor(diff / (1000 * 60 * 60 * 24));
    setQuote(quotes[dayOfYear % quotes.length]);
  }, []);

  return (
    <Card className="border border-temple-gold/20 bg-temple-ivory/50 dark:bg-temple-dark-maroon/50 overflow-hidden relative">
      <CardContent className={cn(
        "p-6 transition-opacity duration-500",
        fadeIn ? "opacity-100" : "opacity-0"
      )}>
        <div className="absolute top-2 right-2 text-4xl opacity-20 text-temple-gold font-sans">"</div>
        <h3 className="text-xl font-heading text-center text-temple-maroon dark:text-temple-gold mb-4">శ్రీసరస్వతీ స్తోత్రమ్</h3>
        <p className="text-center italic mb-4">{quote.text}</p>
        <p className="text-right text-sm text-temple-maroon/70 dark:text-temple-gold/70 font-medium mb-4">{quote.source}</p>

        <div className="flex justify-center">
          <StotramModal>
            <Button
              variant="outline"
              size="sm"
              className="border-temple-gold text-temple-maroon hover:bg-temple-gold hover:text-white dark:text-temple-gold dark:border-temple-gold dark:hover:bg-temple-gold dark:hover:text-temple-maroon transition-colors"
            >
              <BookOpen className="w-4 h-4 mr-2" />
              View Full Stotram
            </Button>
          </StotramModal>
        </div>
      </CardContent>
    </Card>
  );
}
