import { Button } from "@/components/ui/button";
import { 
  FacebookShareButton, 
  TwitterShareButton, 
  WhatsappShareButton, 
  EmailShareButton,
  FacebookIcon,
  TwitterIcon,
  WhatsappIcon,
  EmailIcon
} from 'react-share';
import { cn } from "@/lib/utils";

interface SocialShareProps {
  url: string;
  title: string;
  description?: string;
  className?: string;
  iconSize?: number;
  round?: boolean;
}

export function SocialShare({
  url,
  title,
  description = "Check out this page from Anantasagar Kshetramu Temple!",
  className,
  iconSize = 32,
  round = true
}: SocialShareProps) {
  // Ensure URL is absolute
  const fullUrl = url.startsWith('http') ? url : `https://anantasagareshwari.org${url}`;
  
  return (
    <div className={cn("flex items-center gap-2", className)}>
      <div className="text-sm font-medium mr-2">Share:</div>
      
      <FacebookShareButton url={fullUrl} quote={title} className="hover:opacity-80 transition-opacity">
        <FacebookIcon size={iconSize} round={round} />
      </FacebookShareButton>
      
      <TwitterShareButton url={fullUrl} title={title} className="hover:opacity-80 transition-opacity">
        <TwitterIcon size={iconSize} round={round} />
      </TwitterShareButton>
      
      <WhatsappShareButton url={fullUrl} title={`${title}\n${description}`} className="hover:opacity-80 transition-opacity">
        <WhatsappIcon size={iconSize} round={round} />
      </WhatsappShareButton>
      
      <EmailShareButton url={fullUrl} subject={title} body={`${description}\n\n${fullUrl}`} className="hover:opacity-80 transition-opacity">
        <EmailIcon size={iconSize} round={round} />
      </EmailShareButton>
    </div>
  );
}
