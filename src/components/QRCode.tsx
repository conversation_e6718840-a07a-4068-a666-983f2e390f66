import React, { useState } from 'react';

interface QRCodeProps {
  value: string;
  size?: number;
  className?: string;
}

export const QRCode: React.FC<QRCodeProps> = ({ value, size = 128, className = '' }) => {
  const [imageError, setImageError] = useState(false);

  // Generate QR code using QR Server API with better encoding
  const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=${size}x${size}&data=${encodeURIComponent(value)}&bgcolor=FFFFFF&color=990033&margin=5&ecc=M&format=png`;

  // Fallback QR code as SVG if external service fails
  const fallbackQR = (
    <div
      className="bg-white rounded-lg shadow-md border-2 border-temple-gold/30 flex items-center justify-center"
      style={{ width: size, height: size }}
    >
      <div className="text-center p-2">
        <div className="text-temple-maroon text-xs font-bold mb-1">Temple Location</div>
        <div className="grid grid-cols-8 gap-px">
          {Array.from({ length: 64 }, (_, i) => (
            <div
              key={i}
              className={`w-1 h-1 ${Math.random() > 0.5 ? 'bg-temple-maroon' : 'bg-white'}`}
            />
          ))}
        </div>
        <div className="text-temple-maroon text-xs mt-1">Scan for Directions</div>
      </div>
    </div>
  );

  if (imageError) {
    return <div className={`inline-block ${className}`}>{fallbackQR}</div>;
  }

  return (
    <div className={`inline-block ${className}`}>
      <img
        src={qrCodeUrl}
        alt="QR Code for Temple Location"
        width={size}
        height={size}
        className="rounded-lg shadow-md border-2 border-temple-gold/30"
        loading="lazy"
        onError={() => setImageError(true)}
      />
    </div>
  );
};
