
import { useEffect, useState, useRef } from "react";
import { cn } from "@/lib/utils";

// Use the same announcements as in DynamicAnnouncements
const announcementItems = [
  "🌸 504th Sri Saraswati Yagnam - 25-05-2025 Sunday 🌸 All are welcome! 🌺",
  "🌸 504వ శ్రీ సరస్వతి యజ్ఞము - 25-05-2025 ఆదివారం 🌸 అందరూ ఆహ్వానితులే! 🌺",
  "🌸 504वां श्री सरस्वती यज्ञम - 25-05-2025 रविवार 🌸 सभी का स्वागत है! 🌺",
];

export function Announcements() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const scrollRef = useRef<HTMLDivElement>(null);
  const [isPaused, setIsPaused] = useState(false);

  useEffect(() => {
    const intervalId = setInterval(() => {
      if (!isPaused) {
        setCurrentIndex((prevIndex) =>
          prevIndex === announcementItems.length - 1 ? 0 : prevIndex + 1
        );
      }
    }, 5000);

    return () => clearInterval(intervalId);
  }, [isPaused]);

  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollLeft = 0;
      const animateScroll = () => {
        if (scrollRef.current && !isPaused) {
          scrollRef.current.scrollLeft += 1;

          requestAnimationFrame(animateScroll);
        }
      };

      requestAnimationFrame(animateScroll);
    }
  }, [currentIndex, isPaused]);

  return (
    <div
      className="py-4 bg-gradient-to-r from-temple-maroon via-temple-maroon/95 to-temple-maroon/90 dark:from-temple-dark-maroon dark:via-temple-dark-maroon/95 dark:to-temple-dark-maroon/90 overflow-hidden relative transition-all border-y border-temple-gold/30 shadow-md"
      onMouseEnter={() => setIsPaused(true)}
      onMouseLeave={() => setIsPaused(false)}
    >
      <div className="container mx-auto flex items-center px-4">
        <div className="flex-shrink-0 mr-4">
          <span className="font-heading text-temple-gold font-bold text-sm md:text-base">
            ANNOUNCEMENTS:
          </span>
        </div>
        <div
          ref={scrollRef}
          className="overflow-hidden whitespace-nowrap flex-grow"
        >
          <div
            className={cn(
              "inline-block text-base md:text-lg font-medium text-shimmer",
              isPaused ? "" : "animate-none",
              currentIndex === 1 && "font-telugu",
              currentIndex === 2 && "font-sanskrit"
            )}
          >
            {announcementItems[currentIndex]}
          </div>
        </div>
      </div>
    </div>
  );
}
