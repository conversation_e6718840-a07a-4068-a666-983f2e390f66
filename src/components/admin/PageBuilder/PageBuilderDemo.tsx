import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  Play, 
  Pause, 
  RotateCcw, 
  Eye, 
  Settings,
  Layers,
  Palette,
  Smartphone,
  Tablet,
  Monitor,
  Rocket,
  Save,
  Download,
  Upload
} from 'lucide-react';
import { cn } from '@/lib/utils';

// Demo data for the page builder
const DEMO_FEATURES = [
  {
    title: 'Drag & Drop Editor',
    description: 'Intuitive visual editing with real-time preview',
    icon: Layers,
    status: 'active',
    demo: 'Try dragging elements from the widget library to the canvas'
  },
  {
    title: 'Hero Section Editor',
    description: 'Advanced hero slider with layers and animations',
    icon: Eye,
    status: 'active',
    demo: 'Create stunning hero sections with multiple slides and overlays'
  },
  {
    title: 'Responsive Design',
    description: 'Edit for desktop, tablet, and mobile views',
    icon: Smartphone,
    status: 'active',
    demo: 'Switch between device views to optimize for all screen sizes'
  },
  {
    title: 'Live Preview',
    description: 'See changes instantly as you edit',
    icon: Play,
    status: 'active',
    demo: 'Real-time preview updates without page refresh'
  },
  {
    title: 'Publishing Workflow',
    description: 'Staging and production deployment',
    icon: Rocket,
    status: 'active',
    demo: 'Test in staging before publishing to live site'
  },
  {
    title: 'Version Control',
    description: 'Track changes and rollback if needed',
    icon: RotateCcw,
    status: 'active',
    demo: 'Complete version history with one-click restore'
  }
];

const DEMO_WIDGETS = [
  { name: 'Text Block', category: 'Basic', icon: '📝' },
  { name: 'Image', category: 'Basic', icon: '🖼️' },
  { name: 'Button', category: 'Basic', icon: '🔘' },
  { name: 'Hero Banner', category: 'Temple', icon: '🏛️' },
  { name: 'Gallery', category: 'Temple', icon: '📸' },
  { name: 'Contact Form', category: 'Interactive', icon: '📋' },
  { name: 'Section', category: 'Layout', icon: '📦' },
  { name: 'Spacer', category: 'Layout', icon: '📏' }
];

export const PageBuilderDemo: React.FC = () => {
  const [activeDemo, setActiveDemo] = useState<string>('overview');
  const [isPlaying, setIsPlaying] = useState(false);

  const renderFeatureDemo = (feature: typeof DEMO_FEATURES[0]) => {
    const IconComponent = feature.icon;
    
    return (
      <Card key={feature.title} className="relative overflow-hidden">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <IconComponent className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <CardTitle className="text-lg">{feature.title}</CardTitle>
                <p className="text-sm text-muted-foreground">{feature.description}</p>
              </div>
            </div>
            <Badge variant="default" className="bg-green-100 text-green-700">
              {feature.status}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-3">{feature.demo}</p>
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => setActiveDemo(feature.title.toLowerCase().replace(/\s+/g, '-'))}
          >
            Try Demo
          </Button>
        </CardContent>
      </Card>
    );
  };

  const renderWidgetLibrary = () => (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
      {DEMO_WIDGETS.map((widget) => (
        <Card key={widget.name} className="p-4 text-center hover:shadow-md transition-shadow cursor-pointer">
          <div className="text-2xl mb-2">{widget.icon}</div>
          <h4 className="font-medium text-sm">{widget.name}</h4>
          <p className="text-xs text-muted-foreground">{widget.category}</p>
        </Card>
      ))}
    </div>
  );

  const renderCanvas = () => (
    <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center bg-gray-50">
      <div className="space-y-4">
        <div className="w-16 h-16 bg-blue-100 rounded-lg mx-auto flex items-center justify-center">
          <Layers className="w-8 h-8 text-blue-600" />
        </div>
        <div>
          <h3 className="text-lg font-medium">Visual Canvas</h3>
          <p className="text-muted-foreground">Drag widgets here to start building</p>
        </div>
        <div className="flex justify-center gap-2">
          <Button variant="outline" size="sm">
            <Monitor className="w-4 h-4 mr-2" />
            Desktop
          </Button>
          <Button variant="ghost" size="sm">
            <Tablet className="w-4 h-4 mr-2" />
            Tablet
          </Button>
          <Button variant="ghost" size="sm">
            <Smartphone className="w-4 h-4 mr-2" />
            Mobile
          </Button>
        </div>
      </div>
    </div>
  );

  const renderHeroEditor = () => (
    <div className="space-y-4">
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 text-white relative overflow-hidden">
        <div className="relative z-10">
          <h2 className="text-2xl font-bold mb-2">Hero Section Demo</h2>
          <p className="text-blue-100">Create stunning hero sections with multiple slides</p>
          <Button variant="secondary" className="mt-4">
            Learn More
          </Button>
        </div>
        <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -mr-16 -mt-16"></div>
      </div>
      
      <div className="grid grid-cols-3 gap-4">
        <Card className="p-4">
          <h4 className="font-medium mb-2">Slide 1</h4>
          <div className="w-full h-16 bg-blue-100 rounded mb-2"></div>
          <p className="text-xs text-muted-foreground">Main hero slide</p>
        </Card>
        <Card className="p-4">
          <h4 className="font-medium mb-2">Slide 2</h4>
          <div className="w-full h-16 bg-green-100 rounded mb-2"></div>
          <p className="text-xs text-muted-foreground">Events showcase</p>
        </Card>
        <Card className="p-4">
          <h4 className="font-medium mb-2">Slide 3</h4>
          <div className="w-full h-16 bg-purple-100 rounded mb-2"></div>
          <p className="text-xs text-muted-foreground">Gallery highlight</p>
        </Card>
      </div>
    </div>
  );

  const renderPreviewSystem = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
        <div className="flex items-center gap-3">
          <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
          <span className="font-medium">Staging Environment</span>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Eye className="w-4 h-4 mr-2" />
            Preview
          </Button>
          <Button size="sm">
            <Rocket className="w-4 h-4 mr-2" />
            Publish
          </Button>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card className="p-4">
          <h4 className="font-medium mb-2">Version History</h4>
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>v1.2.3 - Latest</span>
              <Badge variant="default">Live</Badge>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span>v1.2.2 - Previous</span>
              <Button variant="ghost" size="sm">Restore</Button>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span>v1.2.1 - Backup</span>
              <Button variant="ghost" size="sm">Restore</Button>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <h4 className="font-medium mb-2">Export/Import</h4>
          <div className="space-y-2">
            <Button variant="outline" className="w-full justify-start">
              <Download className="w-4 h-4 mr-2" />
              Export Page Data
            </Button>
            <Button variant="outline" className="w-full justify-start">
              <Upload className="w-4 h-4 mr-2" />
              Import Configuration
            </Button>
          </div>
        </Card>
      </div>
    </div>
  );

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="inline-flex items-center gap-2 px-4 py-2 bg-blue-100 text-blue-700 rounded-full text-sm font-medium">
          <Layers className="w-4 h-4" />
          Visual Page Builder Demo
        </div>
        <h1 className="text-4xl font-bold">WordPress Elementor-Style Editor</h1>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          Experience the power of visual page building with drag-and-drop editing, 
          real-time preview, and professional publishing workflow.
        </p>
      </div>

      {/* Demo Navigation */}
      <Tabs value={activeDemo} onValueChange={setActiveDemo} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="editor">Page Editor</TabsTrigger>
          <TabsTrigger value="hero">Hero Editor</TabsTrigger>
          <TabsTrigger value="preview">Preview & Publish</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {DEMO_FEATURES.map(renderFeatureDemo)}
          </div>
        </TabsContent>

        <TabsContent value="editor" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <div className="lg:col-span-1">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Widget Library</CardTitle>
                </CardHeader>
                <CardContent>
                  {renderWidgetLibrary()}
                </CardContent>
              </Card>
            </div>
            
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Visual Canvas</CardTitle>
                </CardHeader>
                <CardContent>
                  {renderCanvas()}
                </CardContent>
              </Card>
            </div>
            
            <div className="lg:col-span-1">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Properties</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium">Element Type</label>
                      <p className="text-sm text-muted-foreground">Text Block</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium">Font Size</label>
                      <p className="text-sm text-muted-foreground">16px</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium">Color</label>
                      <p className="text-sm text-muted-foreground">#333333</p>
                    </div>
                    <Button variant="outline" className="w-full">
                      <Settings className="w-4 h-4 mr-2" />
                      Edit Properties
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="hero" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Hero Section Editor</CardTitle>
              <p className="text-muted-foreground">
                Create stunning hero sections with multiple slides, text overlays, and animations
              </p>
            </CardHeader>
            <CardContent>
              {renderHeroEditor()}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="preview" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Preview & Publishing System</CardTitle>
              <p className="text-muted-foreground">
                Test your changes in staging before publishing to the live website
              </p>
            </CardHeader>
            <CardContent>
              {renderPreviewSystem()}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Call to Action */}
      <div className="text-center space-y-4 p-8 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg">
        <h2 className="text-2xl font-bold">Ready to Start Building?</h2>
        <p className="text-muted-foreground">
          Access the full page builder in the admin dashboard
        </p>
        <Button size="lg" className="bg-gradient-to-r from-blue-600 to-purple-600">
          <Rocket className="w-5 h-5 mr-2" />
          Launch Page Builder
        </Button>
      </div>
    </div>
  );
};
