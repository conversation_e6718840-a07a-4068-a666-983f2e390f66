import React, { useState, useCallback, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  Layout, 
  Image as ImageIcon, 
  Settings, 
  Eye, 
  Save,
  Plus,
  Edit,
  Trash2,
  Copy,
  Globe
} from 'lucide-react';
import { PageBuilder, PageStructure } from './PageBuilder';
import { HeroEditor, HeroSlide, HeroSettings } from './HeroEditor';
import { PreviewSystem } from './PreviewSystem';
import { useContent } from '@/contexts/ContentContext';
import { toast } from 'sonner';

// Available page templates
const PAGE_TEMPLATES = [
  {
    id: 'home',
    name: 'Home Page',
    description: 'Main landing page with hero section and key content',
    thumbnail: '/images/templates/home.jpg'
  },
  {
    id: 'about',
    name: 'About Page',
    description: 'About the temple with history and information',
    thumbnail: '/images/templates/about.jpg'
  },
  {
    id: 'events',
    name: 'Events Page',
    description: 'Temple events and celebrations',
    thumbnail: '/images/templates/events.jpg'
  },
  {
    id: 'gallery',
    name: 'Gallery Page',
    description: 'Photo gallery and media',
    thumbnail: '/images/templates/gallery.jpg'
  },
  {
    id: 'contact',
    name: 'Contact Page',
    description: 'Contact information and forms',
    thumbnail: '/images/templates/contact.jpg'
  }
];

// Default hero settings
const DEFAULT_HERO_SETTINGS: HeroSettings = {
  autoplay: true,
  autoplaySpeed: 5000,
  pauseOnHover: true,
  showDots: true,
  showArrows: true,
  infinite: true,
  height: {
    desktop: '500px',
    tablet: '400px',
    mobile: '300px'
  }
};

export const PageBuilderManager: React.FC = () => {
  const { content, updateContent } = useContent();
  const [activeTab, setActiveTab] = useState('builder');
  const [selectedPage, setSelectedPage] = useState<string>('home');
  const [pages, setPages] = useState<PageStructure[]>([]);
  const [heroSlides, setHeroSlides] = useState<HeroSlide[]>([]);
  const [heroSettings, setHeroSettings] = useState<HeroSettings>(DEFAULT_HERO_SETTINGS);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize page builder data
  useEffect(() => {
    const initializePageBuilder = async () => {
      try {
        setIsLoading(true);
        
        // Load existing pages or create defaults
        const existingPages = content?.pageBuilder?.pages || [];
        if (existingPages.length === 0) {
          // Create default pages
          const defaultPages = PAGE_TEMPLATES.map(template => ({
            id: template.id,
            name: template.name,
            elements: [],
            settings: {
              seo: {
                title: template.name,
                description: template.description,
                keywords: ''
              },
              layout: {
                maxWidth: '1200px',
                background: '#ffffff'
              }
            }
          }));
          setPages(defaultPages);
        } else {
          setPages(existingPages);
        }

        // Load hero slides from existing content
        const existingHeroSlides = content?.hero?.slides || [];
        if (existingHeroSlides.length > 0) {
          const convertedSlides: HeroSlide[] = existingHeroSlides.map((slide, index) => ({
            id: slide.id || `slide_${index}`,
            title: slide.title || '',
            subtitle: slide.subtitle || '',
            imageUrl: slide.imageUrl || '',
            order: slide.order || index,
            active: slide.active !== false,
            layers: [],
            settings: {
              duration: 5000,
              transition: 'fade',
              overlay: {
                enabled: false,
                color: '#000000',
                opacity: 0.3
              }
            }
          }));
          setHeroSlides(convertedSlides);
        }

        // Load hero settings
        const existingHeroSettings = content?.hero?.settings || {};
        setHeroSettings({
          ...DEFAULT_HERO_SETTINGS,
          ...existingHeroSettings
        });

      } catch (error) {
        console.error('Failed to initialize page builder:', error);
        toast.error('Failed to load page builder data');
      } finally {
        setIsLoading(false);
      }
    };

    initializePageBuilder();
  }, [content]);

  // Save page data
  const handleSavePage = useCallback(async (page: PageStructure) => {
    try {
      const updatedPages = pages.map(p => p.id === page.id ? page : p);
      setPages(updatedPages);

      await updateContent({
        pageBuilder: {
          pages: updatedPages,
          lastModified: new Date().toISOString()
        }
      });

      toast.success('Page saved successfully');
    } catch (error) {
      console.error('Failed to save page:', error);
      toast.error('Failed to save page');
    }
  }, [pages, updateContent]);

  // Save hero data
  const handleSaveHero = useCallback(async () => {
    try {
      const heroData = {
        slides: heroSlides.map(slide => ({
          id: slide.id,
          title: slide.title,
          subtitle: slide.subtitle,
          imageUrl: slide.imageUrl,
          order: slide.order,
          active: slide.active
        })),
        settings: heroSettings,
        autoplaySpeed: heroSettings.autoplaySpeed,
        isAutoplay: heroSettings.autoplay
      };

      await updateContent({ hero: heroData });
      toast.success('Hero section saved successfully');
    } catch (error) {
      console.error('Failed to save hero:', error);
      toast.error('Failed to save hero section');
    }
  }, [heroSlides, heroSettings, updateContent]);

  // Publish page
  const handlePublishPage = useCallback(async (page: PageStructure) => {
    try {
      await updateContent({
        pageBuilder: {
          pages: pages.map(p => p.id === page.id ? { ...page, published: true } : p),
          lastModified: new Date().toISOString()
        }
      });

      toast.success('Page published successfully');
    } catch (error) {
      console.error('Failed to publish page:', error);
      toast.error('Failed to publish page');
    }
  }, [pages, updateContent]);

  // Create new page
  const handleCreatePage = useCallback(() => {
    const newPage: PageStructure = {
      id: `page_${Date.now()}`,
      name: 'New Page',
      elements: [],
      settings: {
        seo: {
          title: 'New Page',
          description: '',
          keywords: ''
        },
        layout: {
          maxWidth: '1200px',
          background: '#ffffff'
        }
      }
    };

    setPages(prev => [...prev, newPage]);
    setSelectedPage(newPage.id);
  }, []);

  // Delete page
  const handleDeletePage = useCallback((pageId: string) => {
    if (pages.length <= 1) {
      toast.error('Cannot delete the last page');
      return;
    }

    setPages(prev => prev.filter(p => p.id !== pageId));
    
    if (selectedPage === pageId) {
      setSelectedPage(pages.find(p => p.id !== pageId)?.id || '');
    }

    toast.success('Page deleted successfully');
  }, [pages, selectedPage]);

  // Duplicate page
  const handleDuplicatePage = useCallback((pageId: string) => {
    const pageToClone = pages.find(p => p.id === pageId);
    if (!pageToClone) return;

    const duplicatedPage: PageStructure = {
      ...pageToClone,
      id: `page_${Date.now()}`,
      name: `${pageToClone.name} (Copy)`
    };

    setPages(prev => [...prev, duplicatedPage]);
    setSelectedPage(duplicatedPage.id);
    toast.success('Page duplicated successfully');
  }, [pages]);

  const currentPage = pages.find(p => p.id === selectedPage);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p>Loading Page Builder...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col">
      {/* Header */}
      <div className="border-b bg-background p-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">Visual Page Builder</h1>
            <p className="text-muted-foreground">Create and customize your temple website</p>
          </div>
          
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={handleCreatePage}>
              <Plus className="w-4 h-4 mr-2" />
              New Page
            </Button>
            <Button onClick={() => currentPage && handleSavePage(currentPage)}>
              <Save className="w-4 h-4 mr-2" />
              Save Changes
            </Button>
          </div>
        </div>
      </div>

      {/* Page Selector */}
      <div className="border-b bg-muted/30 p-4">
        <div className="flex items-center gap-4 overflow-x-auto">
          <span className="text-sm font-medium whitespace-nowrap">Pages:</span>
          {pages.map((page) => (
            <div key={page.id} className="flex items-center gap-2">
              <Button
                variant={selectedPage === page.id ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedPage(page.id)}
                className="whitespace-nowrap"
              >
                <Layout className="w-4 h-4 mr-2" />
                {page.name}
              </Button>
              
              {selectedPage === page.id && (
                <div className="flex items-center gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDuplicatePage(page.id)}
                  >
                    <Copy className="w-3 h-3" />
                  </Button>
                  {pages.length > 1 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeletePage(page.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="w-3 h-3" />
                    </Button>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-hidden">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="builder" className="flex items-center gap-2">
              <Layout className="w-4 h-4" />
              Page Builder
            </TabsTrigger>
            <TabsTrigger value="hero" className="flex items-center gap-2">
              <ImageIcon className="w-4 h-4" />
              Hero Editor
            </TabsTrigger>
            <TabsTrigger value="preview" className="flex items-center gap-2">
              <Eye className="w-4 h-4" />
              Preview & Publish
            </TabsTrigger>
          </TabsList>

          <div className="flex-1 overflow-hidden">
            <TabsContent value="builder" className="h-full m-0">
              {currentPage ? (
                <PageBuilder />
              ) : (
                <div className="flex items-center justify-center h-full">
                  <p className="text-muted-foreground">Select a page to start editing</p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="hero" className="h-full m-0">
              <HeroEditor
                slides={heroSlides}
                settings={heroSettings}
                onSlidesUpdate={setHeroSlides}
                onSettingsUpdate={setHeroSettings}
              />
              <div className="p-4 border-t">
                <Button onClick={handleSaveHero}>
                  <Save className="w-4 h-4 mr-2" />
                  Save Hero Section
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="preview" className="h-full m-0 overflow-auto">
              {currentPage && (
                <PreviewSystem
                  currentPage={currentPage}
                  onPageUpdate={(page) => {
                    setPages(prev => prev.map(p => p.id === page.id ? page : p));
                  }}
                  onPublish={handlePublishPage}
                  onSaveDraft={handleSavePage}
                />
              )}
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </div>
  );
};
