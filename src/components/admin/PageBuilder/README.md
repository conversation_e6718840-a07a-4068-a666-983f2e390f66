# 🎨 Visual Page Builder System

A comprehensive WordPress Elementor-style visual page builder for the Anantasagar Kshetramu website that enables complete frontend customization without code changes.

## 🚀 Features

### **Core Visual Editor**
- **Drag-and-Drop Interface**: Intuitive visual editing with real-time preview
- **Component Library**: Pre-built widgets for text, images, buttons, sections, and temple-specific elements
- **Layered Editing**: Support for overlapping elements with z-index control
- **Responsive Design**: Edit for desktop, tablet, and mobile views
- **Live Preview**: Real-time updates as you make changes

### **Hero Section Advanced Editor**
- **Multi-Slide Management**: Add, edit, delete, and reorder hero slides
- **Layer System**: Add text overlays, buttons, and decorative elements
- **Animation Controls**: Configure slide transitions and text animations
- **Timing Controls**: Autoplay speed, pause duration, and hover effects
- **Overlay Management**: Background overlays with color and opacity control

### **Preview & Publishing Workflow**
- **Staging Environment**: Test changes before going live
- **One-Click Publishing**: Deploy approved changes to production
- **Version History**: Track and rollback to previous versions
- **Import/Export**: Backup and restore page configurations

### **Universal Element Editor**
- **Contextual Editing**: Click any element to edit its properties
- **Style Controls**: Typography, colors, spacing, backgrounds, borders
- **Position Management**: Precise positioning and sizing controls
- **Animation Settings**: Entry animations with timing controls

## 📁 File Structure

```
src/components/admin/PageBuilder/
├── PageBuilder.tsx              # Main page builder component
├── PageBuilderManager.tsx       # Integration manager
├── HeroEditor.tsx              # Hero section editor
├── HeroComponents.tsx          # Hero editor components
├── PreviewSystem.tsx           # Preview and publishing system
└── README.md                   # This documentation
```

## 🛠️ Components Overview

### **PageBuilder.tsx**
Main visual editor component with:
- Drag-and-drop canvas
- Widget library sidebar
- Property panel
- Device preview modes
- Undo/redo functionality

### **HeroEditor.tsx**
Specialized hero section editor featuring:
- Slide management
- Layer editing system
- Animation controls
- Preview functionality

### **PreviewSystem.tsx**
Publishing workflow component with:
- Multi-device preview
- Staging/production environments
- Version control
- Import/export functionality

### **PageBuilderManager.tsx**
Main integration component that:
- Manages multiple pages
- Coordinates between editors
- Handles data persistence
- Provides unified interface

## 🎯 Usage Guide

### **Getting Started**

1. **Access the Page Builder**
   ```
   Navigate to Admin Dashboard → Visual Page Builder
   ```

2. **Select a Page**
   - Choose from existing pages or create new ones
   - Switch between Home, About, Events, Gallery, Contact pages

3. **Start Building**
   - Drag widgets from the library to the canvas
   - Click elements to edit their properties
   - Use the device toggle to preview responsive design

### **Widget Library**

#### **Basic Widgets**
- **Text Block**: Rich text content with formatting
- **Image**: Upload or link images with alt text
- **Button**: Call-to-action buttons with links
- **Spacer**: Add spacing between elements

#### **Layout Widgets**
- **Section**: Container for grouping elements
- **Hero Banner**: Full-width hero sections

#### **Temple-Specific Widgets**
- **Gallery**: Photo galleries and media
- **Event Listings**: Dynamic event displays
- **Contact Forms**: Temple contact forms

### **Hero Section Editing**

1. **Add Slides**
   - Click "Add Slide" to create new hero slides
   - Upload background images
   - Set slide duration and transitions

2. **Layer Management**
   - Add text overlays, buttons, or images
   - Position layers with drag-and-drop
   - Configure animations and timing

3. **Settings Configuration**
   - Autoplay controls
   - Navigation options (dots, arrows)
   - Responsive height settings

### **Publishing Workflow**

1. **Save Draft**
   - Save work-in-progress changes
   - Auto-save functionality prevents data loss

2. **Publish to Staging**
   - Test changes in staging environment
   - Review before going live

3. **Publish Live**
   - Deploy to production with one click
   - Changes reflect immediately on website

## 🔧 Technical Implementation

### **Data Structure**

```typescript
interface PageElement {
  id: string;
  type: 'text' | 'image' | 'button' | 'section';
  content: any;
  styles: {
    position: { x: number; y: number };
    size: { width: string; height: string };
    typography: { fontSize: string; color: string };
    // ... more style properties
  };
  responsive: {
    desktop: any;
    tablet: any;
    mobile: any;
  };
}
```

### **Integration Points**

- **ContentContext**: Persists page builder data
- **Admin Dashboard**: Integrated into admin navigation
- **Frontend Rendering**: Dynamic content display
- **SEO Management**: Meta tags and optimization

### **Responsive Design**

The page builder supports three breakpoints:
- **Desktop**: 1200px and above
- **Tablet**: 768px - 1199px
- **Mobile**: Below 768px

Each element can have different styles for each breakpoint.

## 🎨 Styling System

### **Theme Integration**
- Uses existing temple theme colors
- Consistent with site design language
- Dark/light mode support

### **Custom CSS**
- Inject custom CSS for advanced styling
- Global style overrides
- Component-specific styling

## 🔒 Security & Permissions

- **Admin-Only Access**: Restricted to authenticated admin users
- **Content Validation**: Sanitized input and output
- **Version Control**: Track all changes with timestamps
- **Rollback Protection**: Restore previous versions if needed

## 📱 Mobile Optimization

- **Touch-Friendly Interface**: Optimized for mobile admin access
- **Responsive Preview**: Test mobile layouts in real-time
- **Mobile-First Design**: Ensure mobile compatibility

## 🚀 Performance

- **Lazy Loading**: Components load on demand
- **Optimized Rendering**: Efficient DOM updates
- **Caching**: Smart caching for better performance
- **Bundle Splitting**: Separate chunks for page builder

## 🔄 Future Enhancements

### **Planned Features**
- **Template Library**: Pre-designed page templates
- **Global Styles**: Site-wide style management
- **Advanced Animations**: More animation options
- **Form Builder**: Visual form creation
- **E-commerce Integration**: Donation and shop widgets

### **Advanced Capabilities**
- **Custom Widget Development**: Create custom widgets
- **API Integration**: Connect external data sources
- **Multi-language Support**: Content in multiple languages
- **A/B Testing**: Test different page versions

## 🐛 Troubleshooting

### **Common Issues**

1. **Elements Not Saving**
   - Check network connection
   - Verify admin permissions
   - Clear browser cache

2. **Drag and Drop Not Working**
   - Ensure modern browser support
   - Check for JavaScript errors
   - Refresh the page

3. **Preview Not Updating**
   - Clear staging cache
   - Check content context updates
   - Verify data persistence

### **Browser Support**
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 📞 Support

For technical support or feature requests:
- Check the admin dashboard help section
- Review the troubleshooting guide
- Contact the development team

              <Button
                size="sm"
                onClick={clearPreviewCache}
              >
                Clear Cache
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
```

#### **Performance Monitor**

```typescript
const usePerformanceMonitor = () => {
  const [metrics, setMetrics] = useState({
    renderTime: 0,
    elementCount: 0,
    memoryUsage: 0
  });

  useEffect(() => {
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry) => {
        if (entry.name === 'page-builder-render') {
          setMetrics(prev => ({
            ...prev,
            renderTime: entry.duration
          }));
        }
      });
    });

    observer.observe({ entryTypes: ['measure'] });

    return () => observer.disconnect();
  }, []);

  const measureRender = (callback: () => void) => {
    performance.mark('render-start');
    callback();
    performance.mark('render-end');
    performance.measure('page-builder-render', 'render-start', 'render-end');
  };

  return { metrics, measureRender };
};
```

### **Error Recovery**

#### **Automatic Error Recovery**

```typescript
const useErrorRecovery = () => {
  const [lastKnownGoodState, setLastKnownGoodState] = useState<PageStructure | null>(null);

  const saveGoodState = useCallback((pageData: PageStructure) => {
    try {
      validatePageData(pageData);
      setLastKnownGoodState(pageData);
      localStorage.setItem('pageBuilder_lastGoodState', JSON.stringify(pageData));
    } catch (error) {
      console.warn('Invalid page data, not saving as good state:', error);
    }
  }, []);

  const recoverFromError = useCallback(() => {
    if (lastKnownGoodState) {
      return lastKnownGoodState;
    }

    // Try to recover from localStorage
    try {
      const saved = localStorage.getItem('pageBuilder_lastGoodState');
      if (saved) {
        const recovered = JSON.parse(saved);
        validatePageData(recovered);
        return recovered;
      }
    } catch (error) {
      console.error('Failed to recover from localStorage:', error);
    }

    // Return minimal valid state
    return {
      id: 'recovered',
      name: 'Recovered Page',
      elements: [],
      settings: {
        seo: { title: '', description: '', keywords: '' },
        layout: { maxWidth: '1200px', background: '#ffffff' }
      }
    };
  }, [lastKnownGoodState]);

  return { saveGoodState, recoverFromError };
};
```

### **Support Resources**

#### **Help Documentation Integration**

```typescript
const HelpSystem = () => {
  const [activeHelp, setActiveHelp] = useState<string | null>(null);

  const helpTopics = {
    'getting-started': {
      title: 'Getting Started',
      content: 'Learn the basics of using the page builder...',
      video: '/help/videos/getting-started.mp4'
    },
    'drag-drop': {
      title: 'Drag and Drop',
      content: 'How to use drag and drop functionality...',
      video: '/help/videos/drag-drop.mp4'
    },
    'responsive-design': {
      title: 'Responsive Design',
      content: 'Creating responsive layouts...',
      video: '/help/videos/responsive.mp4'
    }
  };

  return (
    <div className="help-system">
      {activeHelp && (
        <Dialog open={!!activeHelp} onOpenChange={() => setActiveHelp(null)}>
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle>{helpTopics[activeHelp]?.title}</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <video
                src={helpTopics[activeHelp]?.video}
                controls
                className="w-full"
              />
              <p>{helpTopics[activeHelp]?.content}</p>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};
```

---

## 📞 Support and Maintenance

### **Regular Maintenance Tasks**

1. **Weekly Tasks:**
   - Clear old version history (keep last 50 versions)
   - Optimize image storage
   - Check for broken links
   - Review performance metrics

2. **Monthly Tasks:**
   - Update widget library
   - Review user feedback
   - Performance optimization
   - Security updates

3. **Quarterly Tasks:**
   - Major feature updates
   - Browser compatibility testing
   - User training sessions
   - Backup verification

### **Contact Information**

For technical support:
- **Documentation**: `/admin/page-builder/help`
- **Video Tutorials**: Available in help system
- **Debug Tools**: Built-in development mode
- **Error Reporting**: Automatic error logging

### **Version History**

- **v1.0.0**: Initial release with core functionality
- **v1.1.0**: Added hero editor and responsive design
- **v1.2.0**: Publishing workflow and version control
- **v1.3.0**: Performance optimizations and debugging tools

---

**Built with ❤️ for Anantasagar Kshetramu Temple**

*This comprehensive page builder system provides WordPress Elementor-style editing capabilities, enabling complete frontend customization without code changes. The system is designed for scalability, performance, and ease of use.*
