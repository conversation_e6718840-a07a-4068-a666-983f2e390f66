# 🎨 Visual Page Builder System

A comprehensive WordPress Elementor-style visual page builder for the Anantasagar Kshetramu website that enables complete frontend customization without code changes.

## 🚀 Features

### **Core Visual Editor**
- **Drag-and-Drop Interface**: Intuitive visual editing with real-time preview
- **Component Library**: Pre-built widgets for text, images, buttons, sections, and temple-specific elements
- **Layered Editing**: Support for overlapping elements with z-index control
- **Responsive Design**: Edit for desktop, tablet, and mobile views
- **Live Preview**: Real-time updates as you make changes

### **Hero Section Advanced Editor**
- **Multi-Slide Management**: Add, edit, delete, and reorder hero slides
- **Layer System**: Add text overlays, buttons, and decorative elements
- **Animation Controls**: Configure slide transitions and text animations
- **Timing Controls**: Autoplay speed, pause duration, and hover effects
- **Overlay Management**: Background overlays with color and opacity control

### **Preview & Publishing Workflow**
- **Staging Environment**: Test changes before going live
- **One-Click Publishing**: Deploy approved changes to production
- **Version History**: Track and rollback to previous versions
- **Import/Export**: Backup and restore page configurations

### **Universal Element Editor**
- **Contextual Editing**: Click any element to edit its properties
- **Style Controls**: Typography, colors, spacing, backgrounds, borders
- **Position Management**: Precise positioning and sizing controls
- **Animation Settings**: Entry animations with timing controls

## 📁 File Structure

```
src/components/admin/PageBuilder/
├── PageBuilder.tsx              # Main page builder component
├── PageBuilderManager.tsx       # Integration manager
├── HeroEditor.tsx              # Hero section editor
├── HeroComponents.tsx          # Hero editor components
├── PreviewSystem.tsx           # Preview and publishing system
└── README.md                   # This documentation
```

## 🛠️ Components Overview

### **PageBuilder.tsx**
Main visual editor component with:
- Drag-and-drop canvas
- Widget library sidebar
- Property panel
- Device preview modes
- Undo/redo functionality

### **HeroEditor.tsx**
Specialized hero section editor featuring:
- Slide management
- Layer editing system
- Animation controls
- Preview functionality

### **PreviewSystem.tsx**
Publishing workflow component with:
- Multi-device preview
- Staging/production environments
- Version control
- Import/export functionality

### **PageBuilderManager.tsx**
Main integration component that:
- Manages multiple pages
- Coordinates between editors
- Handles data persistence
- Provides unified interface

## 🎯 Usage Guide

### **Getting Started**

1. **Access the Page Builder**
   ```
   Navigate to Admin Dashboard → Visual Page Builder
   ```

2. **Select a Page**
   - Choose from existing pages or create new ones
   - Switch between Home, About, Events, Gallery, Contact pages

3. **Start Building**
   - Drag widgets from the library to the canvas
   - Click elements to edit their properties
   - Use the device toggle to preview responsive design

### **Widget Library**

#### **Basic Widgets**
- **Text Block**: Rich text content with formatting
- **Image**: Upload or link images with alt text
- **Button**: Call-to-action buttons with links
- **Spacer**: Add spacing between elements

#### **Layout Widgets**
- **Section**: Container for grouping elements
- **Hero Banner**: Full-width hero sections

#### **Temple-Specific Widgets**
- **Gallery**: Photo galleries and media
- **Event Listings**: Dynamic event displays
- **Contact Forms**: Temple contact forms

### **Hero Section Editing**

1. **Add Slides**
   - Click "Add Slide" to create new hero slides
   - Upload background images
   - Set slide duration and transitions

2. **Layer Management**
   - Add text overlays, buttons, or images
   - Position layers with drag-and-drop
   - Configure animations and timing

3. **Settings Configuration**
   - Autoplay controls
   - Navigation options (dots, arrows)
   - Responsive height settings

### **Publishing Workflow**

1. **Save Draft**
   - Save work-in-progress changes
   - Auto-save functionality prevents data loss

2. **Publish to Staging**
   - Test changes in staging environment
   - Review before going live

3. **Publish Live**
   - Deploy to production with one click
   - Changes reflect immediately on website

## 🔧 Technical Implementation

### **Data Structure**

```typescript
interface PageElement {
  id: string;
  type: 'text' | 'image' | 'button' | 'section';
  content: any;
  styles: {
    position: { x: number; y: number };
    size: { width: string; height: string };
    typography: { fontSize: string; color: string };
    // ... more style properties
  };
  responsive: {
    desktop: any;
    tablet: any;
    mobile: any;
  };
}
```

### **Integration Points**

- **ContentContext**: Persists page builder data
- **Admin Dashboard**: Integrated into admin navigation
- **Frontend Rendering**: Dynamic content display
- **SEO Management**: Meta tags and optimization

### **Responsive Design**

The page builder supports three breakpoints:
- **Desktop**: 1200px and above
- **Tablet**: 768px - 1199px
- **Mobile**: Below 768px

Each element can have different styles for each breakpoint.

## 🎨 Styling System

### **Theme Integration**
- Uses existing temple theme colors
- Consistent with site design language
- Dark/light mode support

### **Custom CSS**
- Inject custom CSS for advanced styling
- Global style overrides
- Component-specific styling

## 🔒 Security & Permissions

- **Admin-Only Access**: Restricted to authenticated admin users
- **Content Validation**: Sanitized input and output
- **Version Control**: Track all changes with timestamps
- **Rollback Protection**: Restore previous versions if needed

## 📱 Mobile Optimization

- **Touch-Friendly Interface**: Optimized for mobile admin access
- **Responsive Preview**: Test mobile layouts in real-time
- **Mobile-First Design**: Ensure mobile compatibility

## 🚀 Performance

- **Lazy Loading**: Components load on demand
- **Optimized Rendering**: Efficient DOM updates
- **Caching**: Smart caching for better performance
- **Bundle Splitting**: Separate chunks for page builder

## 🔄 Future Enhancements

### **Planned Features**
- **Template Library**: Pre-designed page templates
- **Global Styles**: Site-wide style management
- **Advanced Animations**: More animation options
- **Form Builder**: Visual form creation
- **E-commerce Integration**: Donation and shop widgets

### **Advanced Capabilities**
- **Custom Widget Development**: Create custom widgets
- **API Integration**: Connect external data sources
- **Multi-language Support**: Content in multiple languages
- **A/B Testing**: Test different page versions

## 🐛 Troubleshooting

### **Common Issues**

1. **Elements Not Saving**
   - Check network connection
   - Verify admin permissions
   - Clear browser cache

2. **Drag and Drop Not Working**
   - Ensure modern browser support
   - Check for JavaScript errors
   - Refresh the page

3. **Preview Not Updating**
   - Clear staging cache
   - Check content context updates
   - Verify data persistence

### **Browser Support**
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 📞 Support

For technical support or feature requests:
- Check the admin dashboard help section
- Review the troubleshooting guide
- Contact the development team

---

**Built with ❤️ for Anantasagar Kshetramu Temple**
