import React, { useState, useCallback, useRef } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Eye, 
  EyeOff, 
  Rocket, 
  Save, 
  RotateCcw, 
  Monitor, 
  Tablet, 
  Smartphone,
  Globe,
  Clock,
  CheckCircle,
  AlertTriangle,
  Download,
  Upload,
  History,
  GitBranch
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { PageStructure, PageElement } from './PageBuilder';
import { useContent } from '@/contexts/ContentContext';

// Version Interface
interface PageVersion {
  id: string;
  name: string;
  timestamp: Date;
  author: string;
  description: string;
  pageData: PageStructure;
  status: 'draft' | 'staging' | 'published';
}

// Preview Mode Types
type PreviewMode = 'desktop' | 'tablet' | 'mobile';
type PreviewEnvironment = 'staging' | 'production';

interface PreviewSystemProps {
  currentPage: PageStructure;
  onPageUpdate: (page: PageStructure) => void;
  onPublish: (page: PageStructure) => Promise<void>;
  onSaveDraft: (page: PageStructure) => Promise<void>;
}

export const PreviewSystem: React.FC<PreviewSystemProps> = ({
  currentPage,
  onPageUpdate,
  onPublish,
  onSaveDraft
}) => {
  const { content, updateContent } = useContent();
  const [previewMode, setPreviewMode] = useState<PreviewMode>('desktop');
  const [previewEnvironment, setPreviewEnvironment] = useState<PreviewEnvironment>('staging');
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [versions, setVersions] = useState<PageVersion[]>([]);
  const [isPublishing, setIsPublishing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [publishStatus, setPublishStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const previewRef = useRef<HTMLIFrameElement>(null);

  // Generate preview URL
  const getPreviewUrl = useCallback(() => {
    const baseUrl = previewEnvironment === 'staging' 
      ? 'https://staging.anantasagareshwari.org' 
      : 'https://anantasagareshwari.org';
    return `${baseUrl}?preview=true&page=${currentPage.id}`;
  }, [previewEnvironment, currentPage.id]);

  // Save as draft
  const handleSaveDraft = useCallback(async () => {
    setIsSaving(true);
    try {
      await onSaveDraft(currentPage);
      
      // Add to version history
      const newVersion: PageVersion = {
        id: `version_${Date.now()}`,
        name: `Draft - ${new Date().toLocaleString()}`,
        timestamp: new Date(),
        author: 'Admin',
        description: 'Auto-saved draft',
        pageData: { ...currentPage },
        status: 'draft'
      };
      
      setVersions(prev => [newVersion, ...prev.slice(0, 9)]); // Keep last 10 versions
    } catch (error) {
      console.error('Failed to save draft:', error);
    } finally {
      setIsSaving(false);
    }
  }, [currentPage, onSaveDraft]);

  // Publish to staging
  const handlePublishToStaging = useCallback(async () => {
    setIsPublishing(true);
    setPublishStatus('idle');
    
    try {
      // Save to staging environment
      await updateContent({ 
        pageBuilder: { 
          ...currentPage, 
          lastModified: new Date().toISOString(),
          environment: 'staging'
        } 
      });
      
      const stagingVersion: PageVersion = {
        id: `staging_${Date.now()}`,
        name: `Staging - ${new Date().toLocaleString()}`,
        timestamp: new Date(),
        author: 'Admin',
        description: 'Published to staging for review',
        pageData: { ...currentPage },
        status: 'staging'
      };
      
      setVersions(prev => [stagingVersion, ...prev]);
      setPublishStatus('success');
      setPreviewEnvironment('staging');
    } catch (error) {
      console.error('Failed to publish to staging:', error);
      setPublishStatus('error');
    } finally {
      setIsPublishing(false);
    }
  }, [currentPage, updateContent]);

  // Publish to production
  const handlePublishToProduction = useCallback(async () => {
    setIsPublishing(true);
    setPublishStatus('idle');
    
    try {
      await onPublish(currentPage);
      
      const productionVersion: PageVersion = {
        id: `prod_${Date.now()}`,
        name: `Production - ${new Date().toLocaleString()}`,
        timestamp: new Date(),
        author: 'Admin',
        description: 'Published to production',
        pageData: { ...currentPage },
        status: 'published'
      };
      
      setVersions(prev => [productionVersion, ...prev]);
      setPublishStatus('success');
    } catch (error) {
      console.error('Failed to publish to production:', error);
      setPublishStatus('error');
    } finally {
      setIsPublishing(false);
    }
  }, [currentPage, onPublish]);

  // Rollback to previous version
  const handleRollback = useCallback((version: PageVersion) => {
    onPageUpdate(version.pageData);
  }, [onPageUpdate]);

  // Export page data
  const handleExport = useCallback(() => {
    const dataStr = JSON.stringify(currentPage, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `${currentPage.name.replace(/\s+/g, '_')}_${Date.now()}.json`;
    link.click();
    
    URL.revokeObjectURL(url);
  }, [currentPage]);

  // Import page data
  const handleImport = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const pageData = JSON.parse(e.target?.result as string);
        onPageUpdate(pageData);
      } catch (error) {
        console.error('Failed to import page data:', error);
      }
    };
    reader.readAsText(file);
  }, [onPageUpdate]);

  // Render preview frame
  const renderPreviewFrame = () => {
    const frameWidth = previewMode === 'desktop' ? '100%' : 
                     previewMode === 'tablet' ? '768px' : '375px';
    const frameHeight = previewMode === 'desktop' ? '600px' : 
                       previewMode === 'tablet' ? '1024px' : '667px';

    return (
      <div className="flex justify-center p-4 bg-gray-100">
        <div 
          className="bg-white shadow-lg rounded-lg overflow-hidden"
          style={{ width: frameWidth, height: frameHeight }}
        >
          <iframe
            ref={previewRef}
            src={getPreviewUrl()}
            className="w-full h-full border-0"
            title="Page Preview"
          />
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Preview Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="w-5 h-5" />
            Preview & Publishing
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap items-center gap-4">
            {/* Device Preview Toggle */}
            <div className="flex items-center border rounded-lg p-1">
              <Button
                variant={previewMode === 'desktop' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setPreviewMode('desktop')}
              >
                <Monitor className="w-4 h-4" />
              </Button>
              <Button
                variant={previewMode === 'tablet' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setPreviewMode('tablet')}
              >
                <Tablet className="w-4 h-4" />
              </Button>
              <Button
                variant={previewMode === 'mobile' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setPreviewMode('mobile')}
              >
                <Smartphone className="w-4 h-4" />
              </Button>
            </div>

            {/* Environment Toggle */}
            <div className="flex items-center border rounded-lg p-1">
              <Button
                variant={previewEnvironment === 'staging' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setPreviewEnvironment('staging')}
              >
                Staging
              </Button>
              <Button
                variant={previewEnvironment === 'production' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setPreviewEnvironment('production')}
              >
                Production
              </Button>
            </div>

            {/* Action Buttons */}
            <Button
              variant="outline"
              onClick={() => setIsPreviewOpen(true)}
            >
              <Eye className="w-4 h-4 mr-2" />
              Full Preview
            </Button>

            <Button
              variant="outline"
              onClick={handleSaveDraft}
              disabled={isSaving}
            >
              <Save className="w-4 h-4 mr-2" />
              {isSaving ? 'Saving...' : 'Save Draft'}
            </Button>

            <Button
              variant="secondary"
              onClick={handlePublishToStaging}
              disabled={isPublishing}
            >
              <GitBranch className="w-4 h-4 mr-2" />
              Publish to Staging
            </Button>

            <Button
              onClick={handlePublishToProduction}
              disabled={isPublishing}
            >
              <Rocket className="w-4 h-4 mr-2" />
              {isPublishing ? 'Publishing...' : 'Publish Live'}
            </Button>
          </div>

          {/* Status Messages */}
          {publishStatus === 'success' && (
            <Alert className="mt-4">
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                Page published successfully! Changes are now live.
              </AlertDescription>
            </Alert>
          )}

          {publishStatus === 'error' && (
            <Alert variant="destructive" className="mt-4">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Failed to publish page. Please try again.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Inline Preview */}
      <Card>
        <CardHeader>
          <CardTitle>Live Preview</CardTitle>
        </CardHeader>
        <CardContent>
          {renderPreviewFrame()}
        </CardContent>
      </Card>

      {/* Version History */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="w-5 h-5" />
            Version History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {versions.length === 0 ? (
              <p className="text-muted-foreground text-center py-4">
                No versions saved yet
              </p>
            ) : (
              versions.map((version) => (
                <div
                  key={version.id}
                  className="flex items-center justify-between p-3 border rounded-lg"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{version.name}</span>
                      <Badge 
                        variant={
                          version.status === 'published' ? 'default' :
                          version.status === 'staging' ? 'secondary' : 'outline'
                        }
                      >
                        {version.status}
                      </Badge>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {version.description} • {version.timestamp.toLocaleString()}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleRollback(version)}
                    >
                      <RotateCcw className="w-4 h-4 mr-1" />
                      Restore
                    </Button>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>

      {/* Import/Export */}
      <Card>
        <CardHeader>
          <CardTitle>Import/Export</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={handleExport}>
              <Download className="w-4 h-4 mr-2" />
              Export Page
            </Button>
            
            <div>
              <input
                type="file"
                accept=".json"
                onChange={handleImport}
                className="hidden"
                id="import-file"
              />
              <Button variant="outline" asChild>
                <label htmlFor="import-file" className="cursor-pointer">
                  <Upload className="w-4 h-4 mr-2" />
                  Import Page
                </label>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Full Preview Modal */}
      <Dialog open={isPreviewOpen} onOpenChange={setIsPreviewOpen}>
        <DialogContent className="max-w-6xl h-[80vh]">
          <DialogHeader>
            <DialogTitle>Full Page Preview</DialogTitle>
          </DialogHeader>
          <div className="flex-1 overflow-hidden">
            {renderPreviewFrame()}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};
