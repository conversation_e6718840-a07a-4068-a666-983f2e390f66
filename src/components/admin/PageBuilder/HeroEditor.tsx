import React, { useState, useCallback, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Plus,
  Trash2,
  Upload,
  Play,
  Pause,
  RotateCcw,
  Move,
  Type,
  Image as ImageIcon,
  Layers,
  Settings,
  Eye,
  EyeOff,
  Copy,
  ArrowUp,
  ArrowDown
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';

// Hero Slide Interface
export interface HeroSlide {
  id: string;
  title: string;
  subtitle: string;
  imageUrl: string;
  order: number;
  active: boolean;
  layers: HeroLayer[];
  settings: {
    duration: number;
    transition: 'fade' | 'slide' | 'zoom' | 'none';
    overlay: {
      enabled: boolean;
      color: string;
      opacity: number;
    };
  };
}

// Hero Layer Interface (for text overlays, buttons, etc.)
export interface HeroLayer {
  id: string;
  type: 'text' | 'button' | 'image' | 'shape';
  content: any;
  position: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  styles: {
    fontSize?: string;
    fontWeight?: string;
    color?: string;
    backgroundColor?: string;
    borderRadius?: string;
    padding?: string;
    textAlign?: 'left' | 'center' | 'right';
    animation?: {
      type: 'fadeIn' | 'slideUp' | 'slideDown' | 'slideLeft' | 'slideRight' | 'zoomIn' | 'none';
      duration: number;
      delay: number;
    };
  };
  visible: boolean;
  zIndex: number;
}

// Hero Settings Interface
export interface HeroSettings {
  autoplay: boolean;
  autoplaySpeed: number;
  pauseOnHover: boolean;
  showDots: boolean;
  showArrows: boolean;
  infinite: boolean;
  height: {
    desktop: string;
    tablet: string;
    mobile: string;
  };
}

interface HeroEditorProps {
  slides: HeroSlide[];
  settings: HeroSettings;
  onSlidesUpdate: (slides: HeroSlide[]) => void;
  onSettingsUpdate: (settings: HeroSettings) => void;
}

export const HeroEditor: React.FC<HeroEditorProps> = ({
  slides,
  settings,
  onSlidesUpdate,
  onSettingsUpdate
}) => {
  const [activeSlide, setActiveSlide] = useState<string>(slides[0]?.id || '');
  const [selectedLayer, setSelectedLayer] = useState<string | null>(null);
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [currentPreviewSlide, setCurrentPreviewSlide] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Generate unique ID
  const generateId = () => `hero_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  // Add new slide
  const addSlide = useCallback(() => {
    const newSlide: HeroSlide = {
      id: generateId(),
      title: 'New Slide Title',
      subtitle: 'New slide subtitle',
      imageUrl: '/placeholder.svg',
      order: slides.length,
      active: true,
      layers: [],
      settings: {
        duration: 5000,
        transition: 'fade',
        overlay: {
          enabled: false,
          color: '#000000',
          opacity: 0.3
        }
      }
    };

    onSlidesUpdate([...slides, newSlide]);
    setActiveSlide(newSlide.id);
  }, [slides, onSlidesUpdate]);

  // Delete slide
  const deleteSlide = useCallback((slideId: string) => {
    const updatedSlides = slides.filter(slide => slide.id !== slideId);
    onSlidesUpdate(updatedSlides);

    if (activeSlide === slideId && updatedSlides.length > 0) {
      setActiveSlide(updatedSlides[0].id);
    }
  }, [slides, activeSlide, onSlidesUpdate]);

  // Update slide
  const updateSlide = useCallback((slideId: string, updates: Partial<HeroSlide>) => {
    const updatedSlides = slides.map(slide =>
      slide.id === slideId ? { ...slide, ...updates } : slide
    );
    onSlidesUpdate(updatedSlides);
  }, [slides, onSlidesUpdate]);

  // Add layer to slide
  const addLayer = useCallback((slideId: string, layerType: HeroLayer['type']) => {
    const newLayer: HeroLayer = {
      id: generateId(),
      type: layerType,
      content: getDefaultLayerContent(layerType),
      position: { x: 50, y: 50, width: 200, height: 50 },
      styles: {
        fontSize: '16px',
        fontWeight: 'normal',
        color: '#ffffff',
        backgroundColor: 'transparent',
        textAlign: 'center',
        animation: {
          type: 'fadeIn',
          duration: 1000,
          delay: 0
        }
      },
      visible: true,
      zIndex: 1
    };

    updateSlide(slideId, {
      layers: [...(slides.find(s => s.id === slideId)?.layers || []), newLayer]
    });
    setSelectedLayer(newLayer.id);
  }, [slides, updateSlide]);

  // Get default content for layer types
  const getDefaultLayerContent = (type: HeroLayer['type']) => {
    switch (type) {
      case 'text':
        return { text: 'Edit this text' };
      case 'button':
        return { text: 'Click Me', href: '#', variant: 'default' };
      case 'image':
        return { src: '/placeholder.svg', alt: 'Layer Image' };
      case 'shape':
        return { shape: 'rectangle', color: '#ffffff' };
      default:
        return {};
    }
  };

  // Update layer
  const updateLayer = useCallback((slideId: string, layerId: string, updates: Partial<HeroLayer>) => {
    const slide = slides.find(s => s.id === slideId);
    if (!slide) return;

    const updatedLayers = slide.layers.map(layer =>
      layer.id === layerId ? { ...layer, ...updates } : layer
    );

    updateSlide(slideId, { layers: updatedLayers });
  }, [slides, updateSlide]);

  // Delete layer
  const deleteLayer = useCallback((slideId: string, layerId: string) => {
    const slide = slides.find(s => s.id === slideId);
    if (!slide) return;

    const updatedLayers = slide.layers.filter(layer => layer.id !== layerId);
    updateSlide(slideId, { layers: updatedLayers });

    if (selectedLayer === layerId) {
      setSelectedLayer(null);
    }
  }, [slides, selectedLayer, updateSlide]);

  // Handle image upload
  const handleImageUpload = useCallback((slideId: string, file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const imageUrl = e.target?.result as string;
      updateSlide(slideId, { imageUrl });
    };
    reader.readAsDataURL(file);
  }, [updateSlide]);

  // Handle slide reordering
  const handleSlideReorder = useCallback((result: any) => {
    if (!result.destination) return;

    const reorderedSlides = Array.from(slides);
    const [removed] = reorderedSlides.splice(result.source.index, 1);
    reorderedSlides.splice(result.destination.index, 0, removed);

    // Update order property
    const updatedSlides = reorderedSlides.map((slide, index) => ({
      ...slide,
      order: index
    }));

    onSlidesUpdate(updatedSlides);
  }, [slides, onSlidesUpdate]);

  const currentSlide = slides.find(slide => slide.id === activeSlide);
  const selectedLayerData = currentSlide?.layers.find(layer => layer.id === selectedLayer);

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="border-b p-4">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-bold">Hero Section Editor</h2>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsPreviewMode(!isPreviewMode)}
            >
              {isPreviewMode ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              {isPreviewMode ? 'Edit Mode' : 'Preview'}
            </Button>
            <Button onClick={addSlide}>
              <Plus className="w-4 h-4 mr-2" />
              Add Slide
            </Button>
          </div>
        </div>
      </div>

      <div className="flex-1 flex">
        {/* Slides Panel */}
        {!isPreviewMode && (
          <div className="w-64 border-r bg-muted/30 p-4 overflow-y-auto">
            <h3 className="font-semibold mb-4">Slides</h3>

            <DragDropContext onDragEnd={handleSlideReorder}>
              <Droppable droppableId="slides">
                {(provided) => (
                  <div {...provided.droppableProps} ref={provided.innerRef} className="space-y-2">
                    {slides.map((slide, index) => (
                      <Draggable key={slide.id} draggableId={slide.id} index={index}>
                        {(provided, snapshot) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                            className={cn(
                              'p-3 border rounded-lg cursor-pointer transition-colors',
                              activeSlide === slide.id ? 'border-blue-500 bg-blue-50' : 'hover:bg-muted',
                              snapshot.isDragging && 'shadow-lg'
                            )}
                            onClick={() => setActiveSlide(slide.id)}
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex-1">
                                <div className="font-medium text-sm truncate">{slide.title}</div>
                                <div className="text-xs text-muted-foreground truncate">{slide.subtitle}</div>
                              </div>
                              <div className="flex items-center gap-1">
                                <Switch
                                  checked={slide.active}
                                  onCheckedChange={(checked) => updateSlide(slide.id, { active: checked })}
                                  size="sm"
                                />
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    deleteSlide(slide.id);
                                  }}
                                >
                                  <Trash2 className="w-3 h-3" />
                                </Button>
                              </div>
                            </div>

                            {slide.imageUrl && (
                              <div className="mt-2">
                                <img
                                  src={slide.imageUrl}
                                  alt={slide.title}
                                  className="w-full h-16 object-cover rounded"
                                />
                              </div>
                            )}
                          </div>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </DragDropContext>
          </div>
        )}

        {/* Main Editor Area */}
        <div className="flex-1 flex flex-col">
          {/* Canvas */}
          <div className="flex-1 p-4 overflow-auto bg-gray-100">
            {isPreviewMode ? (
              <HeroPreview
                slides={slides.filter(s => s.active)}
                settings={settings}
                currentSlide={currentPreviewSlide}
                onSlideChange={setCurrentPreviewSlide}
              />
            ) : (
              <HeroCanvas
                slide={currentSlide}
                selectedLayer={selectedLayer}
                onLayerSelect={setSelectedLayer}
                onLayerUpdate={(layerId, updates) =>
                  currentSlide && updateLayer(currentSlide.id, layerId, updates)
                }
              />
            )}
          </div>

          {/* Layer Controls */}
          {!isPreviewMode && currentSlide && (
            <div className="border-t p-4 bg-background">
              <div className="flex items-center justify-between mb-4">
                <h4 className="font-medium">Layers</h4>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => addLayer(currentSlide.id, 'text')}
                  >
                    <Type className="w-4 h-4 mr-1" />
                    Text
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => addLayer(currentSlide.id, 'button')}
                  >
                    <Plus className="w-4 h-4 mr-1" />
                    Button
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => addLayer(currentSlide.id, 'image')}
                  >
                    <ImageIcon className="w-4 h-4 mr-1" />
                    Image
                  </Button>
                </div>
              </div>

              <div className="flex gap-2 overflow-x-auto">
                {currentSlide.layers
                  .sort((a, b) => b.zIndex - a.zIndex)
                  .map((layer) => (
                    <div
                      key={layer.id}
                      className={cn(
                        'p-2 border rounded cursor-pointer min-w-[100px]',
                        selectedLayer === layer.id ? 'border-blue-500 bg-blue-50' : 'hover:bg-muted'
                      )}
                      onClick={() => setSelectedLayer(layer.id)}
                    >
                      <div className="text-xs font-medium">{layer.type}</div>
                      <div className="text-xs text-muted-foreground truncate">
                        {layer.type === 'text' ? layer.content.text : `${layer.type} layer`}
                      </div>
                    </div>
                  ))}
              </div>
            </div>
          )}
        </div>

        {/* Properties Panel */}
        {!isPreviewMode && (
          <div className="w-80 border-l bg-muted/30 overflow-y-auto">
            <Tabs defaultValue="slide" className="h-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="slide">Slide</TabsTrigger>
                <TabsTrigger value="layer">Layer</TabsTrigger>
                <TabsTrigger value="settings">Settings</TabsTrigger>
              </TabsList>

              <TabsContent value="slide" className="p-4 space-y-4">
                {currentSlide && <SlideProperties slide={currentSlide} onUpdate={(updates) => updateSlide(currentSlide.id, updates)} />}
              </TabsContent>

              <TabsContent value="layer" className="p-4 space-y-4">
                {selectedLayerData && currentSlide && (
                  <LayerProperties
                    layer={selectedLayerData}
                    onUpdate={(updates) => updateLayer(currentSlide.id, selectedLayerData.id, updates)}
                    onDelete={() => deleteLayer(currentSlide.id, selectedLayerData.id)}
                  />
                )}
                {!selectedLayerData && (
                  <div className="text-center text-muted-foreground">
                    <Layers className="w-8 h-8 mx-auto mb-2 opacity-50" />
                    <p>Select a layer to edit its properties</p>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="settings" className="p-4 space-y-4">
                <HeroSettingsPanel settings={settings} onUpdate={onSettingsUpdate} />
              </TabsContent>
            </Tabs>
          </div>
        )}
      </div>

      {/* Hidden file input for image uploads */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        className="hidden"
        onChange={(e) => {
          const file = e.target.files?.[0];
          if (file && currentSlide) {
            handleImageUpload(currentSlide.id, file);
          }
        }}
      />
    </div>
  );
};