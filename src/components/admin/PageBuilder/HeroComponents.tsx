import React, { useState, useRef } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Upload, 
  Trash2, 
  Move, 
  RotateCcw,
  Play,
  Pause,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { HeroSlide, HeroLayer, HeroSettings } from './HeroEditor';

// Hero Canvas Component
export const HeroCanvas: React.FC<{
  slide?: HeroSlide;
  selectedLayer: string | null;
  onLayerSelect: (layerId: string) => void;
  onLayerUpdate: (layerId: string, updates: Partial<HeroLayer>) => void;
}> = ({ slide, selectedLayer, onLayerSelect, onLayerUpdate }) => {
  const canvasRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState<string | null>(null);
  const [dragStart, setDragStart] = useState<{ x: number; y: number } | null>(null);

  if (!slide) {
    return (
      <div className="w-full h-96 bg-gray-200 rounded-lg flex items-center justify-center">
        <p className="text-gray-500">Select a slide to edit</p>
      </div>
    );
  }

  const handleMouseDown = (e: React.MouseEvent, layerId: string) => {
    e.preventDefault();
    setIsDragging(layerId);
    setDragStart({ x: e.clientX, y: e.clientY });
    onLayerSelect(layerId);
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging || !dragStart) return;

    const layer = slide.layers.find(l => l.id === isDragging);
    if (!layer) return;

    const deltaX = e.clientX - dragStart.x;
    const deltaY = e.clientY - dragStart.y;

    onLayerUpdate(isDragging, {
      position: {
        ...layer.position,
        x: Math.max(0, layer.position.x + deltaX),
        y: Math.max(0, layer.position.y + deltaY)
      }
    });

    setDragStart({ x: e.clientX, y: e.clientY });
  };

  const handleMouseUp = () => {
    setIsDragging(null);
    setDragStart(null);
  };

  const renderLayer = (layer: HeroLayer) => {
    const isSelected = selectedLayer === layer.id;

    let content;
    switch (layer.type) {
      case 'text':
        content = (
          <div 
            className="p-2 select-none"
            style={{
              fontSize: layer.styles.fontSize,
              fontWeight: layer.styles.fontWeight,
              color: layer.styles.color,
              textAlign: layer.styles.textAlign,
              backgroundColor: layer.styles.backgroundColor,
              borderRadius: layer.styles.borderRadius,
              padding: layer.styles.padding
            }}
          >
            {layer.content.text || 'Text Layer'}
          </div>
        );
        break;
      case 'button':
        content = (
          <Button 
            variant={layer.content.variant || 'default'}
            style={{
              backgroundColor: layer.styles.backgroundColor,
              color: layer.styles.color,
              borderRadius: layer.styles.borderRadius
            }}
          >
            {layer.content.text || 'Button'}
          </Button>
        );
        break;
      case 'image':
        content = (
          <img 
            src={layer.content.src || '/placeholder.svg'} 
            alt={layer.content.alt || 'Layer Image'}
            className="w-full h-full object-cover"
            style={{ borderRadius: layer.styles.borderRadius }}
          />
        );
        break;
      case 'shape':
        content = (
          <div 
            className="w-full h-full"
            style={{
              backgroundColor: layer.content.color || layer.styles.backgroundColor,
              borderRadius: layer.styles.borderRadius
            }}
          />
        );
        break;
      default:
        content = <div>Unknown Layer</div>;
    }

    return (
      <div
        key={layer.id}
        className={cn(
          'absolute cursor-move border-2 border-transparent',
          isSelected && 'border-blue-500',
          !layer.visible && 'opacity-50'
        )}
        style={{
          left: layer.position.x,
          top: layer.position.y,
          width: layer.position.width,
          height: layer.position.height,
          zIndex: layer.zIndex
        }}
        onMouseDown={(e) => handleMouseDown(e, layer.id)}
        onClick={() => onLayerSelect(layer.id)}
      >
        {content}
        {isSelected && (
          <div className="absolute -top-6 left-0 bg-blue-500 text-white px-2 py-1 rounded text-xs">
            {layer.type}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="w-full max-w-4xl mx-auto">
      <div
        ref={canvasRef}
        className="relative w-full h-96 bg-cover bg-center bg-no-repeat rounded-lg overflow-hidden border"
        style={{ 
          backgroundImage: `url(${slide.imageUrl})`,
          backgroundColor: slide.settings.overlay.enabled 
            ? `${slide.settings.overlay.color}${Math.round(slide.settings.overlay.opacity * 255).toString(16).padStart(2, '0')}`
            : 'transparent'
        }}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
      >
        {/* Overlay */}
        {slide.settings.overlay.enabled && (
          <div 
            className="absolute inset-0"
            style={{
              backgroundColor: slide.settings.overlay.color,
              opacity: slide.settings.overlay.opacity
            }}
          />
        )}

        {/* Layers */}
        {slide.layers
          .filter(layer => layer.visible)
          .sort((a, b) => a.zIndex - b.zIndex)
          .map(renderLayer)}

        {/* Default Title and Subtitle if no text layers */}
        {slide.layers.filter(l => l.type === 'text').length === 0 && (
          <div className="absolute inset-0 flex flex-col items-center justify-center text-white">
            <h1 className="text-4xl font-bold mb-4">{slide.title}</h1>
            <p className="text-xl">{slide.subtitle}</p>
          </div>
        )}
      </div>
    </div>
  );
};

// Hero Preview Component
export const HeroPreview: React.FC<{
  slides: HeroSlide[];
  settings: HeroSettings;
  currentSlide: number;
  onSlideChange: (index: number) => void;
}> = ({ slides, settings, currentSlide, onSlideChange }) => {
  const [isPlaying, setIsPlaying] = useState(settings.autoplay);

  React.useEffect(() => {
    if (!isPlaying || slides.length <= 1) return;

    const interval = setInterval(() => {
      onSlideChange((currentSlide + 1) % slides.length);
    }, settings.autoplaySpeed);

    return () => clearInterval(interval);
  }, [isPlaying, currentSlide, slides.length, settings.autoplaySpeed, onSlideChange]);

  const nextSlide = () => {
    onSlideChange((currentSlide + 1) % slides.length);
  };

  const prevSlide = () => {
    onSlideChange(currentSlide === 0 ? slides.length - 1 : currentSlide - 1);
  };

  if (slides.length === 0) {
    return (
      <div className="w-full h-96 bg-gray-200 rounded-lg flex items-center justify-center">
        <p className="text-gray-500">No active slides to preview</p>
      </div>
    );
  }

  const slide = slides[currentSlide];

  return (
    <div className="w-full max-w-4xl mx-auto relative">
      <div
        className="relative w-full rounded-lg overflow-hidden"
        style={{ 
          height: settings.height.desktop,
          backgroundImage: `url(${slide.imageUrl})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center'
        }}
        onMouseEnter={() => settings.pauseOnHover && setIsPlaying(false)}
        onMouseLeave={() => settings.pauseOnHover && settings.autoplay && setIsPlaying(true)}
      >
        {/* Overlay */}
        {slide.settings.overlay.enabled && (
          <div 
            className="absolute inset-0"
            style={{
              backgroundColor: slide.settings.overlay.color,
              opacity: slide.settings.overlay.opacity
            }}
          />
        )}

        {/* Layers */}
        {slide.layers
          .filter(layer => layer.visible)
          .sort((a, b) => a.zIndex - b.zIndex)
          .map((layer) => (
            <div
              key={layer.id}
              className="absolute"
              style={{
                left: layer.position.x,
                top: layer.position.y,
                width: layer.position.width,
                height: layer.position.height,
                zIndex: layer.zIndex
              }}
            >
              {layer.type === 'text' && (
                <div 
                  style={{
                    fontSize: layer.styles.fontSize,
                    fontWeight: layer.styles.fontWeight,
                    color: layer.styles.color,
                    textAlign: layer.styles.textAlign,
                    backgroundColor: layer.styles.backgroundColor,
                    borderRadius: layer.styles.borderRadius,
                    padding: layer.styles.padding
                  }}
                >
                  {layer.content.text}
                </div>
              )}
              {layer.type === 'button' && (
                <Button 
                  variant={layer.content.variant}
                  style={{
                    backgroundColor: layer.styles.backgroundColor,
                    color: layer.styles.color,
                    borderRadius: layer.styles.borderRadius
                  }}
                >
                  {layer.content.text}
                </Button>
              )}
              {layer.type === 'image' && (
                <img 
                  src={layer.content.src} 
                  alt={layer.content.alt}
                  className="w-full h-full object-cover"
                  style={{ borderRadius: layer.styles.borderRadius }}
                />
              )}
            </div>
          ))}

        {/* Default content if no layers */}
        {slide.layers.length === 0 && (
          <div className="absolute inset-0 flex flex-col items-center justify-center text-white">
            <h1 className="text-4xl font-bold mb-4">{slide.title}</h1>
            <p className="text-xl">{slide.subtitle}</p>
          </div>
        )}

        {/* Navigation Arrows */}
        {settings.showArrows && slides.length > 1 && (
          <>
            <Button
              variant="ghost"
              size="sm"
              className="absolute left-4 top-1/2 transform -translate-y-1/2 text-white hover:bg-white/20"
              onClick={prevSlide}
            >
              <ChevronLeft className="w-6 h-6" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white hover:bg-white/20"
              onClick={nextSlide}
            >
              <ChevronRight className="w-6 h-6" />
            </Button>
          </>
        )}

        {/* Play/Pause Button */}
        <Button
          variant="ghost"
          size="sm"
          className="absolute top-4 right-4 text-white hover:bg-white/20"
          onClick={() => setIsPlaying(!isPlaying)}
        >
          {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
        </Button>
      </div>

      {/* Dots Navigation */}
      {settings.showDots && slides.length > 1 && (
        <div className="flex justify-center mt-4 gap-2">
          {slides.map((_, index) => (
            <button
              key={index}
              className={cn(
                'w-3 h-3 rounded-full transition-colors',
                index === currentSlide ? 'bg-blue-500' : 'bg-gray-300'
              )}
              onClick={() => onSlideChange(index)}
            />
          ))}
        </div>
      )}
    </div>
  );
};

// Slide Properties Panel
export const SlideProperties: React.FC<{
  slide: HeroSlide;
  onUpdate: (updates: Partial<HeroSlide>) => void;
}> = ({ slide, onUpdate }) => {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleImageUpload = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const imageUrl = e.target?.result as string;
      onUpdate({ imageUrl });
    };
    reader.readAsDataURL(file);
  };

  return (
    <div className="space-y-4">
      <div>
        <Label>Slide Title</Label>
        <Input
          value={slide.title}
          onChange={(e) => onUpdate({ title: e.target.value })}
          placeholder="Enter slide title"
        />
      </div>

      <div>
        <Label>Slide Subtitle</Label>
        <Textarea
          value={slide.subtitle}
          onChange={(e) => onUpdate({ subtitle: e.target.value })}
          placeholder="Enter slide subtitle"
          rows={2}
        />
      </div>

      <div>
        <Label>Background Image</Label>
        <div className="space-y-2">
          <Input
            value={slide.imageUrl}
            onChange={(e) => onUpdate({ imageUrl: e.target.value })}
            placeholder="Enter image URL"
          />
          <Button
            variant="outline"
            onClick={() => fileInputRef.current?.click()}
            className="w-full"
          >
            <Upload className="w-4 h-4 mr-2" />
            Upload Image
          </Button>
        </div>
      </div>

      <div>
        <Label>Slide Duration (ms)</Label>
        <Input
          type="number"
          value={slide.settings.duration}
          onChange={(e) => onUpdate({
            settings: {
              ...slide.settings,
              duration: parseInt(e.target.value) || 5000
            }
          })}
        />
      </div>

      <div>
        <Label>Transition Effect</Label>
        <Select
          value={slide.settings.transition}
          onValueChange={(value: any) => onUpdate({
            settings: {
              ...slide.settings,
              transition: value
            }
          })}
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="fade">Fade</SelectItem>
            <SelectItem value="slide">Slide</SelectItem>
            <SelectItem value="zoom">Zoom</SelectItem>
            <SelectItem value="none">None</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Label>Overlay</Label>
          <Switch
            checked={slide.settings.overlay.enabled}
            onCheckedChange={(enabled) => onUpdate({
              settings: {
                ...slide.settings,
                overlay: {
                  ...slide.settings.overlay,
                  enabled
                }
              }
            })}
          />
        </div>

        {slide.settings.overlay.enabled && (
          <div className="space-y-2 pl-4">
            <div>
              <Label>Overlay Color</Label>
              <Input
                type="color"
                value={slide.settings.overlay.color}
                onChange={(e) => onUpdate({
                  settings: {
                    ...slide.settings,
                    overlay: {
                      ...slide.settings.overlay,
                      color: e.target.value
                    }
                  }
                })}
              />
            </div>
            <div>
              <Label>Opacity: {Math.round(slide.settings.overlay.opacity * 100)}%</Label>
              <Slider
                value={[slide.settings.overlay.opacity]}
                onValueChange={([value]) => onUpdate({
                  settings: {
                    ...slide.settings,
                    overlay: {
                      ...slide.settings.overlay,
                      opacity: value
                    }
                  }
                })}
                min={0}
                max={1}
                step={0.1}
                className="mt-2"
              />
            </div>
          </div>
        )}
      </div>

      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        className="hidden"
        onChange={(e) => {
          const file = e.target.files?.[0];
          if (file) handleImageUpload(file);
        }}
      />
    </div>
  );
};

// Layer Properties Panel
export const LayerProperties: React.FC<{
  layer: HeroLayer;
  onUpdate: (updates: Partial<HeroLayer>) => void;
  onDelete: () => void;
}> = ({ layer, onUpdate, onDelete }) => {
  const updateStyles = (styleUpdates: any) => {
    onUpdate({
      styles: {
        ...layer.styles,
        ...styleUpdates
      }
    });
  };

  const updateContent = (contentUpdates: any) => {
    onUpdate({
      content: {
        ...layer.content,
        ...contentUpdates
      }
    });
  };

  const updatePosition = (positionUpdates: any) => {
    onUpdate({
      position: {
        ...layer.position,
        ...positionUpdates
      }
    });
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="font-medium">Layer Properties</h4>
        <Button variant="destructive" size="sm" onClick={onDelete}>
          <Trash2 className="w-4 h-4" />
        </Button>
      </div>

      {/* Content Properties */}
      <div className="space-y-2">
        <Label>Content</Label>
        {layer.type === 'text' && (
          <Textarea
            value={layer.content.text || ''}
            onChange={(e) => updateContent({ text: e.target.value })}
            placeholder="Enter text content"
            rows={3}
          />
        )}
        {layer.type === 'button' && (
          <div className="space-y-2">
            <Input
              value={layer.content.text || ''}
              onChange={(e) => updateContent({ text: e.target.value })}
              placeholder="Button text"
            />
            <Input
              value={layer.content.href || ''}
              onChange={(e) => updateContent({ href: e.target.value })}
              placeholder="Button link"
            />
            <Select
              value={layer.content.variant || 'default'}
              onValueChange={(value) => updateContent({ variant: value })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="default">Default</SelectItem>
                <SelectItem value="destructive">Destructive</SelectItem>
                <SelectItem value="outline">Outline</SelectItem>
                <SelectItem value="secondary">Secondary</SelectItem>
                <SelectItem value="ghost">Ghost</SelectItem>
                <SelectItem value="link">Link</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}
        {layer.type === 'image' && (
          <div className="space-y-2">
            <Input
              value={layer.content.src || ''}
              onChange={(e) => updateContent({ src: e.target.value })}
              placeholder="Image URL"
            />
            <Input
              value={layer.content.alt || ''}
              onChange={(e) => updateContent({ alt: e.target.value })}
              placeholder="Alt text"
            />
          </div>
        )}
      </div>

      {/* Position Properties */}
      <div className="space-y-2">
        <Label>Position & Size</Label>
        <div className="grid grid-cols-2 gap-2">
          <div>
            <Label className="text-xs">X Position</Label>
            <Input
              type="number"
              value={layer.position.x}
              onChange={(e) => updatePosition({ x: parseInt(e.target.value) || 0 })}
            />
          </div>
          <div>
            <Label className="text-xs">Y Position</Label>
            <Input
              type="number"
              value={layer.position.y}
              onChange={(e) => updatePosition({ y: parseInt(e.target.value) || 0 })}
            />
          </div>
          <div>
            <Label className="text-xs">Width</Label>
            <Input
              type="number"
              value={layer.position.width}
              onChange={(e) => updatePosition({ width: parseInt(e.target.value) || 100 })}
            />
          </div>
          <div>
            <Label className="text-xs">Height</Label>
            <Input
              type="number"
              value={layer.position.height}
              onChange={(e) => updatePosition({ height: parseInt(e.target.value) || 50 })}
            />
          </div>
        </div>
      </div>

      {/* Style Properties */}
      <div className="space-y-2">
        <Label>Styling</Label>
        {(layer.type === 'text' || layer.type === 'button') && (
          <div className="space-y-2">
            <div>
              <Label className="text-xs">Font Size</Label>
              <Input
                value={layer.styles.fontSize || '16px'}
                onChange={(e) => updateStyles({ fontSize: e.target.value })}
                placeholder="16px"
              />
            </div>
            <div>
              <Label className="text-xs">Font Weight</Label>
              <Select
                value={layer.styles.fontWeight || 'normal'}
                onValueChange={(value) => updateStyles({ fontWeight: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="normal">Normal</SelectItem>
                  <SelectItem value="bold">Bold</SelectItem>
                  <SelectItem value="lighter">Lighter</SelectItem>
                  <SelectItem value="bolder">Bolder</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label className="text-xs">Text Color</Label>
              <Input
                type="color"
                value={layer.styles.color || '#ffffff'}
                onChange={(e) => updateStyles({ color: e.target.value })}
              />
            </div>
            <div>
              <Label className="text-xs">Text Align</Label>
              <Select
                value={layer.styles.textAlign || 'center'}
                onValueChange={(value) => updateStyles({ textAlign: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="left">Left</SelectItem>
                  <SelectItem value="center">Center</SelectItem>
                  <SelectItem value="right">Right</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        )}

        <div>
          <Label className="text-xs">Background Color</Label>
          <Input
            type="color"
            value={layer.styles.backgroundColor || '#transparent'}
            onChange={(e) => updateStyles({ backgroundColor: e.target.value })}
          />
        </div>

        <div>
          <Label className="text-xs">Border Radius</Label>
          <Input
            value={layer.styles.borderRadius || '0px'}
            onChange={(e) => updateStyles({ borderRadius: e.target.value })}
            placeholder="0px"
          />
        </div>

        <div>
          <Label className="text-xs">Padding</Label>
          <Input
            value={layer.styles.padding || '8px'}
            onChange={(e) => updateStyles({ padding: e.target.value })}
            placeholder="8px"
          />
        </div>
      </div>

      {/* Animation Properties */}
      <div className="space-y-2">
        <Label>Animation</Label>
        <div>
          <Label className="text-xs">Animation Type</Label>
          <Select
            value={layer.styles.animation?.type || 'fadeIn'}
            onValueChange={(value) => updateStyles({
              animation: {
                ...layer.styles.animation,
                type: value
              }
            })}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="none">None</SelectItem>
              <SelectItem value="fadeIn">Fade In</SelectItem>
              <SelectItem value="slideUp">Slide Up</SelectItem>
              <SelectItem value="slideDown">Slide Down</SelectItem>
              <SelectItem value="slideLeft">Slide Left</SelectItem>
              <SelectItem value="slideRight">Slide Right</SelectItem>
              <SelectItem value="zoomIn">Zoom In</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label className="text-xs">Duration (ms)</Label>
          <Input
            type="number"
            value={layer.styles.animation?.duration || 1000}
            onChange={(e) => updateStyles({
              animation: {
                ...layer.styles.animation,
                duration: parseInt(e.target.value) || 1000
              }
            })}
          />
        </div>

        <div>
          <Label className="text-xs">Delay (ms)</Label>
          <Input
            type="number"
            value={layer.styles.animation?.delay || 0}
            onChange={(e) => updateStyles({
              animation: {
                ...layer.styles.animation,
                delay: parseInt(e.target.value) || 0
              }
            })}
          />
        </div>
      </div>

      {/* Layer Controls */}
      <div className="space-y-2">
        <Label>Layer Controls</Label>
        <div className="flex items-center justify-between">
          <Label className="text-xs">Visible</Label>
          <Switch
            checked={layer.visible}
            onCheckedChange={(visible) => onUpdate({ visible })}
          />
        </div>

        <div>
          <Label className="text-xs">Z-Index</Label>
          <Input
            type="number"
            value={layer.zIndex}
            onChange={(e) => onUpdate({ zIndex: parseInt(e.target.value) || 1 })}
          />
        </div>
      </div>
    </div>
  );
};

// Hero Settings Panel
export const HeroSettingsPanel: React.FC<{
  settings: HeroSettings;
  onUpdate: (updates: Partial<HeroSettings>) => void;
}> = ({ settings, onUpdate }) => {
  return (
    <div className="space-y-4">
      <h4 className="font-medium">Hero Settings</h4>

      {/* Autoplay Settings */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Label>Autoplay</Label>
          <Switch
            checked={settings.autoplay}
            onCheckedChange={(autoplay) => onUpdate({ autoplay })}
          />
        </div>

        {settings.autoplay && (
          <div>
            <Label className="text-xs">Autoplay Speed (ms)</Label>
            <Input
              type="number"
              value={settings.autoplaySpeed}
              onChange={(e) => onUpdate({ autoplaySpeed: parseInt(e.target.value) || 5000 })}
            />
          </div>
        )}

        <div className="flex items-center justify-between">
          <Label>Pause on Hover</Label>
          <Switch
            checked={settings.pauseOnHover}
            onCheckedChange={(pauseOnHover) => onUpdate({ pauseOnHover })}
          />
        </div>
      </div>

      {/* Navigation Settings */}
      <div className="space-y-2">
        <Label>Navigation</Label>
        <div className="flex items-center justify-between">
          <Label className="text-xs">Show Dots</Label>
          <Switch
            checked={settings.showDots}
            onCheckedChange={(showDots) => onUpdate({ showDots })}
          />
        </div>

        <div className="flex items-center justify-between">
          <Label className="text-xs">Show Arrows</Label>
          <Switch
            checked={settings.showArrows}
            onCheckedChange={(showArrows) => onUpdate({ showArrows })}
          />
        </div>

        <div className="flex items-center justify-between">
          <Label className="text-xs">Infinite Loop</Label>
          <Switch
            checked={settings.infinite}
            onCheckedChange={(infinite) => onUpdate({ infinite })}
          />
        </div>
      </div>

      {/* Height Settings */}
      <div className="space-y-2">
        <Label>Height Settings</Label>
        <div>
          <Label className="text-xs">Desktop Height</Label>
          <Input
            value={settings.height.desktop}
            onChange={(e) => onUpdate({
              height: {
                ...settings.height,
                desktop: e.target.value
              }
            })}
            placeholder="500px"
          />
        </div>

        <div>
          <Label className="text-xs">Tablet Height</Label>
          <Input
            value={settings.height.tablet}
            onChange={(e) => onUpdate({
              height: {
                ...settings.height,
                tablet: e.target.value
              }
            })}
            placeholder="400px"
          />
        </div>

        <div>
          <Label className="text-xs">Mobile Height</Label>
          <Input
            value={settings.height.mobile}
            onChange={(e) => onUpdate({
              height: {
                ...settings.height,
                mobile: e.target.value
              }
            })}
            placeholder="300px"
          />
        </div>
      </div>
    </div>
  );
};
