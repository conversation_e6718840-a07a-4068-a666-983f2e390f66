import React, { useState, useCallback, useRef, useEffect } from 'react';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  Plus, 
  Trash2, 
  Edit, 
  Eye, 
  Save, 
  Undo, 
  Redo, 
  Copy, 
  Move,
  Settings,
  Layers,
  Smartphone,
  Tablet,
  Monitor,
  Palette,
  Type,
  Image as ImageIcon,
  Layout,
  MousePointer
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useContent } from '@/contexts/ContentContext';

// Page Builder Types
export interface PageElement {
  id: string;
  type: 'text' | 'image' | 'button' | 'section' | 'hero' | 'gallery' | 'form' | 'spacer';
  content: any;
  styles: {
    position?: { x: number; y: number };
    size?: { width: string; height: string };
    spacing?: { margin: string; padding: string };
    background?: { color: string; image?: string; gradient?: string };
    typography?: { fontSize: string; fontWeight: string; color: string; fontFamily: string };
    border?: { width: string; color: string; radius: string };
    animation?: { type: string; duration: string; delay: string };
  };
  responsive?: {
    desktop: any;
    tablet: any;
    mobile: any;
  };
  children?: PageElement[];
}

export interface PageStructure {
  id: string;
  name: string;
  elements: PageElement[];
  settings: {
    seo: {
      title: string;
      description: string;
      keywords: string;
    };
    layout: {
      maxWidth: string;
      background: string;
    };
  };
}

// Device Types for Responsive Editing
type DeviceType = 'desktop' | 'tablet' | 'mobile';

// Drag and Drop Item Types
const ItemTypes = {
  ELEMENT: 'element',
  WIDGET: 'widget'
};

// Widget Library
const WIDGET_LIBRARY = [
  { id: 'text', name: 'Text Block', icon: Type, category: 'basic' },
  { id: 'image', name: 'Image', icon: ImageIcon, category: 'basic' },
  { id: 'button', name: 'Button', icon: MousePointer, category: 'basic' },
  { id: 'section', name: 'Section', icon: Layout, category: 'layout' },
  { id: 'hero', name: 'Hero Banner', icon: ImageIcon, category: 'temple' },
  { id: 'gallery', name: 'Gallery', icon: ImageIcon, category: 'temple' },
  { id: 'form', name: 'Contact Form', icon: Edit, category: 'interactive' },
  { id: 'spacer', name: 'Spacer', icon: Move, category: 'layout' }
];

// Draggable Widget Component
const DraggableWidget: React.FC<{ widget: any }> = ({ widget }) => {
  const [{ isDragging }, drag] = useDrag({
    type: ItemTypes.WIDGET,
    item: { type: widget.id, widget },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const IconComponent = widget.icon;

  return (
    <div
      ref={drag}
      className={cn(
        'p-3 border rounded-lg cursor-move hover:bg-muted/50 transition-colors',
        isDragging && 'opacity-50'
      )}
    >
      <div className="flex items-center gap-2">
        <IconComponent className="w-4 h-4" />
        <span className="text-sm font-medium">{widget.name}</span>
      </div>
    </div>
  );
};

// Droppable Canvas Area
const DropCanvas: React.FC<{ 
  elements: PageElement[]; 
  onDrop: (item: any, position: { x: number; y: number }) => void;
  onElementSelect: (element: PageElement) => void;
  selectedElement: PageElement | null;
}> = ({ elements, onDrop, onElementSelect, selectedElement }) => {
  const [{ isOver }, drop] = useDrop({
    accept: [ItemTypes.WIDGET, ItemTypes.ELEMENT],
    drop: (item: any, monitor) => {
      const offset = monitor.getClientOffset();
      if (offset) {
        onDrop(item, { x: offset.x, y: offset.y });
      }
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
    }),
  });

  return (
    <div
      ref={drop}
      className={cn(
        'min-h-[600px] bg-white border-2 border-dashed border-muted relative overflow-auto',
        isOver && 'border-blue-500 bg-blue-50'
      )}
    >
      {elements.map((element) => (
        <DraggableElement
          key={element.id}
          element={element}
          onSelect={onElementSelect}
          isSelected={selectedElement?.id === element.id}
        />
      ))}
      {elements.length === 0 && (
        <div className="absolute inset-0 flex items-center justify-center text-muted-foreground">
          <div className="text-center">
            <Layout className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p className="text-lg font-medium">Drop widgets here to start building</p>
            <p className="text-sm">Drag elements from the widget library</p>
          </div>
        </div>
      )}
    </div>
  );
};

// Draggable Element Component
const DraggableElement: React.FC<{
  element: PageElement;
  onSelect: (element: PageElement) => void;
  isSelected: boolean;
}> = ({ element, onSelect, isSelected }) => {
  const [{ isDragging }, drag] = useDrag({
    type: ItemTypes.ELEMENT,
    item: { type: 'element', element },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const renderElementContent = () => {
    switch (element.type) {
      case 'text':
        return (
          <div 
            className="p-4 border rounded"
            style={element.styles.typography}
          >
            {element.content.text || 'Text Block'}
          </div>
        );
      case 'image':
        return (
          <div className="border rounded overflow-hidden">
            <img 
              src={element.content.src || '/placeholder.svg'} 
              alt={element.content.alt || 'Image'}
              className="w-full h-32 object-cover"
            />
          </div>
        );
      case 'button':
        return (
          <Button 
            variant={element.content.variant || 'default'}
            style={element.styles.typography}
          >
            {element.content.text || 'Button'}
          </Button>
        );
      case 'section':
        return (
          <div 
            className="min-h-[100px] border-2 border-dashed border-muted rounded p-4"
            style={element.styles.background}
          >
            <p className="text-muted-foreground text-center">Section Container</p>
          </div>
        );
      default:
        return (
          <div className="p-4 border rounded bg-muted">
            {element.type} Element
          </div>
        );
    }
  };

  return (
    <div
      ref={drag}
      onClick={() => onSelect(element)}
      className={cn(
        'absolute cursor-move',
        isDragging && 'opacity-50',
        isSelected && 'ring-2 ring-blue-500'
      )}
      style={{
        left: element.styles.position?.x || 0,
        top: element.styles.position?.y || 0,
        width: element.styles.size?.width || 'auto',
        height: element.styles.size?.height || 'auto',
      }}
    >
      {renderElementContent()}
      {isSelected && (
        <div className="absolute -top-8 left-0 bg-blue-500 text-white px-2 py-1 rounded text-xs">
          {element.type}
        </div>
      )}
    </div>
  );
};

// Property Panel Component
const PropertyPanel: React.FC<{
  element: PageElement | null;
  onUpdate: (updates: Partial<PageElement>) => void;
  device: DeviceType;
}> = ({ element, onUpdate, device }) => {
  if (!element) {
    return (
      <div className="p-4 text-center text-muted-foreground">
        <Settings className="w-8 h-8 mx-auto mb-2 opacity-50" />
        <p>Select an element to edit its properties</p>
      </div>
    );
  }

  const updateStyles = (styleUpdates: any) => {
    onUpdate({
      styles: {
        ...element.styles,
        ...styleUpdates
      }
    });
  };

  const updateContent = (contentUpdates: any) => {
    onUpdate({
      content: {
        ...element.content,
        ...contentUpdates
      }
    });
  };

  return (
    <div className="p-4 space-y-4">
      <div className="flex items-center gap-2 mb-4">
        <Badge variant="outline">{element.type}</Badge>
        <Badge variant="secondary">{device}</Badge>
      </div>

      <Tabs defaultValue="content" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="content">Content</TabsTrigger>
          <TabsTrigger value="style">Style</TabsTrigger>
          <TabsTrigger value="advanced">Advanced</TabsTrigger>
        </TabsList>

        <TabsContent value="content" className="space-y-4">
          {element.type === 'text' && (
            <div className="space-y-2">
              <label className="text-sm font-medium">Text Content</label>
              <Textarea
                value={element.content.text || ''}
                onChange={(e) => updateContent({ text: e.target.value })}
                placeholder="Enter text content..."
              />
            </div>
          )}

          {element.type === 'image' && (
            <div className="space-y-2">
              <label className="text-sm font-medium">Image URL</label>
              <Input
                value={element.content.src || ''}
                onChange={(e) => updateContent({ src: e.target.value })}
                placeholder="Enter image URL..."
              />
              <label className="text-sm font-medium">Alt Text</label>
              <Input
                value={element.content.alt || ''}
                onChange={(e) => updateContent({ alt: e.target.value })}
                placeholder="Enter alt text..."
              />
            </div>
          )}

          {element.type === 'button' && (
            <div className="space-y-2">
              <label className="text-sm font-medium">Button Text</label>
              <Input
                value={element.content.text || ''}
                onChange={(e) => updateContent({ text: e.target.value })}
                placeholder="Enter button text..."
              />
              <label className="text-sm font-medium">Link URL</label>
              <Input
                value={element.content.href || ''}
                onChange={(e) => updateContent({ href: e.target.value })}
                placeholder="Enter link URL..."
              />
            </div>
          )}
        </TabsContent>

        <TabsContent value="style" className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Typography</label>
            <div className="grid grid-cols-2 gap-2">
              <Input
                placeholder="Font Size"
                value={element.styles.typography?.fontSize || ''}
                onChange={(e) => updateStyles({ 
                  typography: { ...element.styles.typography, fontSize: e.target.value }
                })}
              />
              <Input
                placeholder="Color"
                value={element.styles.typography?.color || ''}
                onChange={(e) => updateStyles({ 
                  typography: { ...element.styles.typography, color: e.target.value }
                })}
              />
            </div>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Background</label>
            <Input
              placeholder="Background Color"
              value={element.styles.background?.color || ''}
              onChange={(e) => updateStyles({ 
                background: { ...element.styles.background, color: e.target.value }
              })}
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Spacing</label>
            <div className="grid grid-cols-2 gap-2">
              <Input
                placeholder="Margin"
                value={element.styles.spacing?.margin || ''}
                onChange={(e) => updateStyles({ 
                  spacing: { ...element.styles.spacing, margin: e.target.value }
                })}
              />
              <Input
                placeholder="Padding"
                value={element.styles.spacing?.padding || ''}
                onChange={(e) => updateStyles({ 
                  spacing: { ...element.styles.spacing, padding: e.target.value }
                })}
              />
            </div>
          </div>
        </TabsContent>

        <TabsContent value="advanced" className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Position</label>
            <div className="grid grid-cols-2 gap-2">
              <Input
                placeholder="X Position"
                type="number"
                value={element.styles.position?.x || 0}
                onChange={(e) => updateStyles({ 
                  position: { ...element.styles.position, x: parseInt(e.target.value) }
                })}
              />
              <Input
                placeholder="Y Position"
                type="number"
                value={element.styles.position?.y || 0}
                onChange={(e) => updateStyles({ 
                  position: { ...element.styles.position, y: parseInt(e.target.value) }
                })}
              />
            </div>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Size</label>
            <div className="grid grid-cols-2 gap-2">
              <Input
                placeholder="Width"
                value={element.styles.size?.width || ''}
                onChange={(e) => updateStyles({ 
                  size: { ...element.styles.size, width: e.target.value }
                })}
              />
              <Input
                placeholder="Height"
                value={element.styles.size?.height || ''}
                onChange={(e) => updateStyles({ 
                  size: { ...element.styles.size, height: e.target.value }
                })}
              />
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

// Main Page Builder Component
export const PageBuilder: React.FC = () => {
  const { content, updateContent } = useContent();
  const [currentPage, setCurrentPage] = useState<PageStructure>({
    id: 'home',
    name: 'Home Page',
    elements: [],
    settings: {
      seo: { title: '', description: '', keywords: '' },
      layout: { maxWidth: '1200px', background: '#ffffff' }
    }
  });
  const [selectedElement, setSelectedElement] = useState<PageElement | null>(null);
  const [device, setDevice] = useState<DeviceType>('desktop');
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [history, setHistory] = useState<PageStructure[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);

  // Generate unique ID for elements
  const generateId = () => `element_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  // Add element to history for undo/redo
  const addToHistory = useCallback((page: PageStructure) => {
    const newHistory = history.slice(0, historyIndex + 1);
    newHistory.push({ ...page });
    setHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);
  }, [history, historyIndex]);

  // Handle widget drop
  const handleDrop = useCallback((item: any, position: { x: number; y: number }) => {
    if (item.type === 'element') return; // Handle element reordering separately

    const newElement: PageElement = {
      id: generateId(),
      type: item.widget.id,
      content: getDefaultContent(item.widget.id),
      styles: {
        position: { x: position.x - 200, y: position.y - 100 }, // Adjust for canvas offset
        size: { width: 'auto', height: 'auto' },
        spacing: { margin: '0', padding: '8px' },
        background: { color: 'transparent' },
        typography: { fontSize: '16px', fontWeight: 'normal', color: '#000000', fontFamily: 'inherit' },
        border: { width: '0', color: 'transparent', radius: '0' }
      }
    };

    const updatedPage = {
      ...currentPage,
      elements: [...currentPage.elements, newElement]
    };

    setCurrentPage(updatedPage);
    addToHistory(updatedPage);
  }, [currentPage, addToHistory]);

  // Get default content for widget types
  const getDefaultContent = (type: string) => {
    switch (type) {
      case 'text':
        return { text: 'Edit this text block' };
      case 'image':
        return { src: '/placeholder.svg', alt: 'Image' };
      case 'button':
        return { text: 'Click Me', href: '#', variant: 'default' };
      case 'section':
        return { background: 'transparent' };
      default:
        return {};
    }
  };

  // Update selected element
  const updateSelectedElement = useCallback((updates: Partial<PageElement>) => {
    if (!selectedElement) return;

    const updatedElements = currentPage.elements.map(el =>
      el.id === selectedElement.id ? { ...el, ...updates } : el
    );

    const updatedPage = {
      ...currentPage,
      elements: updatedElements
    };

    setCurrentPage(updatedPage);
    setSelectedElement({ ...selectedElement, ...updates });
    addToHistory(updatedPage);
  }, [selectedElement, currentPage, addToHistory]);

  // Delete selected element
  const deleteSelectedElement = useCallback(() => {
    if (!selectedElement) return;

    const updatedElements = currentPage.elements.filter(el => el.id !== selectedElement.id);
    const updatedPage = {
      ...currentPage,
      elements: updatedElements
    };

    setCurrentPage(updatedPage);
    setSelectedElement(null);
    addToHistory(updatedPage);
  }, [selectedElement, currentPage, addToHistory]);

  // Undo/Redo functionality
  const undo = useCallback(() => {
    if (historyIndex > 0) {
      setHistoryIndex(historyIndex - 1);
      setCurrentPage(history[historyIndex - 1]);
    }
  }, [history, historyIndex]);

  const redo = useCallback(() => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(historyIndex + 1);
      setCurrentPage(history[historyIndex + 1]);
    }
  }, [history, historyIndex]);

  // Save page
  const savePage = useCallback(async () => {
    try {
      // Here you would save to your content management system
      console.log('Saving page:', currentPage);
      // await updateContent({ pageBuilder: currentPage });
    } catch (error) {
      console.error('Failed to save page:', error);
    }
  }, [currentPage]);

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="h-screen flex flex-col">
        {/* Toolbar */}
        <div className="border-b bg-background p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <h1 className="text-xl font-bold">Visual Page Builder</h1>
              <Badge variant="outline">{currentPage.name}</Badge>
            </div>

            <div className="flex items-center gap-2">
              {/* Device Preview Toggle */}
              <div className="flex items-center border rounded-lg p-1">
                <Button
                  variant={device === 'desktop' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setDevice('desktop')}
                >
                  <Monitor className="w-4 h-4" />
                </Button>
                <Button
                  variant={device === 'tablet' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setDevice('tablet')}
                >
                  <Tablet className="w-4 h-4" />
                </Button>
                <Button
                  variant={device === 'mobile' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setDevice('mobile')}
                >
                  <Smartphone className="w-4 h-4" />
                </Button>
              </div>

              {/* Action Buttons */}
              <Button variant="outline" size="sm" onClick={undo} disabled={historyIndex <= 0}>
                <Undo className="w-4 h-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={redo} disabled={historyIndex >= history.length - 1}>
                <Redo className="w-4 h-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={() => setIsPreviewMode(!isPreviewMode)}>
                <Eye className="w-4 h-4" />
                {isPreviewMode ? 'Edit' : 'Preview'}
              </Button>
              <Button onClick={savePage}>
                <Save className="w-4 h-4 mr-2" />
                Save
              </Button>
            </div>
          </div>
        </div>

        {/* Main Editor Area */}
        <div className="flex-1 flex">
          {/* Widget Library Sidebar */}
          {!isPreviewMode && (
            <div className="w-64 border-r bg-muted/30 p-4 overflow-y-auto">
              <h3 className="font-semibold mb-4 flex items-center gap-2">
                <Plus className="w-4 h-4" />
                Widget Library
              </h3>
              
              <div className="space-y-4">
                {['basic', 'layout', 'temple', 'interactive'].map(category => (
                  <div key={category}>
                    <h4 className="text-sm font-medium text-muted-foreground mb-2 capitalize">
                      {category}
                    </h4>
                    <div className="space-y-2">
                      {WIDGET_LIBRARY.filter(w => w.category === category).map(widget => (
                        <DraggableWidget key={widget.id} widget={widget} />
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Canvas Area */}
          <div className="flex-1 p-4 overflow-auto">
            <div 
              className={cn(
                'mx-auto bg-white shadow-lg',
                device === 'desktop' && 'max-w-full',
                device === 'tablet' && 'max-w-3xl',
                device === 'mobile' && 'max-w-sm'
              )}
            >
              <DropCanvas
                elements={currentPage.elements}
                onDrop={handleDrop}
                onElementSelect={setSelectedElement}
                selectedElement={selectedElement}
              />
            </div>
          </div>

          {/* Properties Panel */}
          {!isPreviewMode && (
            <div className="w-80 border-l bg-muted/30 overflow-y-auto">
              <div className="p-4 border-b">
                <h3 className="font-semibold flex items-center gap-2">
                  <Settings className="w-4 h-4" />
                  Properties
                </h3>
                {selectedElement && (
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={deleteSelectedElement}
                    className="mt-2"
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete Element
                  </Button>
                )}
              </div>
              
              <PropertyPanel
                element={selectedElement}
                onUpdate={updateSelectedElement}
                device={device}
              />
            </div>
          )}
        </div>
      </div>
    </DndProvider>
  );
};
