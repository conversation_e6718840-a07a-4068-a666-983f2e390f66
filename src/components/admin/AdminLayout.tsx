import { useState, useEffect, ReactNode } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useSupabase } from '@/contexts/SupabaseContext';
import { useNotifications } from '@/contexts/NotificationContext';
import { useIsMobile } from '@/hooks/use-mobile';
import { AdminSidebar } from './AdminSidebar';
import { AdminHeader } from './AdminHeader';
import { cn } from '@/lib/utils';
import { ErrorBoundary } from '@/components/ErrorBoundary';

interface AdminLayoutProps {
  children: ReactNode;
}

const getPageTitle = (pathname: string) => {
  const routes: Record<string, { title: string; subtitle?: string }> = {
    '/admin': { title: 'Dashboard', subtitle: 'Overview of your temple management system' },
    '/admin/dynamic': { title: 'Dynamic Content', subtitle: 'Manage website content dynamically' },
    '/admin/events': { title: 'Events', subtitle: 'Manage temple events and yagnams' },
    '/admin/gallery': { title: 'Gallery', subtitle: 'Manage temple photos and media' },
    '/admin/content': { title: 'Content Manager', subtitle: 'Edit pages and content' },
    '/admin/announcements': { title: 'Announcements', subtitle: 'Manage temple announcements' },
    '/admin/messages': { title: 'Messages', subtitle: 'View and respond to messages' },
    '/admin/users': { title: 'Users', subtitle: 'Manage user accounts and permissions' },
    '/admin/analytics': { title: 'Analytics', subtitle: 'Website and user analytics' },
    '/admin/design': { title: 'Design System', subtitle: 'Customize website appearance' },
    '/admin/seo': { title: 'SEO & Meta', subtitle: 'Search engine optimization' },
    '/admin/security': { title: 'Security', subtitle: 'Security settings and monitoring' },
    '/admin/settings': { title: 'Settings', subtitle: 'System configuration and preferences' },
  };

  return routes[pathname] || { title: 'Admin Panel', subtitle: 'Temple management system' };
};

const AdminLayout = ({ children }: AdminLayoutProps) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { user, loading } = useSupabase();
  const { unreadCount } = useNotifications();
  const isMobile = useIsMobile();
  const pageInfo = getPageTitle(location.pathname);

  // Mobile sidebar management
  useEffect(() => {
    if (isMobile) {
      setSidebarCollapsed(true);
      setIsMobileMenuOpen(false);
    }
  }, [isMobile, location.pathname]);

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-temple-gold mx-auto mb-4"></div>
          <p className="text-white text-lg">Loading Admin Panel...</p>
          <p className="text-slate-400 text-sm mt-2">Initializing temple management system</p>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-background">
        {/* Mobile menu overlay */}
        {isMobileMenuOpen && (
          <div 
            className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
            onClick={() => setIsMobileMenuOpen(false)}
          />
        )}

        <div className="flex h-full">
          {/* Sidebar */}
          <div className={cn(
            "fixed lg:relative lg:flex z-50 h-full transition-transform duration-300 ease-in-out",
            isMobileMenuOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0"
          )}>
            <AdminSidebar
              collapsed={sidebarCollapsed}
              onToggle={() => setSidebarCollapsed(!sidebarCollapsed)}
            />
          </div>

          {/* Main Content */}
          <div className="flex-1 flex flex-col min-h-screen">
            <AdminHeader
              title={pageInfo.title}
              subtitle={pageInfo.subtitle}
              onMobileMenuClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            />
            
            <main className="flex-1 overflow-y-auto bg-gradient-to-br from-background via-muted/20 to-background p-4 lg:p-6">
              <div className="container mx-auto max-w-7xl">
                <ErrorBoundary>
                  {children}
                </ErrorBoundary>
              </div>
            </main>
          </div>
        </div>
      </div>
    </ErrorBoundary>
  );
};

export default AdminLayout;
