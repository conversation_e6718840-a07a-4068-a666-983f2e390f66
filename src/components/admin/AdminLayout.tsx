
import { useState, useEffect, ReactNode } from 'react';
import { useNavigate, Link, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import {
  LayoutDashboard,
  CalendarDays,
  ImageIcon,
  FileText,
  Settings,
  MessageSquare,
  Bell,
  LogOut,
  Menu,
  X,
  Users,
  Database,
  BarChart3,
  Palette,
  Globe,
  Shield
} from 'lucide-react';
import { useSupabase } from '@/contexts/SupabaseContext';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import { useIsMobile } from '@/hooks/use-mobile';
import { useNotifications } from '@/contexts/NotificationContext';
import { AdminSidebar } from './AdminSidebar';
import { AdminHeader } from './AdminHeader';

interface AdminLayoutProps {
  children: ReactNode;
}

const getPageTitle = (pathname: string) => {
  const routes: Record<string, { title: string; subtitle?: string }> = {
    '/admin': { title: 'Dashboard', subtitle: 'Overview of your temple management system' },
    '/admin/dynamic': { title: 'Dynamic Content', subtitle: 'Manage website content dynamically' },
    '/admin/events': { title: 'Events', subtitle: 'Manage temple events and yagnams' },
    '/admin/gallery': { title: 'Gallery', subtitle: 'Manage temple photos and media' },
    '/admin/content': { title: 'Content Manager', subtitle: 'Edit pages and content' },
    '/admin/announcements': { title: 'Announcements', subtitle: 'Manage temple announcements' },
    '/admin/messages': { title: 'Messages', subtitle: 'View and respond to messages' },
    '/admin/users': { title: 'Users', subtitle: 'Manage user accounts and permissions' },
    '/admin/analytics': { title: 'Analytics', subtitle: 'Website and user analytics' },
    '/admin/design': { title: 'Design System', subtitle: 'Customize website appearance' },
    '/admin/seo': { title: 'SEO & Meta', subtitle: 'Search engine optimization' },
    '/admin/security': { title: 'Security', subtitle: 'Security settings and monitoring' },
    '/admin/settings': { title: 'Settings', subtitle: 'System configuration and preferences' },
  };

  return routes[pathname] || { title: 'Admin Panel', subtitle: 'Temple management system' };
};

const AdminLayout = ({ children }: AdminLayoutProps) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { user, signOut, loading } = useSupabase();
  const { unreadCount } = useNotifications();
  const isMobile = useIsMobile();

  const pageInfo = getPageTitle(location.pathname);

  // If on mobile, collapse sidebar by default
  useEffect(() => {
    if (isMobile) {
      setSidebarCollapsed(true);
    } else {
      setSidebarCollapsed(false);
    }
  }, [isMobile]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-temple-gold mx-auto mb-4"></div>
          <p className="text-white text-lg">Loading Admin Panel...</p>
          <p className="text-slate-400 text-sm mt-2">Initializing temple management system</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background flex">
      {/* Enhanced Sidebar */}
      <AdminSidebar
        collapsed={sidebarCollapsed}
        onToggle={() => setSidebarCollapsed(!sidebarCollapsed)}
      />

      {/* Main Content */}
      <div className={cn(
        "flex-1 flex flex-col transition-all duration-300 ease-in-out"
      )}>
        {/* Enhanced Header */}
        <AdminHeader
          title={pageInfo.title}
          subtitle={pageInfo.subtitle}
        />

        {/* Page Content with enhanced background */}
        <main className="flex-1 overflow-y-auto bg-gradient-to-br from-background via-muted/20 to-background">
          <div className="container mx-auto p-6">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
};

export default AdminLayout;
