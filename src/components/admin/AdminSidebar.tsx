import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  LayoutDashboard,
  Settings,
  Users,
  Calendar,
  Image,
  MessageSquare,
  Megaphone,
  FileText,
  BarChart3,
  Palette,
  Globe,
  ChevronLeft,
  ChevronRight,
  Sparkles,
  Database,
  Shield,
  Bell,
  Heart,
  CalendarDays,
  Layers,
  TestTube
} from 'lucide-react';

interface AdminSidebarProps {
  collapsed: boolean;
  onToggle: () => void;
}

const menuItems = [
  {
    title: 'Dashboard',
    icon: LayoutDashboard,
    href: '/admin',
    badge: null,
    color: 'text-blue-600'
  },
  {
    title: 'Dynamic Content',
    icon: Database,
    href: '/admin/dynamic',
    badge: 'New',
    color: 'text-purple-600'
  },
  {
    title: 'Visual Page Builder',
    icon: Layers,
    href: '/admin/page-builder',
    badge: 'Pro',
    color: 'text-blue-500'
  },
  {
    title: 'Content Management',
    icon: Database,
    href: '/admin/content-management',
    badge: 'New',
    color: 'text-green-600'
  },
  {
    title: 'Sync Test Panel',
    icon: TestTube,
    href: '/admin/content-sync-test',
    badge: 'Test',
    color: 'text-orange-600'
  },
  {
    title: 'Events',
    icon: Calendar,
    href: '/admin/events',
    badge: null,
    color: 'text-green-600'
  },
  {
    title: 'Advanced Events',
    icon: CalendarDays,
    href: '/admin/events-advanced',
    badge: 'New',
    color: 'text-emerald-600'
  },
  {
    title: 'Gallery',
    icon: Image,
    href: '/admin/gallery',
    badge: null,
    color: 'text-pink-600'
  },
  {
    title: 'Content Manager',
    icon: FileText,
    href: '/admin/content',
    badge: null,
    color: 'text-orange-600'
  },
  {
    title: 'Announcements',
    icon: Megaphone,
    href: '/admin/announcements',
    badge: null,
    color: 'text-red-600'
  },
  {
    title: 'Messages',
    icon: MessageSquare,
    href: '/admin/messages',
    badge: '3',
    color: 'text-indigo-600'
  },
  {
    title: 'Users',
    icon: Users,
    href: '/admin/users',
    badge: null,
    color: 'text-cyan-600'
  },
  {
    title: 'Analytics',
    icon: BarChart3,
    href: '/admin/analytics',
    badge: 'Enhanced',
    color: 'text-emerald-600'
  },
  {
    title: 'Donations',
    icon: Heart,
    href: '/admin/donations',
    badge: 'New',
    color: 'text-rose-600'
  },
  {
    title: 'Design System',
    icon: Palette,
    href: '/admin/design',
    badge: 'Beta',
    color: 'text-violet-600'
  },
  {
    title: 'SEO & Meta',
    icon: Globe,
    href: '/admin/seo',
    badge: null,
    color: 'text-teal-600'
  },
  {
    title: 'Security',
    icon: Shield,
    href: '/admin/security',
    badge: null,
    color: 'text-slate-600'
  },
  {
    title: 'Settings',
    icon: Settings,
    href: '/admin/settings',
    badge: null,
    color: 'text-gray-600'
  }
];

export const AdminSidebar: React.FC<AdminSidebarProps> = ({ collapsed, onToggle }) => {
  const location = useLocation();
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);

  return (
    <div className={cn(
      "relative h-screen bg-gradient-to-b from-slate-900 via-slate-800 to-slate-900 border-r border-slate-700/50 transition-all duration-300 ease-in-out",
      collapsed ? "w-16" : "w-64"
    )}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-slate-700/50">
        {!collapsed && (
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-br from-temple-gold to-temple-saffron rounded-lg flex items-center justify-center">
              <Sparkles className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-white font-semibold text-sm">Admin Panel</h2>
              <p className="text-slate-400 text-xs">Anantasagar Kshetramu</p>
            </div>
          </div>
        )}
        <Button
          variant="ghost"
          size="sm"
          onClick={onToggle}
          className="text-slate-400 hover:text-white hover:bg-slate-700/50 p-1.5"
        >
          {collapsed ? <ChevronRight className="w-4 h-4" /> : <ChevronLeft className="w-4 h-4" />}
        </Button>
      </div>

      {/* Navigation */}
      <nav className="p-2 space-y-1 overflow-y-auto h-[calc(100vh-80px)] scrollbar-thin scrollbar-thumb-slate-600 scrollbar-track-transparent">
        {menuItems.map((item) => {
          const isActive = location.pathname === item.href;
          const Icon = item.icon;
          
          return (
            <Link
              key={item.href}
              to={item.href}
              onMouseEnter={() => setHoveredItem(item.href)}
              onMouseLeave={() => setHoveredItem(null)}
              className={cn(
                "group relative flex items-center space-x-3 px-3 py-2.5 rounded-lg transition-all duration-200 ease-in-out",
                isActive 
                  ? "bg-gradient-to-r from-temple-gold/20 to-temple-saffron/20 text-temple-gold border border-temple-gold/30" 
                  : "text-slate-300 hover:text-white hover:bg-slate-700/50",
                collapsed && "justify-center px-2"
              )}
            >
              {/* Active indicator */}
              {isActive && (
                <div className="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-temple-gold to-temple-saffron rounded-r-full" />
              )}
              
              {/* Icon */}
              <div className={cn(
                "flex-shrink-0 transition-colors duration-200",
                isActive ? "text-temple-gold" : item.color
              )}>
                <Icon className={cn(
                  "transition-transform duration-200",
                  hoveredItem === item.href && "scale-110",
                  collapsed ? "w-5 h-5" : "w-5 h-5"
                )} />
              </div>
              
              {/* Label and Badge */}
              {!collapsed && (
                <>
                  <span className="flex-1 font-medium text-sm truncate">
                    {item.title}
                  </span>
                  {item.badge && (
                    <Badge 
                      variant={item.badge === 'New' ? 'default' : 'secondary'}
                      className={cn(
                        "text-xs px-1.5 py-0.5 font-medium",
                        item.badge === 'New' 
                          ? "bg-gradient-to-r from-green-500 to-emerald-500 text-white" 
                          : item.badge === 'Beta'
                          ? "bg-gradient-to-r from-purple-500 to-violet-500 text-white"
                          : "bg-red-500 text-white"
                      )}
                    >
                      {item.badge}
                    </Badge>
                  )}
                </>
              )}
              
              {/* Tooltip for collapsed state */}
              {collapsed && (
                <div className="absolute left-full ml-2 px-2 py-1 bg-slate-800 text-white text-xs rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50 border border-slate-600">
                  {item.title}
                  {item.badge && (
                    <span className="ml-1 px-1 py-0.5 bg-red-500 text-white rounded text-xs">
                      {item.badge}
                    </span>
                  )}
                </div>
              )}
            </Link>
          );
        })}
      </nav>

      {/* Footer */}
      {!collapsed && (
        <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-slate-700/50">
          <div className="flex items-center space-x-2 text-slate-400 text-xs">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span>System Online</span>
          </div>
        </div>
      )}

      {/* Gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-transparent to-slate-900/5 pointer-events-none" />
    </div>
  );
};
