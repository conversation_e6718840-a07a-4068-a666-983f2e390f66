import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { 
  LayoutDashboard, 
  Layers, 
  Search, 
  Heart, 
  BarChart3, 
  Shield,
  ArrowRight
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface DashboardCard {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  route: string;
  color: string;
  bgGradient: string;
}

const dashboardCards: DashboardCard[] = [
  {
    id: 'dashboard',
    title: 'Dashboard',
    description: 'Overview and quick stats',
    icon: LayoutDashboard,
    route: '/admin/overview',
    color: 'text-blue-600',
    bgGradient: 'from-blue-50 to-blue-100'
  },
  {
    id: 'page-builder',
    title: 'Page Builder',
    description: 'Design and edit your website',
    icon: Layers,
    route: '/admin/visual-builder',
    color: 'text-purple-600',
    bgGradient: 'from-purple-50 to-purple-100'
  },
  {
    id: 'seo',
    title: 'SEO',
    description: 'Search engine optimization',
    icon: Search,
    route: '/admin/seo',
    color: 'text-green-600',
    bgGradient: 'from-green-50 to-green-100'
  },
  {
    id: 'donations',
    title: 'Donations',
    description: 'Manage donations and payments',
    icon: Heart,
    route: '/admin/donations',
    color: 'text-red-600',
    bgGradient: 'from-red-50 to-red-100'
  },
  {
    id: 'statistics',
    title: 'Statistics',
    description: 'Analytics and insights',
    icon: BarChart3,
    route: '/admin/analytics',
    color: 'text-orange-600',
    bgGradient: 'from-orange-50 to-orange-100'
  },
  {
    id: 'security',
    title: 'Security',
    description: 'Security settings and logs',
    icon: Shield,
    route: '/admin/security',
    color: 'text-gray-600',
    bgGradient: 'from-gray-50 to-gray-100'
  }
];

export const MinimalDashboard: React.FC = () => {
  const navigate = useNavigate();

  const handleCardClick = (route: string) => {
    navigate(route);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 p-6">
      {/* Header */}
      <div className="max-w-7xl mx-auto mb-12">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Admin Dashboard
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Manage your temple website with our intuitive tools
          </p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Visitors</p>
                <p className="text-2xl font-bold text-gray-900">12,345</p>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <BarChart3 className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Pages</p>
                <p className="text-2xl font-bold text-gray-900">8</p>
              </div>
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <Layers className="w-6 h-6 text-purple-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Donations</p>
                <p className="text-2xl font-bold text-gray-900">₹45,678</p>
              </div>
              <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                <Heart className="w-6 h-6 text-red-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Security Score</p>
                <p className="text-2xl font-bold text-gray-900">98%</p>
              </div>
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <Shield className="w-6 h-6 text-green-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Main Dashboard Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {dashboardCards.map((card) => {
            const Icon = card.icon;
            return (
              <Card 
                key={card.id}
                className="group cursor-pointer transition-all duration-300 hover:shadow-xl hover:-translate-y-2 border-0 bg-white overflow-hidden"
                onClick={() => handleCardClick(card.route)}
              >
                <CardContent className="p-0">
                  <div className={`h-32 bg-gradient-to-br ${card.bgGradient} flex items-center justify-center relative overflow-hidden`}>
                    <div className="absolute inset-0 bg-white/20 backdrop-blur-sm"></div>
                    <Icon className={`w-16 h-16 ${card.color} relative z-10 group-hover:scale-110 transition-transform duration-300`} />
                  </div>
                  
                  <div className="p-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                      {card.title}
                    </h3>
                    <p className="text-gray-600 mb-4">
                      {card.description}
                    </p>
                    
                    <Button 
                      variant="ghost" 
                      className="w-full justify-between group-hover:bg-blue-50 group-hover:text-blue-600 transition-colors"
                    >
                      Open {card.title}
                      <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Quick Actions */}
        <div className="mt-12 bg-white rounded-xl shadow-sm border border-gray-200 p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button 
              onClick={() => navigate('/admin/visual-builder')}
              className="h-16 text-lg bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800"
            >
              <Layers className="w-6 h-6 mr-3" />
              Start Building
            </Button>
            
            <Button 
              onClick={() => window.open('/', '_blank')}
              variant="outline"
              className="h-16 text-lg border-2 hover:bg-gray-50"
            >
              <Search className="w-6 h-6 mr-3" />
              Preview Website
            </Button>
            
            <Button 
              onClick={() => navigate('/admin/analytics')}
              variant="outline"
              className="h-16 text-lg border-2 hover:bg-gray-50"
            >
              <BarChart3 className="w-6 h-6 mr-3" />
              View Analytics
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
