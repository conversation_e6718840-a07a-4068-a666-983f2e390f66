
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSupabase } from '@/contexts/SupabaseContext';
import { isAdmin } from '@/utils/authUtils';
import { toast } from 'sonner';

interface AdminAuthGuardProps {
  children: React.ReactNode;
}

const AdminAuthGuard: React.FC<AdminAuthGuardProps> = ({ children }) => {
  const { user, loading } = useSupabase();
  const navigate = useNavigate();
  const [authorized, setAuthorized] = useState<boolean | null>(null);

  useEffect(() => {
    if (!loading) {
      // No user logged in
      if (!user) {
        navigate('/admin/login');
        return;
      }
      
      // Check if user is an admin
      const adminCheck = isAdmin(user);
      setAuthorized(adminCheck);
      
      if (!adminCheck) {
        toast.error('Access denied', {
          description: 'You do not have permission to access the admin dashboard'
        });
        navigate('/admin/login');
      }
    }
  }, [user, loading, navigate]);

  if (loading || authorized === null) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="h-12 w-12 rounded-full border-4 border-temple-gold border-t-transparent animate-spin"></div>
      </div>
    );
  }

  // Only render children when authenticated and authorized
  return authorized ? <>{children}</> : null;
};

export default AdminAuthGuard;
