import React, { useState, useRef, useCallback } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Crop, 
  RotateCcw, 
  ZoomIn, 
  ZoomOut, 
  Palette, 
  Sliders,
  Download,
  Upload,
  Scissors,
  Move,
  Square
} from 'lucide-react';
import { toast } from 'sonner';

interface MediaEditorProps {
  file: File | string;
  type: 'image' | 'video';
  onSave: (editedFile: string | File) => void;
  onClose: () => void;
}

interface ImageFilters {
  brightness: number;
  contrast: number;
  saturation: number;
  blur: number;
  sepia: number;
  grayscale: number;
  hue: number;
}

interface CropArea {
  x: number;
  y: number;
  width: number;
  height: number;
}

export const MediaEditor: React.FC<MediaEditorProps> = ({
  file,
  type,
  onSave,
  onClose
}) => {
  const [imageSrc, setImageSrc] = useState<string>('');
  const [filters, setFilters] = useState<ImageFilters>({
    brightness: 100,
    contrast: 100,
    saturation: 100,
    blur: 0,
    sepia: 0,
    grayscale: 0,
    hue: 0
  });
  const [cropArea, setCropArea] = useState<CropArea>({ x: 0, y: 0, width: 100, height: 100 });
  const [rotation, setRotation] = useState(0);
  const [scale, setScale] = useState(1);
  const [activeTab, setActiveTab] = useState('filters');

  const canvasRef = useRef<HTMLCanvasElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);

  // Initialize image
  React.useEffect(() => {
    if (typeof file === 'string') {
      setImageSrc(file);
    } else {
      const reader = new FileReader();
      reader.onload = (e) => {
        setImageSrc(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  }, [file]);

  // Apply filters to canvas
  const applyFilters = useCallback(() => {
    const canvas = canvasRef.current;
    const image = imageRef.current;
    
    if (!canvas || !image) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    canvas.width = image.naturalWidth;
    canvas.height = image.naturalHeight;

    // Apply transformations
    ctx.save();
    ctx.translate(canvas.width / 2, canvas.height / 2);
    ctx.rotate((rotation * Math.PI) / 180);
    ctx.scale(scale, scale);
    ctx.translate(-canvas.width / 2, -canvas.height / 2);

    // Apply filters
    ctx.filter = `
      brightness(${filters.brightness}%)
      contrast(${filters.contrast}%)
      saturate(${filters.saturation}%)
      blur(${filters.blur}px)
      sepia(${filters.sepia}%)
      grayscale(${filters.grayscale}%)
      hue-rotate(${filters.hue}deg)
    `;

    ctx.drawImage(image, 0, 0);
    ctx.restore();
  }, [filters, rotation, scale]);

  // Update filter
  const updateFilter = useCallback((filterName: keyof ImageFilters, value: number) => {
    setFilters(prev => ({
      ...prev,
      [filterName]: value
    }));
  }, []);

  // Reset filters
  const resetFilters = useCallback(() => {
    setFilters({
      brightness: 100,
      contrast: 100,
      saturation: 100,
      blur: 0,
      sepia: 0,
      grayscale: 0,
      hue: 0
    });
    setRotation(0);
    setScale(1);
  }, []);

  // Crop image
  const cropImage = useCallback(() => {
    const canvas = canvasRef.current;
    const image = imageRef.current;
    
    if (!canvas || !image) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Create new canvas for cropped image
    const croppedCanvas = document.createElement('canvas');
    const croppedCtx = croppedCanvas.getContext('2d');
    
    if (!croppedCtx) return;

    const cropX = (cropArea.x / 100) * image.naturalWidth;
    const cropY = (cropArea.y / 100) * image.naturalHeight;
    const cropWidth = (cropArea.width / 100) * image.naturalWidth;
    const cropHeight = (cropArea.height / 100) * image.naturalHeight;

    croppedCanvas.width = cropWidth;
    croppedCanvas.height = cropHeight;

    croppedCtx.drawImage(
      image,
      cropX, cropY, cropWidth, cropHeight,
      0, 0, cropWidth, cropHeight
    );

    // Update main canvas
    canvas.width = cropWidth;
    canvas.height = cropHeight;
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    ctx.drawImage(croppedCanvas, 0, 0);

    toast.success('Image cropped successfully');
  }, [cropArea]);

  // Save edited image
  const saveImage = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    canvas.toBlob((blob) => {
      if (blob) {
        const editedFile = new File([blob], 'edited-image.png', { type: 'image/png' });
        onSave(editedFile);
        toast.success('Image saved successfully');
      }
    }, 'image/png', 0.9);
  }, [onSave]);

  // Download edited image
  const downloadImage = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const link = document.createElement('a');
    link.download = 'edited-image.png';
    link.href = canvas.toDataURL();
    link.click();
  }, []);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gray-50 px-6 py-4 border-b flex items-center justify-between">
          <h2 className="text-xl font-bold text-gray-900">Media Editor</h2>
          <div className="flex items-center space-x-2">
            <Button variant="outline" onClick={resetFilters}>
              <RotateCcw className="w-4 h-4 mr-2" />
              Reset
            </Button>
            <Button variant="outline" onClick={downloadImage}>
              <Download className="w-4 h-4 mr-2" />
              Download
            </Button>
            <Button onClick={saveImage}>
              <Upload className="w-4 h-4 mr-2" />
              Save
            </Button>
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </div>
        </div>

        <div className="flex h-[calc(90vh-80px)]">
          {/* Sidebar - Controls */}
          <div className="w-80 bg-gray-50 border-r overflow-y-auto">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="filters">Filters</TabsTrigger>
                <TabsTrigger value="crop">Crop</TabsTrigger>
                <TabsTrigger value="transform">Transform</TabsTrigger>
              </TabsList>

              <TabsContent value="filters" className="p-4 space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm flex items-center">
                      <Palette className="w-4 h-4 mr-2" />
                      Color Adjustments
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label>Brightness: {filters.brightness}%</Label>
                      <Slider
                        value={[filters.brightness]}
                        onValueChange={([value]) => updateFilter('brightness', value)}
                        min={0}
                        max={200}
                        step={1}
                        className="mt-2"
                      />
                    </div>

                    <div>
                      <Label>Contrast: {filters.contrast}%</Label>
                      <Slider
                        value={[filters.contrast]}
                        onValueChange={([value]) => updateFilter('contrast', value)}
                        min={0}
                        max={200}
                        step={1}
                        className="mt-2"
                      />
                    </div>

                    <div>
                      <Label>Saturation: {filters.saturation}%</Label>
                      <Slider
                        value={[filters.saturation]}
                        onValueChange={([value]) => updateFilter('saturation', value)}
                        min={0}
                        max={200}
                        step={1}
                        className="mt-2"
                      />
                    </div>

                    <div>
                      <Label>Hue: {filters.hue}°</Label>
                      <Slider
                        value={[filters.hue]}
                        onValueChange={([value]) => updateFilter('hue', value)}
                        min={-180}
                        max={180}
                        step={1}
                        className="mt-2"
                      />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm flex items-center">
                      <Sliders className="w-4 h-4 mr-2" />
                      Effects
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label>Blur: {filters.blur}px</Label>
                      <Slider
                        value={[filters.blur]}
                        onValueChange={([value]) => updateFilter('blur', value)}
                        min={0}
                        max={10}
                        step={0.1}
                        className="mt-2"
                      />
                    </div>

                    <div>
                      <Label>Sepia: {filters.sepia}%</Label>
                      <Slider
                        value={[filters.sepia]}
                        onValueChange={([value]) => updateFilter('sepia', value)}
                        min={0}
                        max={100}
                        step={1}
                        className="mt-2"
                      />
                    </div>

                    <div>
                      <Label>Grayscale: {filters.grayscale}%</Label>
                      <Slider
                        value={[filters.grayscale]}
                        onValueChange={([value]) => updateFilter('grayscale', value)}
                        min={0}
                        max={100}
                        step={1}
                        className="mt-2"
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="crop" className="p-4 space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm flex items-center">
                      <Crop className="w-4 h-4 mr-2" />
                      Crop Settings
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <Label>X Position</Label>
                        <Input
                          type="number"
                          value={cropArea.x}
                          onChange={(e) => setCropArea(prev => ({ ...prev, x: Number(e.target.value) }))}
                          min={0}
                          max={100}
                        />
                      </div>
                      <div>
                        <Label>Y Position</Label>
                        <Input
                          type="number"
                          value={cropArea.y}
                          onChange={(e) => setCropArea(prev => ({ ...prev, y: Number(e.target.value) }))}
                          min={0}
                          max={100}
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <Label>Width</Label>
                        <Input
                          type="number"
                          value={cropArea.width}
                          onChange={(e) => setCropArea(prev => ({ ...prev, width: Number(e.target.value) }))}
                          min={1}
                          max={100}
                        />
                      </div>
                      <div>
                        <Label>Height</Label>
                        <Input
                          type="number"
                          value={cropArea.height}
                          onChange={(e) => setCropArea(prev => ({ ...prev, height: Number(e.target.value) }))}
                          min={1}
                          max={100}
                        />
                      </div>
                    </div>

                    <Button onClick={cropImage} className="w-full">
                      <Scissors className="w-4 h-4 mr-2" />
                      Apply Crop
                    </Button>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="transform" className="p-4 space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm flex items-center">
                      <Move className="w-4 h-4 mr-2" />
                      Transform
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label>Rotation: {rotation}°</Label>
                      <Slider
                        value={[rotation]}
                        onValueChange={([value]) => setRotation(value)}
                        min={-180}
                        max={180}
                        step={1}
                        className="mt-2"
                      />
                    </div>

                    <div>
                      <Label>Scale: {scale.toFixed(2)}x</Label>
                      <Slider
                        value={[scale]}
                        onValueChange={([value]) => setScale(value)}
                        min={0.1}
                        max={3}
                        step={0.1}
                        className="mt-2"
                      />
                    </div>

                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        onClick={() => setScale(prev => Math.min(prev + 0.1, 3))}
                        className="flex-1"
                      >
                        <ZoomIn className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => setScale(prev => Math.max(prev - 0.1, 0.1))}
                        className="flex-1"
                      >
                        <ZoomOut className="w-4 h-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>

          {/* Canvas Area */}
          <div className="flex-1 bg-gray-100 p-4 overflow-auto">
            <div className="bg-white rounded-lg shadow-lg p-4 max-w-full max-h-full overflow-auto">
              {imageSrc && (
                <>
                  <img
                    ref={imageRef}
                    src={imageSrc}
                    alt="Original"
                    className="hidden"
                    onLoad={applyFilters}
                  />
                  <canvas
                    ref={canvasRef}
                    className="max-w-full max-h-full border border-gray-200 rounded"
                    style={{
                      filter: `
                        brightness(${filters.brightness}%)
                        contrast(${filters.contrast}%)
                        saturate(${filters.saturation}%)
                        blur(${filters.blur}px)
                        sepia(${filters.sepia}%)
                        grayscale(${filters.grayscale}%)
                        hue-rotate(${filters.hue}deg)
                      `,
                      transform: `rotate(${rotation}deg) scale(${scale})`
                    }}
                  />
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
