import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { toast } from "sonner";
import { 
  Layout, 
  Image as ImageIcon,
  MessageSquare,
  Map,
  Star,
  Settings as SettingsIcon,
  Share2,
  Menu as MenuIcon
} from "lucide-react";
import { HeroEditor } from './sections/HeroEditor';
import { NavigationEditor } from './sections/NavigationEditor';
import { AnnouncementsEditor } from './sections/AnnouncementsEditor';
import { ReviewsManager } from './sections/ReviewsManager';
import { LocationsManager } from './sections/LocationsManager';
import { SocialMediaManager } from './sections/SocialMediaManager';
import { PopupManager } from './sections/PopupManager';
import { AnalyticsDashboard } from './AnalyticsDashboard';
import AboutEditor from './sections/AboutEditor';
import GalleryManager from './sections/GalleryManager';
import FooterEditor from './sections/FooterEditor';
import { useContent } from '@/contexts/ContentContext';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';

export const DynamicContentManager = () => {
  const [activeTab, setActiveTab] = useState('hero');
  const [showPreview, setShowPreview] = useState(false);
  const [isPublishing, setIsPublishing] = useState(false);
  const { content, updateContent, isLoading, rollbackContent } = useContent();
  const [showHistory, setShowHistory] = useState(false);

  const handlePreview = () => {
    setShowPreview(true);
  };

  const handlePublish = async () => {
    setIsPublishing(true);
    // Simulate API call or publishing logic
    setTimeout(() => {
      setIsPublishing(false);
      toast.success('Content published successfully!');
    }, 1200);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-temple-gold mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading content manager...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Dynamic Content Manager</h1>
          <p className="text-muted-foreground">
            Manage all website content from one place
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={handlePreview} variant="secondary">
            Preview
          </Button>
          <Button onClick={handlePublish} variant="default" disabled={isPublishing}>
            {isPublishing ? 'Publishing...' : 'Publish'}
          </Button>
          <Button onClick={() => window.open('/', '_blank')} variant="outline">
            Preview Website
          </Button>
          <Button onClick={() => setShowHistory(true)} variant="outline">Rollback/History</Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-2 lg:grid-cols-8 w-full gap-4">
          <TabsTrigger value="hero" className="flex items-center gap-2">
            <Layout className="h-4 w-4" />
            Hero Section
          </TabsTrigger>
          <TabsTrigger value="navigation" className="flex items-center gap-2">
            <MenuIcon className="h-4 w-4" />
            Navigation
          </TabsTrigger>
          <TabsTrigger value="announcements" className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            Announcements
          </TabsTrigger>
          <TabsTrigger value="reviews" className="flex items-center gap-2">
            <Star className="h-4 w-4" />
            Reviews
          </TabsTrigger>
          <TabsTrigger value="locations" className="flex items-center gap-2">
            <Map className="h-4 w-4" />
            Locations
          </TabsTrigger>
          <TabsTrigger value="social" className="flex items-center gap-2">
            <Share2 className="h-4 w-4" />
            Social Media
          </TabsTrigger>
          <TabsTrigger value="popups" className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            Popups
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center gap-2">
            <SettingsIcon className="h-4 w-4" />
            Settings
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <span role="img" aria-label="analytics">📊</span>
            Analytics
          </TabsTrigger>
          <TabsTrigger value="about" className="flex items-center gap-2">
            <Layout className="h-4 w-4" />
            About
          </TabsTrigger>
          <TabsTrigger value="gallery" className="flex items-center gap-2">
            <ImageIcon className="h-4 w-4" />
            Gallery
          </TabsTrigger>
          <TabsTrigger value="footer" className="flex items-center gap-2">
            <Layout className="h-4 w-4" />
            Footer
          </TabsTrigger>
        </TabsList>

        <div className="mt-6">
          <TabsContent value="hero">
            <HeroEditor 
              content={content.hero} 
              onUpdate={hero => updateContent({ hero })} 
            />
          </TabsContent>
          <TabsContent value="navigation">
            <NavigationEditor 
              mainMenu={content.navigation.mainMenu}
              footerMenu={content.navigation.footerMenu}
              onUpdate={navigation => updateContent({ navigation })}
            />
          </TabsContent>
          <TabsContent value="announcements">
            <AnnouncementsEditor 
              announcements={content.announcements} 
              onUpdate={announcements => updateContent({ announcements })}
            />
          </TabsContent>
          <TabsContent value="reviews">
            <ReviewsManager 
              reviews={content.reviews} 
              onUpdate={reviews => updateContent({ reviews })}
            />
          </TabsContent>
          <TabsContent value="locations">
            <LocationsManager 
              locations={content.locations} 
              onUpdate={locations => updateContent({ locations })}
            />
          </TabsContent>
          <TabsContent value="social">
            <SocialMediaManager 
              socialMedia={content.socialMedia} 
              onUpdate={socialMedia => updateContent({ socialMedia })}
            />
          </TabsContent>
          <TabsContent value="popups">
            <PopupManager 
              settings={content.settings.popups} 
              onUpdate={popups => updateContent({ settings: { ...content.settings, popups } })}
            />
          </TabsContent>
          <TabsContent value="settings">
            <Card>
              <CardHeader>
                <CardTitle>Global Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between space-x-2">
                  <Label htmlFor="maintenance-mode">Maintenance Mode</Label>
                  <Switch
                    id="maintenance-mode"
                    checked={content.settings.isMaintenanceMode}
                    onCheckedChange={isMaintenanceMode => 
                      updateContent({ settings: { ...content.settings, isMaintenanceMode } })
                    }
                  />
                </div>
                {content.settings.isMaintenanceMode && (
                  <div className="space-y-2">
                    <Label htmlFor="maintenance-message">Maintenance Message</Label>
                    <Input
                      id="maintenance-message"
                      value={content.settings.maintenanceMessage}
                      onChange={e => 
                        updateContent({ settings: { ...content.settings, maintenanceMessage: e.target.value } })
                      }
                    />
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value="analytics">
            <AnalyticsDashboard />
          </TabsContent>
          <TabsContent value="about">
            <AboutEditor about={content.about.sections} onChange={sections => updateContent({ about: { ...content.about, sections } })} />
          </TabsContent>
          <TabsContent value="gallery">
            <GalleryManager gallery={content.gallery} onChange={gallery => updateContent({ gallery })} />
          </TabsContent>
          <TabsContent value="footer">
            <FooterEditor footer={content.footer} onChange={footer => updateContent({ footer })} />
          </TabsContent>
        </div>
      </Tabs>

      <Dialog open={showPreview} onOpenChange={setShowPreview}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Live Preview</DialogTitle>
          </DialogHeader>
          {/* You can render a summary or a live preview of the content here. For now, just show a JSON dump. */}
          <pre className="bg-muted p-4 rounded text-xs overflow-x-auto max-h-[60vh]">{JSON.stringify(content, null, 2)}</pre>
        </DialogContent>
      </Dialog>

      {/* Rollback/History Dialog */}
      <Dialog open={showHistory} onOpenChange={setShowHistory}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Content Version History</DialogTitle>
          </DialogHeader>
          {content?._history && content._historyTimestamps ? (
            <ul className="space-y-2">
              {content._history.map((ver, i) => (
                <li key={i} className="flex items-center justify-between">
                  <span>{content._historyTimestamps[i]}</span>
                  <Button size="sm" variant="outline" onClick={() => { rollbackContent(i); setShowHistory(false); }}>Rollback to this</Button>
                </li>
              ))}
            </ul>
          ) : (
            <div>No history available.</div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};
