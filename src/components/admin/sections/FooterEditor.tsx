import React, { useState } from 'react';
import { FooterContent } from '../../../types/content';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface FooterEditorProps {
  footer: FooterContent;
  onUpdate: (footer: FooterContent) => void;
}

const FooterEditor: React.FC<FooterEditorProps> = ({ footer, onUpdate }) => {
  const [footerState, setFooterState] = useState<FooterContent>(footer);
  const [newLink, setNewLink] = useState({ label: '', url: '' });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFooterState({ ...footerState, [name]: value });
    onUpdate({ ...footerState, [name]: value });
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>, field: 'logo' | 'backgroundImage') => {
    const file = e.target.files?.[0];
    if (!file) return;
    const url = URL.createObjectURL(file);
    setFooterState({ ...footerState, [field]: url });
    onUpdate({ ...footerState, [field]: url });
  };

  const handleLinkChange = (idx: number, e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const updatedLinks = [...footerState.links];
    updatedLinks[idx][name] = value;
    setFooterState({ ...footerState, links: updatedLinks });
    onUpdate({ ...footerState, links: updatedLinks });
  };

  const handleAddLink = () => {
    if (!newLink.label.trim() || !newLink.url.trim()) return;
    const updatedLinks = [...footerState.links, { ...newLink }];
    setFooterState({ ...footerState, links: updatedLinks });
    onUpdate({ ...footerState, links: updatedLinks });
    setNewLink({ label: '', url: '' });
  };

  const handleDeleteLink = (idx: number) => {
    const updatedLinks = footerState.links.filter((_, i) => i !== idx);
    setFooterState({ ...footerState, links: updatedLinks });
    onUpdate({ ...footerState, links: updatedLinks });
  };

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold mb-2">Footer Content</h2>
      <div className="space-y-4">
        <Label>Footer Text</Label>
        <Input name="text" value={footerState.text} onChange={handleChange} />
        <Label>Logo</Label>
        <Input type="file" accept="image/*" onChange={e => handleImageUpload(e, 'logo')} />
        {footerState.logo && <img src={footerState.logo} alt="Footer Logo" className="max-h-24 mt-2" />}
        <Label>Background Image</Label>
        <Input type="file" accept="image/*" onChange={e => handleImageUpload(e, 'backgroundImage')} />
        {footerState.backgroundImage && <img src={footerState.backgroundImage} alt="Footer Background" className="max-h-24 mt-2" />}
        <div>
          <Label>Links</Label>
          <div className="space-y-2">
            {footerState.links.map((link, idx) => (
              <div key={idx} className="flex gap-2 items-center">
                <Input name="label" value={link.label} onChange={e => handleLinkChange(idx, e)} placeholder="Label" />
                <Input name="url" value={link.url} onChange={e => handleLinkChange(idx, e)} placeholder="URL" />
                <Button size="sm" variant="destructive" onClick={() => handleDeleteLink(idx)}>Delete</Button>
              </div>
            ))}
          </div>
          <div className="flex gap-2 mt-2">
            <Input name="label" value={newLink.label} onChange={e => setNewLink({ ...newLink, label: e.target.value })} placeholder="New Link Label" />
            <Input name="url" value={newLink.url} onChange={e => setNewLink({ ...newLink, url: e.target.value })} placeholder="New Link URL" />
            <Button size="sm" onClick={handleAddLink}>Add Link</Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FooterEditor;
