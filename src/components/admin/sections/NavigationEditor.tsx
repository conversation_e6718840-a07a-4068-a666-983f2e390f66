import React from 'react';
import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { MenuItem } from '@/types/content';
import { PlusCircle, GripVertical, Trash2, Edit, ExternalLink } from 'lucide-react';
import { DragDropContext, Draggable, Droppable, DropResult } from '@hello-pangea/dnd';

const menuItemSchema = z.object({
  title: z.string().min(1, "Title is required"),
  path: z.string().min(1, "Path is required"),
  parentId: z.string().optional(),
  isExternal: z.boolean().default(false),
  order: z.number(),
});

interface NavigationEditorProps {
  mainMenu: MenuItem[];
  footerMenu: MenuItem[];
  onUpdate: (menu: { mainMenu: MenuItem[], footerMenu: MenuItem[] }) => void;
}

export function NavigationEditor({ mainMenu, footerMenu, onUpdate }: NavigationEditorProps) {
  const [activeMenu, setActiveMenu] = useState<'main' | 'footer'>('main');
  const [editingItem, setEditingItem] = useState<MenuItem | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);

  const form = useForm<z.infer<typeof menuItemSchema>>({
    resolver: zodResolver(menuItemSchema),
    defaultValues: {
      title: "",
      path: "",
      isExternal: false,
      order: 0,
    },
  });

  const handleAddItem = (values: z.infer<typeof menuItemSchema>) => {
    // Ensure all required properties are included
    const newItem: MenuItem = {
      id: crypto.randomUUID(),
      title: values.title,
      path: values.path,
      order: activeMenu === 'main' ? mainMenu.length : footerMenu.length,
      isExternal: values.isExternal,
    };
    
    // Only include parentId if it's provided and not empty
    if (values.parentId) {
      (newItem as any).parentId = values.parentId;
    }

    if (activeMenu === 'main') {
      onUpdate({
        mainMenu: [...mainMenu, newItem],
        footerMenu,
      });
    } else {
      onUpdate({
        mainMenu,
        footerMenu: [...footerMenu, newItem],
      });
    }

    form.reset();
    setDialogOpen(false);
  };

  const handleEditItem = (values: z.infer<typeof menuItemSchema>) => {
    if (!editingItem) return;

    const updatedItem: MenuItem = {
      ...editingItem,
      title: values.title,
      path: values.path,
      isExternal: values.isExternal,
      order: editingItem.order,
    };

    // Only include parentId if it's provided and not empty
    if (values.parentId) {
      (updatedItem as any).parentId = values.parentId;
    }

    if (activeMenu === 'main') {
      onUpdate({
        mainMenu: mainMenu.map(item => item.id === editingItem.id ? updatedItem : item),
        footerMenu,
      });
    } else {
      onUpdate({
        mainMenu,
        footerMenu: footerMenu.map(item => item.id === editingItem.id ? updatedItem : item),
      });
    }

    setEditingItem(null);
    setDialogOpen(false);
  };

  const handleDeleteItem = (itemId: string) => {
    if (activeMenu === 'main') {
      onUpdate({
        mainMenu: mainMenu.filter(item => item.id !== itemId),
        footerMenu,
      });
    } else {
      onUpdate({
        mainMenu,
        footerMenu: footerMenu.filter(item => item.id !== itemId),
      });
    }
  };

  const onDragEnd = (result: DropResult) => {
    if (!result.destination) return;

    const items = activeMenu === 'main' ? [...mainMenu] : [...footerMenu];
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    // Update order property
    const reorderedItems = items.map((item, index) => ({
      ...item,
      order: index,
    }));

    if (activeMenu === 'main') {
      onUpdate({
        mainMenu: reorderedItems,
        footerMenu,
      });
    } else {
      onUpdate({
        mainMenu,
        footerMenu: reorderedItems,
      });
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex space-x-4">
        <Button
          variant={activeMenu === 'main' ? 'default' : 'outline'}
          onClick={() => setActiveMenu('main')}
        >
          Main Menu
        </Button>
        <Button
          variant={activeMenu === 'footer' ? 'default' : 'outline'}
          onClick={() => setActiveMenu('footer')}
        >
          Footer Menu
        </Button>
      </div>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <CardTitle>{activeMenu === 'main' ? 'Main Navigation' : 'Footer Navigation'}</CardTitle>
          <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
            <DialogTrigger asChild>
              <Button
                onClick={() => {
                  form.reset();
                  setEditingItem(null);
                }}
              >
                <PlusCircle className="mr-2 h-4 w-4" />
                Add Item
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>{editingItem ? 'Edit Menu Item' : 'Add Menu Item'}</DialogTitle>
                <DialogDescription>
                  {editingItem
                    ? "Edit the navigation menu item details below."
                    : "Add a new item to the navigation menu."}
                </DialogDescription>
              </DialogHeader>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(editingItem ? handleEditItem : handleAddItem)} className="space-y-4">
                  <FormField
                    control={form.control}
                    name="title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Title</FormLabel>
                        <FormControl>
                          <Input placeholder="Home" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="path"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Path</FormLabel>
                        <FormControl>
                          <Input placeholder="/home" {...field} />
                        </FormControl>
                        <FormDescription>
                          Use relative paths (e.g., /about) for internal links or full URLs for external links.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  {activeMenu === 'main' && (
                    <FormField
                      control={form.control}
                      name="parentId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Parent Menu (Optional)</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select a parent menu" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="">None</SelectItem>
                              {mainMenu
                                .filter(item => !item.parentId)
                                .map(item => (
                                  <SelectItem key={item.id} value={item.id}>
                                    {item.title}
                                  </SelectItem>
                                ))}
                            </SelectContent>
                          </Select>
                        </FormItem>
                      )}
                    />
                  )}
                  <FormField
                    control={form.control}
                    name="isExternal"
                    render={({ field }) => (
                      <FormItem className="flex items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">External Link</FormLabel>
                          <FormDescription>
                            Open link in a new tab
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <DialogFooter>
                    <Button type="submit">
                      {editingItem ? 'Save Changes' : 'Add Item'}
                    </Button>
                  </DialogFooter>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
        </CardHeader>
        <CardContent>
          <DragDropContext onDragEnd={onDragEnd}>
            <Droppable droppableId="menu-items">
              {(provided) => (
                <div
                  {...provided.droppableProps}
                  ref={provided.innerRef}
                  className="space-y-2"
                >
                  {(activeMenu === 'main' ? mainMenu : footerMenu)
                    .sort((a, b) => a.order - b.order)
                    .map((item, index) => (
                      <Draggable key={item.id} draggableId={item.id} index={index}>
                        {(provided) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            className="flex items-center justify-between p-3 bg-card rounded-lg border"
                          >
                            <div className="flex items-center gap-3">
                              <div {...provided.dragHandleProps}>
                                <GripVertical className="h-5 w-5 text-muted-foreground" />
                              </div>
                              <div className="flex flex-col">
                                <span className="font-medium">{item.title}</span>
                                <span className="text-sm text-muted-foreground">
                                  {item.path}
                                </span>
                              </div>
                              {item.isExternal && (
                                <Badge variant="secondary">
                                  <ExternalLink className="h-3 w-3 mr-1" />
                                  External
                                </Badge>
                              )}
                              {item.parentId && (
                                <Badge variant="outline">
                                  Sub-item
                                </Badge>
                              )}
                            </div>
                            <div className="flex items-center gap-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  setEditingItem(item);
                                  form.reset({
                                    title: item.title,
                                    path: item.path,
                                    parentId: item.parentId,
                                    isExternal: item.isExternal,
                                    order: item.order,
                                  });
                                  setDialogOpen(true);
                                }}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDeleteItem(item.id)}
                              >
                                <Trash2 className="h-4 w-4 text-destructive" />
                              </Button>
                            </div>
                          </div>
                        )}
                      </Draggable>
                    ))}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </DragDropContext>
        </CardContent>
      </Card>
    </div>
  );
}