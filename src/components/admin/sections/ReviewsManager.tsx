import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Star, Plus, Trash2, Edit2 } from 'lucide-react';
import { Review } from '@/types/content';

interface ReviewsManagerProps {
  reviews: Review[];
  onUpdate: (reviews: Review[]) => void;
}

export const ReviewsManager: React.FC<ReviewsManagerProps> = ({ reviews, onUpdate }) => {
  const [showDialog, setShowDialog] = useState(false);
  const [editingReview, setEditingReview] = useState<Review | null>(null);
  const [formData, setFormData] = useState<Partial<Review>>({
    name: '',
    rating: 5,
    comment: '',
    date: new Date().toISOString().slice(0, 10),
    isApproved: true,
  });

  const handleAdd = () => {
    setFormData({ name: '', rating: 5, comment: '', date: new Date().toISOString().slice(0, 10), isApproved: true });
    setShowDialog(true);
  };

  const handleEdit = (review: Review) => {
    setEditingReview(review);
    setFormData(review);
    setShowDialog(true);
  };

  const handleDelete = (id: string) => {
    onUpdate(reviews.filter(r => r.id !== id));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (editingReview) {
      onUpdate(reviews.map(r => r.id === editingReview.id ? { ...editingReview, ...formData } as Review : r));
    } else {
      const newReview: Review = {
        id: Math.random().toString(36).substr(2, 9),
        ...formData as Review,
      };
      onUpdate([...reviews, newReview]);
    }
    setShowDialog(false);
    setEditingReview(null);
    setFormData({ name: '', rating: 5, comment: '', date: new Date().toISOString().slice(0, 10), isApproved: true });
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Reviews</CardTitle>
            <CardDescription>Manage user reviews and testimonials</CardDescription>
          </div>
          <Button onClick={handleAdd}>
            <Plus className="h-4 w-4 mr-2" /> Add Review
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {reviews.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground mb-4">No reviews yet</p>
              <Button onClick={handleAdd} variant="outline">Create your first review</Button>
            </div>
          ) : (
            reviews.map((review) => (
              <Card key={review.id}>
                <CardContent className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <h3 className="font-medium">{review.name}</h3>
                        <Badge variant={review.isApproved ? 'default' : 'outline'}>{review.isApproved ? 'Approved' : 'Pending'}</Badge>
                        <span className="flex items-center text-yellow-500">{Array.from({ length: review.rating }).map((_, i) => <Star key={i} className="h-4 w-4 fill-yellow-400" />)}</span>
                      </div>
                      <p className="text-muted-foreground">{review.comment}</p>
                      <p className="text-sm text-muted-foreground">{review.date}</p>
                    </div>
                    <div className="flex gap-2">
                      <Button variant="ghost" size="icon" onClick={() => handleEdit(review)}><Edit2 className="h-4 w-4" /></Button>
                      <Button variant="ghost" size="icon" onClick={() => handleDelete(review.id)}><Trash2 className="h-4 w-4 text-destructive" /></Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
        <Dialog open={showDialog} onOpenChange={setShowDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{editingReview ? 'Edit Review' : 'Add Review'}</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid gap-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="name" className="text-right">Name</Label>
                  <Input id="name" value={formData.name} onChange={e => setFormData({ ...formData, name: e.target.value })} className="col-span-3" />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="rating" className="text-right">Rating</Label>
                  <Input id="rating" type="number" min={1} max={5} value={formData.rating} onChange={e => setFormData({ ...formData, rating: Number(e.target.value) })} className="col-span-3" />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="comment" className="text-right">Comment</Label>
                  <Textarea id="comment" value={formData.comment} onChange={e => setFormData({ ...formData, comment: e.target.value })} className="col-span-3" />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="date" className="text-right">Date</Label>
                  <Input id="date" type="date" value={formData.date} onChange={e => setFormData({ ...formData, date: e.target.value })} className="col-span-3" />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label className="text-right">Approved</Label>
                  <div className="flex items-center space-x-2 col-span-3">
                    <Switch checked={formData.isApproved} onCheckedChange={checked => setFormData({ ...formData, isApproved: checked })} />
                    <Label>{formData.isApproved ? 'Yes' : 'No'}</Label>
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => { setShowDialog(false); setEditingReview(null); }}>Cancel</Button>
                <Button type="submit">{editingReview ? 'Update Review' : 'Save Review'}</Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
};