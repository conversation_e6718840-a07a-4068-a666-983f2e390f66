import React, { useState } from 'react';
import { AboutSection } from '../../../types/content';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface AboutEditorProps {
  about: AboutSection[];
  onUpdate: (about: AboutSection[]) => void;
}

const AboutEditor: React.FC<AboutEditorProps> = ({ about, onUpdate }) => {
  const [sections, setSections] = useState<AboutSection[]>(about || []);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [newSection, setNewSection] = useState<AboutSection>({
    id: '',
    title: '',
    content: '',
    image: '',
    order: sections.length,
  });

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>, idx: number | null) => {
    const file = e.target.files?.[0];
    if (!file) return;
    const url = URL.createObjectURL(file);
    if (idx === null) {
      setNewSection({ ...newSection, image: url });
    } else {
      const updated = [...sections];
      updated[idx].image = url;
      setSections(updated);
      onUpdate(updated);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>, idx: number | null) => {
    const { name, value } = e.target;
    if (idx === null) {
      setNewSection({ ...newSection, [name]: value });
    } else {
      const updated = [...sections];
      if (name === 'title' || name === 'content' || name === 'image' || name === 'id') {
        updated[idx][name] = value;
      } else if (name === 'order') {
        updated[idx][name] = Number(value);
      }
      setSections(updated);
      onUpdate(updated);
    }
  };

  const handleAdd = () => {
    if (!newSection.title.trim()) return;
    const section = { ...newSection, id: Date.now().toString(), order: sections.length };
    const updated = [...sections, section];
    setSections(updated);
    setNewSection({ id: '', title: '', content: '', image: '', order: updated.length });
    onUpdate(updated);
  };

  const handleDelete = (idx: number) => {
    const updated = sections.filter((_, i) => i !== idx);
    setSections(updated);
    onUpdate(updated);
  };

  const handleEdit = (idx: number) => {
    setEditingIndex(idx);
  };

  const handleSaveEdit = (idx: number) => {
    setEditingIndex(null);
    onUpdate(sections);
  };

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold mb-2">About Sections</h2>
      <div className="space-y-4">
        {sections.map((section, idx) => (
          <div key={section.id} className="border p-4 rounded space-y-2 bg-muted">
            {editingIndex === idx ? (
              <>
                <Label>Title</Label>
                <Input name="title" value={section.title} onChange={e => handleChange(e, idx)} />
                <Label>Content</Label>
                <textarea name="content" value={section.content} onChange={e => handleChange(e, idx)} className="w-full border rounded p-2" />
                <Label>Image</Label>
                <Input type="file" accept="image/*" onChange={e => handleImageUpload(e, idx)} />
                {section.image && <img src={section.image} alt="About" className="max-h-32 mt-2" />}
                <div className="flex gap-2 mt-2">
                  <Button size="sm" onClick={() => handleSaveEdit(idx)}>Save</Button>
                  <Button size="sm" variant="outline" onClick={() => setEditingIndex(null)}>Cancel</Button>
                </div>
              </>
            ) : (
              <>
                <div className="flex justify-between items-center">
                  <div>
                    <div className="font-semibold">{section.title}</div>
                    <div className="text-sm text-muted-foreground">{section.content}</div>
                  </div>
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline" onClick={() => handleEdit(idx)}>Edit</Button>
                    <Button size="sm" variant="destructive" onClick={() => handleDelete(idx)}>Delete</Button>
                  </div>
                </div>
                {section.image && <img src={section.image} alt="About" className="max-h-32 mt-2" />}
              </>
            )}
          </div>
        ))}
      </div>
      <div className="border p-4 rounded space-y-2">
        <h3 className="font-semibold">Add New Section</h3>
        <Label>Title</Label>
        <Input name="title" value={newSection.title} onChange={e => handleChange(e, null)} />
        <Label>Content</Label>
        <textarea name="content" value={newSection.content} onChange={e => handleChange(e, null)} className="w-full border rounded p-2" />
        <Label>Image</Label>
        <Input type="file" accept="image/*" onChange={e => handleImageUpload(e, null)} />
        {newSection.image && <img src={newSection.image} alt="About" className="max-h-32 mt-2" />}
        <Button className="mt-2" onClick={handleAdd}>Add Section</Button>
      </div>
    </div>
  );
};

export default AboutEditor;
