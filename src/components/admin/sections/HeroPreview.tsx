import React from 'react';
import { HeroSlide } from '@/types/content';
import { cn } from '@/lib/utils';

interface HeroPreviewProps {
  slide: HeroSlide;
  className?: string;
}

export const HeroPreview: React.FC<HeroPreviewProps> = ({ slide, className }) => {
  return (
    <div className={cn(
      "relative w-full h-[300px] overflow-hidden rounded-lg",
      className
    )}>
      {/* Background Image */}
      <div
        className="absolute inset-0 bg-cover bg-center transition-transform duration-500"
        style={{
          backgroundImage: `url(${slide.imageUrl})`,
          backgroundColor: slide.backgroundColor || 'transparent',
        }}
      />
      
      {/* Overlay */}
      {slide.overlayColor && (
        <div
          className="absolute inset-0"
          style={{
            backgroundColor: slide.overlayColor,
            opacity: slide.overlayOpacity || 0.5,
          }}
        />
      )}

      {/* Content */}
      <div className={cn(
        "relative z-10 h-full flex flex-col justify-center p-6",
        slide.alignment === 'left' ? 'items-start text-left' :
        slide.alignment === 'right' ? 'items-end text-right' :
        'items-center text-center'
      )}>
        {slide.title && (
          <h2 className="text-2xl sm:text-3xl font-bold text-white mb-2">
            {slide.title}
          </h2>
        )}
        
        {slide.subtitle && (
          <h3 className="text-lg sm:text-xl text-white/90 mb-2">
            {slide.subtitle}
          </h3>
        )}
        
        {slide.description && (
          <div className="text-sm sm:text-base text-white/80 mb-4 max-w-lg">
            {slide.richDescription ? (
              <div dangerouslySetInnerHTML={{ __html: slide.richDescription }} />
            ) : (
              slide.description
            )}
          </div>
        )}

        {slide.buttonText && (
          <button
            className={cn(
              "px-4 py-2 rounded-md font-medium transition-colors",
              slide.buttonStyle === 'primary' && "bg-temple-gold text-white hover:bg-temple-gold/90",
              slide.buttonStyle === 'secondary' && "bg-white text-temple-gold hover:bg-white/90",
              slide.buttonStyle === 'outline' && "border-2 border-white text-white hover:bg-white/10",
              !slide.buttonStyle && "bg-temple-gold text-white hover:bg-temple-gold/90"
            )}
          >
            {slide.buttonText}
          </button>
        )}
      </div>
    </div>
  );
};
