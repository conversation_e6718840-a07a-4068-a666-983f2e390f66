import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
-- Enable uuid extension if not already enabled
create extension if not exists "uuid-ossp";

-- Main content table for your website
create table website_content (
  id uuid primary key default uuid_generate_v4(),
  data jsonb not null,
  updated_at timestamp with time zone default now()
);

-- (Optional) Add RLS for security
alter table website_content enable row level security;import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

const popupSchema = z.object({
  isEnabled: z.boolean(),
  title: z.string().min(1, 'Title is required'),
  content: z.string().min(1, 'Content is required'),
  showDelay: z.number().min(0),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
}).refine((data) => {
  if (data.endDate && data.startDate) {
    return new Date(data.endDate) >= new Date(data.startDate);
  }
  return true;
}, {
  message: 'End date must be after start date',
  path: ['endDate'],
});

interface PopupSettings {
  isEnabled: boolean;
  title: string;
  content: string;
  showDelay: number;
  startDate?: string;
  endDate?: string;
  image?: string;
}

interface PopupManagerProps {
  settings: PopupSettings;
  onUpdate: (settings: PopupSettings) => void;
}

export const PopupManager: React.FC<PopupManagerProps> = ({ settings, onUpdate }) => {
  const [showDialog, setShowDialog] = useState(false);
  const [formData, setFormData] = useState<PopupSettings>({ ...settings });
  const [imagePreview, setImagePreview] = useState<string | null>(settings.image || null);
  const [dateError, setDateError] = useState<string | null>(null);

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const url = URL.createObjectURL(file);
      setImagePreview(url);
      setFormData({ ...formData, image: url });
    }
  };

  const handleFieldChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData({ ...formData, [name]: type === 'number' ? Number(value) : value });
  };

  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
    if (name === 'endDate' && formData.startDate && value) {
      if (new Date(value) < new Date(formData.startDate)) {
        setDateError('End date must be after start date');
      } else {
        setDateError(null);
      }
    }
    if (name === 'startDate' && formData.endDate && value) {
      if (new Date(formData.endDate) < new Date(value)) {
        setDateError('End date must be after start date');
      } else {
        setDateError(null);
      }
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (formData.endDate && formData.startDate && new Date(formData.endDate) < new Date(formData.startDate)) {
      setDateError('End date must be after start date');
      return;
    }
    setDateError(null);
    onUpdate(formData);
    setShowDialog(false);
  };

  const handleEdit = () => {
    setFormData({ ...settings });
    setImagePreview(settings.image || null);
    setShowDialog(true);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Popup Manager</CardTitle>
            <CardDescription>Manage popups for announcements, offers, or alerts</CardDescription>
          </div>
          <Button onClick={handleEdit}>Edit Popup Settings</Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <Label>Popup Enabled</Label>
            <Switch checked={settings.isEnabled} onCheckedChange={checked => onUpdate({ ...settings, isEnabled: checked })} />
          </div>
          <div>
            <Label>Title</Label>
            <div className="text-muted-foreground">{settings.title}</div>
          </div>
          <div>
            <Label>Content</Label>
            <div className="text-muted-foreground">{settings.content}</div>
          </div>
          <div>
            <Label>Show Delay (seconds)</Label>
            <div className="text-muted-foreground">{settings.showDelay}</div>
          </div>
          <div>
            <Label>Start Date</Label>
            <div className="text-muted-foreground">{settings.startDate ? settings.startDate : 'Not set'}</div>
          </div>
          <div>
            <Label>End Date</Label>
            <div className="text-muted-foreground">{settings.endDate ? settings.endDate : 'Not set'}</div>
          </div>
        </div>
        <Dialog open={showDialog} onOpenChange={setShowDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Edit Popup Settings</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid gap-4">
                <div className="flex items-center gap-4">
                  <Label>Popup Enabled</Label>
                  <Switch checked={formData.isEnabled} onCheckedChange={checked => setFormData({ ...formData, isEnabled: checked })} />
                </div>
                <div>
                  <Label htmlFor="popup-title">Title</Label>
                  <Input id="popup-title" name="title" value={formData.title} onChange={handleFieldChange} />
                </div>
                <div>
                  <Label htmlFor="popup-content">Content</Label>
                  <Input id="popup-content" name="content" value={formData.content} onChange={handleFieldChange} />
                </div>
                <div>
                  <Label htmlFor="popup-delay">Show Delay (seconds)</Label>
                  <Input id="popup-delay" type="number" min={0} name="showDelay" value={formData.showDelay} onChange={handleFieldChange} />
                </div>
                <div>
                  <Label htmlFor="popup-start-date">Start Date</Label>
                  <Input
                    id="popup-start-date"
                    type="date"
                    name="startDate"
                    value={formData.startDate}
                    onChange={handleDateChange}
                  />
                </div>
                <div>
                  <Label htmlFor="popup-end-date">End Date</Label>
                  <Input
                    id="popup-end-date"
                    type="date"
                    name="endDate"
                    value={formData.endDate}
                    onChange={handleDateChange}
                  />
                </div>
                <div>
                  <Label htmlFor="popup-image">Image</Label>
                  <Input id="popup-image" type="file" accept="image/*" onChange={handleImageChange} />
                  {imagePreview && (
                    <img src={imagePreview} alt="Preview" className="mt-2 max-h-32 rounded" />
                  )}
                </div>
              </div>
              {dateError && <span className="text-destructive text-xs">{dateError}</span>}
              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setShowDialog(false)}>Cancel</Button>
                <Button type="submit">Save Settings</Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
};