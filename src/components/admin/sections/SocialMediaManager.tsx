import React, { useState } from 'react';
import { Card, Card<PERSON><PERSON>nt, Card<PERSON>eader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Share2, Plus, Trash2, Edit2 } from 'lucide-react';
import { SocialMedia } from '@/types/content';

interface SocialMediaManagerProps {
  socialMedia: SocialMedia[];
  onUpdate: (socialMedia: SocialMedia[]) => void;
}

export const SocialMediaManager: React.FC<SocialMediaManagerProps> = ({ socialMedia, onUpdate }) => {
  const [showDialog, setShowDialog] = useState(false);
  const [editingItem, setEditingItem] = useState<SocialMedia | null>(null);
  const [formData, setFormData] = useState<Partial<SocialMedia>>({
    platform: '',
    url: '',
    icon: '',
  });

  const handleAdd = () => {
    setFormData({ platform: '', url: '', icon: '' });
    setShowDialog(true);
  };

  const handleEdit = (item: SocialMedia) => {
    setEditingItem(item);
    setFormData(item);
    setShowDialog(true);
  };

  const handleDelete = (platform: string) => {
    onUpdate(socialMedia.filter(s => s.platform !== platform));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (editingItem) {
      onUpdate(socialMedia.map(s => s.platform === editingItem.platform ? { ...editingItem, ...formData } as SocialMedia : s));
    } else {
      const newItem: SocialMedia = {
        ...formData as SocialMedia,
      };
      onUpdate([...socialMedia, newItem]);
    }
    setShowDialog(false);
    setEditingItem(null);
    setFormData({ platform: '', url: '', icon: '' });
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Social Media</CardTitle>
            <CardDescription>Manage social media links and icons</CardDescription>
          </div>
          <Button onClick={handleAdd}>
            <Plus className="h-4 w-4 mr-2" /> Add Social Link
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {socialMedia.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground mb-4">No social media links yet</p>
              <Button onClick={handleAdd} variant="outline">Create your first link</Button>
            </div>
          ) : (
            socialMedia.map((item) => (
              <Card key={item.platform}>
                <CardContent className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <h3 className="font-medium">{item.platform}</h3>
                        <span className="flex items-center text-blue-500"><Share2 className="h-4 w-4" /></span>
                      </div>
                      <p className="text-muted-foreground">{item.url}</p>
                      {item.icon && <img src={item.icon} alt="icon" className="h-8 w-8 mt-2" />}
                    </div>
                    <div className="flex gap-2">
                      <Button variant="ghost" size="icon" onClick={() => handleEdit(item)}><Edit2 className="h-4 w-4" /></Button>
                      <Button variant="ghost" size="icon" onClick={() => handleDelete(item.platform)}><Trash2 className="h-4 w-4 text-destructive" /></Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
        <Dialog open={showDialog} onOpenChange={setShowDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{editingItem ? 'Edit Social Link' : 'Add Social Link'}</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid gap-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="platform" className="text-right">Platform</Label>
                  <Input id="platform" value={formData.platform} onChange={e => setFormData({ ...formData, platform: e.target.value })} className="col-span-3" />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="url" className="text-right">URL</Label>
                  <Input id="url" value={formData.url} onChange={e => setFormData({ ...formData, url: e.target.value })} className="col-span-3" />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="icon" className="text-right">Icon URL</Label>
                  <Input id="icon" value={formData.icon} onChange={e => setFormData({ ...formData, icon: e.target.value })} className="col-span-3" />
                </div>
              </div>
              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => { setShowDialog(false); setEditingItem(null); }}>Cancel</Button>
                <Button type="submit">{editingItem ? 'Update Link' : 'Save Link'}</Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
};