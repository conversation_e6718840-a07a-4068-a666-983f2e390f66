import React, { useState } from 'react';
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from '@/components/ui/dialog';
import { format } from 'date-fns';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { toast } from 'sonner';
import { CalendarIcon, Plus, Trash2, Edit2 } from 'lucide-react';
import { Announcement } from '@/types/content';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

interface AnnouncementsEditorProps {
  announcements: Announcement[];
  onUpdate: (announcements: Announcement[]) => void;
}

const announcementSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  content: z.string().min(1, 'Content is required'),
  type: z.enum(['info', 'alert', 'event']),
  startDate: z.string().min(1, 'Start date is required'),
  endDate: z.string().optional(),
  isActive: z.boolean(),
  image: z.any().optional(),
}).refine((data) => {
  if (data.endDate && data.startDate) {
    return new Date(data.endDate) >= new Date(data.startDate);
  }
  return true;
}, {
  message: 'End date must be after start date',
  path: ['endDate'],
});

export const AnnouncementsEditor: React.FC<AnnouncementsEditorProps> = ({
  announcements,
  onUpdate,
}) => {
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [editingAnnouncement, setEditingAnnouncement] = useState<Announcement | null>(null);
  const [formData, setFormData] = useState<Partial<Announcement>>({
    title: '',
    content: '',
    type: 'info',
    startDate: format(new Date(), 'yyyy-MM-dd'),
    endDate: undefined,
    isActive: true,
  });
  const [imagePreview, setImagePreview] = useState<string | null>(null);

  const {
    register,
    handleSubmit: rhfHandleSubmit,
    setValue,
    watch,
    reset,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(announcementSchema),
    defaultValues: formData,
  });

  // Watch for image changes
  const imageFile = watch('image');
  React.useEffect(() => {
    if (imageFile && imageFile[0]) {
      const url = URL.createObjectURL(imageFile[0]);
      setImagePreview(url);
      return () => URL.revokeObjectURL(url);
    } else {
      setImagePreview(null);
    }
  }, [imageFile]);

  const handleAddAnnouncement = () => {
    setFormData({
      title: '',
      content: '',
      type: 'info',
      startDate: format(new Date(), 'yyyy-MM-dd'),
      endDate: undefined,
      isActive: true,
    });
    setShowAddDialog(true);
  };

  const handleEditAnnouncement = (announcement: Announcement) => {
    setEditingAnnouncement(announcement);
    setFormData(announcement);
  };

  const handleDeleteAnnouncement = (id: string) => {
    const updatedAnnouncements = announcements.filter(a => a.id !== id);
    onUpdate(updatedAnnouncements);
    toast.success('Announcement deleted successfully');
  };

  const onFormSubmit = (data: any) => {
    if (editingAnnouncement) {
      const updatedAnnouncements = announcements.map(a =>
        a.id === editingAnnouncement.id ? { ...a, ...data, image: imagePreview || a.image } : a
      );
      onUpdate(updatedAnnouncements);
      setEditingAnnouncement(null);
      toast.success('Announcement updated successfully');
    } else {
      const newAnnouncement: Announcement = {
        id: Math.random().toString(36).substr(2, 9),
        ...data,
        image: imagePreview,
      };
      onUpdate([...announcements, newAnnouncement]);
      setShowAddDialog(false);
      toast.success('Announcement added successfully');
    }
    reset();
    setImagePreview(null);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Announcements</CardTitle>
            <CardDescription>Manage site-wide announcements and notifications</CardDescription>
          </div>
          <Button onClick={handleAddAnnouncement}>
            <Plus className="h-4 w-4 mr-2" />
            Add Announcement
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {announcements.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground mb-4">No announcements yet</p>
              <Button onClick={handleAddAnnouncement} variant="outline">
                Create your first announcement
              </Button>
            </div>
          ) : (
            announcements.map((announcement) => (
              <Card key={announcement.id}>
                <CardContent className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <h3 className="font-medium">{announcement.title}</h3>
                        <Badge variant={announcement.type === 'info' ? 'default' : announcement.type === 'alert' ? 'destructive' : 'secondary'}>
                          {announcement.type}
                        </Badge>
                        {announcement.isActive && (
                          <Badge variant="outline" className="bg-green-100 text-green-800">
                            Active
                          </Badge>
                        )}
                      </div>
                      <p className="text-muted-foreground">{announcement.content}</p>
                      <p className="text-sm text-muted-foreground">
                        Start date: {format(new Date(announcement.startDate), 'PP')}
                        {announcement.endDate && ` • End date: ${format(new Date(announcement.endDate), 'PP')}`}
                      </p>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleEditAnnouncement(announcement)}
                      >
                        <Edit2 className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleDeleteAnnouncement(announcement.id)}
                      >
                        <Trash2 className="h-4 w-4 text-destructive" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>

        {/* Add Announcement Dialog */}
        <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add New Announcement</DialogTitle>
            </DialogHeader>
            <form onSubmit={rhfHandleSubmit(onFormSubmit)} className="space-y-4">
              <div className="grid gap-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="title" className="text-right">
                    Title
                  </Label>
                  <Input
                    id="title"
                    {...register('title')}
                    className="col-span-3"
                  />
                  {errors.title && <span className="text-destructive text-xs">{errors.title.message}</span>}
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="content" className="text-right">
                    Content
                  </Label>
                  <Textarea
                    id="content"
                    {...register('content')}
                    className="col-span-3"
                  />
                  {errors.content && <span className="text-destructive text-xs">{errors.content.message}</span>}
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="type" className="text-right">
                    Type
                  </Label>
                  <Select
                    {...register('type')}
                  >
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Select announcement type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="info">Info</SelectItem>
                      <SelectItem value="alert">Alert</SelectItem>
                      <SelectItem value="event">Event</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="startDate" className="text-right">
                    Start Date
                  </Label>
                  <div className="col-span-3">
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className="w-full justify-start text-left font-normal"
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {formData.startDate ? (
                            format(new Date(formData.startDate), 'PPP')
                          ) : (
                            <span>Pick a date</span>
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={formData.startDate ? new Date(formData.startDate) : undefined}
                          onSelect={(date) =>
                            setFormData({
                              ...formData,
                              startDate: date ? format(date, 'yyyy-MM-dd') : undefined
                            })
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="endDate" className="text-right">
                    End Date
                  </Label>
                  <div className="col-span-3">
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className="w-full justify-start text-left font-normal"
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {formData.endDate ? (
                            format(new Date(formData.endDate), 'PPP')
                          ) : (
                            <span>Pick a date</span>
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={formData.endDate ? new Date(formData.endDate) : undefined}
                          onSelect={(date) =>
                            setFormData({
                              ...formData,
                              endDate: date ? format(date, 'yyyy-MM-dd') : undefined
                            })
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label className="text-right">Status</Label>
                  <div className="flex items-center space-x-2 col-span-3">
                    <Switch
                      checked={formData.isActive}
                      onCheckedChange={(checked) =>
                        setFormData({ ...formData, isActive: checked })
                      }
                    />
                    <Label>Active</Label>
                  </div>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="image" className="text-right">Image</Label>
                  <div className="col-span-3">
                    <Input id="image" type="file" accept="image/*" {...register('image')} />
                    {imagePreview && (
                      <img src={imagePreview} alt="Preview" className="mt-2 max-h-32 rounded" />
                    )}
                    {errors.image && <span className="text-destructive text-xs">{errors.image.message}</span>}
                  </div>
                </div>
              </div>
              {errors.endDate && <span className="text-destructive text-xs">{errors.endDate.message}</span>}
              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setShowAddDialog(false)}>
                  Cancel
                </Button>
                <Button type="submit">Save Announcement</Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>

        {/* Edit Announcement Dialog */}
        <Dialog
          open={editingAnnouncement !== null}
          onOpenChange={() => setEditingAnnouncement(null)}
        >
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Edit Announcement</DialogTitle>
            </DialogHeader>
            <form onSubmit={rhfHandleSubmit(onFormSubmit)} className="space-y-4">
              <div className="grid gap-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="edit-title" className="text-right">
                    Title
                  </Label>
                  <Input
                    id="edit-title"
                    {...register('title')}
                    className="col-span-3"
                  />
                  {errors.title && <span className="text-destructive text-xs">{errors.title.message}</span>}
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="edit-content" className="text-right">
                    Content
                  </Label>
                  <Textarea
                    id="edit-content"
                    {...register('content')}
                    className="col-span-3"
                  />
                  {errors.content && <span className="text-destructive text-xs">{errors.content.message}</span>}
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="edit-type" className="text-right">
                    Type
                  </Label>
                  <Select
                    {...register('type')}
                  >
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Select announcement type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="info">Info</SelectItem>
                      <SelectItem value="alert">Alert</SelectItem>
                      <SelectItem value="event">Event</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="edit-startDate" className="text-right">
                    Start Date
                  </Label>
                  <div className="col-span-3">
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className="w-full justify-start text-left font-normal"
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {formData.startDate ? (
                            format(new Date(formData.startDate), 'PPP')
                          ) : (
                            <span>Pick a date</span>
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={formData.startDate ? new Date(formData.startDate) : undefined}
                          onSelect={(date) =>
                            setFormData({
                              ...formData,
                              startDate: date ? format(date, 'yyyy-MM-dd') : undefined
                            })
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="edit-endDate" className="text-right">
                    End Date
                  </Label>
                  <div className="col-span-3">
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className="w-full justify-start text-left font-normal"
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {formData.endDate ? (
                            format(new Date(formData.endDate), 'PPP')
                          ) : (
                            <span>Pick a date</span>
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={formData.endDate ? new Date(formData.endDate) : undefined}
                          onSelect={(date) =>
                            setFormData({
                              ...formData,
                              endDate: date ? format(date, 'yyyy-MM-dd') : undefined
                            })
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label className="text-right">Status</Label>
                  <div className="flex items-center space-x-2 col-span-3">
                    <Switch
                      checked={formData.isActive}
                      onCheckedChange={(checked) =>
                        setFormData({ ...formData, isActive: checked })
                      }
                    />
                    <Label>Active</Label>
                  </div>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="image" className="text-right">Image</Label>
                  <div className="col-span-3">
                    <Input id="image" type="file" accept="image/*" {...register('image')} />
                    {imagePreview && (
                      <img src={imagePreview} alt="Preview" className="mt-2 max-h-32 rounded" />
                    )}
                    {errors.image && <span className="text-destructive text-xs">{errors.image.message}</span>}
                  </div>
                </div>
              </div>
              {errors.endDate && <span className="text-destructive text-xs">{errors.endDate.message}</span>}
              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setEditingAnnouncement(null)}
                >
                  Cancel
                </Button>
                <Button type="submit">Update Announcement</Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
};