import React, { useState } from 'react';
import { GalleryImage } from '../../../types/content';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface GalleryManagerProps {
  gallery: GalleryImage[];
  onUpdate: (gallery: GalleryImage[]) => void;
}

const GalleryManager: React.FC<GalleryManagerProps> = ({ gallery, onUpdate }) => {
  const [images, setImages] = useState<GalleryImage[]>(gallery || []);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [newImage, setNewImage] = useState<GalleryImage>({
    id: '',
    url: '',
    caption: '',
    order: images.length,
  });

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>, idx: number | null) => {
    const file = e.target.files?.[0];
    if (!file) return;
    const url = URL.createObjectURL(file);
    if (idx === null) {
      setNewImage({ ...newImage, url });
    } else {
      const updated = [...images];
      updated[idx].url = url;
      setImages(updated);
      onUpdate(updated);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>, idx: number | null) => {
    const { name, value } = e.target;
    if (idx === null) {
      setNewImage({ ...newImage, [name]: value });
    } else {
      const updated = [...images];
      if (name === 'caption' || name === 'url' || name === 'id') {
        updated[idx][name] = value;
      } else if (name === 'order') {
        updated[idx][name] = Number(value);
      }
      setImages(updated);
      onUpdate(updated);
    }
  };

  const handleAdd = () => {
    if (!newImage.url) return;
    const image = { ...newImage, id: Date.now().toString(), order: images.length };
    const updated = [...images, image];
    setImages(updated);
    setNewImage({ id: '', url: '', caption: '', order: updated.length });
    onUpdate(updated);
  };

  const handleDelete = (idx: number) => {
    const updated = images.filter((_, i) => i !== idx);
    setImages(updated);
    onUpdate(updated);
  };

  const handleEdit = (idx: number) => {
    setEditingIndex(idx);
  };

  const handleSaveEdit = (idx: number) => {
    setEditingIndex(null);
    onUpdate(images);
  };

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold mb-2">Gallery Images</h2>
      <div className="space-y-4">
        {images.map((img, idx) => (
          <div key={img.id} className="border p-4 rounded space-y-2 bg-muted">
            {editingIndex === idx ? (
              <>
                <Label>Caption</Label>
                <Input name="caption" value={img.caption} onChange={e => handleChange(e, idx)} />
                <Label>Image</Label>
                <Input type="file" accept="image/*" onChange={e => handleImageUpload(e, idx)} />
                {img.url && <img src={img.url} alt="Gallery" className="max-h-32 mt-2" />}
                <div className="flex gap-2 mt-2">
                  <Button size="sm" onClick={() => handleSaveEdit(idx)}>Save</Button>
                  <Button size="sm" variant="outline" onClick={() => setEditingIndex(null)}>Cancel</Button>
                </div>
              </>
            ) : (
              <>
                <div className="flex justify-between items-center">
                  <div>
                    <div className="font-semibold">{img.caption}</div>
                  </div>
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline" onClick={() => handleEdit(idx)}>Edit</Button>
                    <Button size="sm" variant="destructive" onClick={() => handleDelete(idx)}>Delete</Button>
                  </div>
                </div>
                {img.url && <img src={img.url} alt="Gallery" className="max-h-32 mt-2" />}
              </>
            )}
          </div>
        ))}
      </div>
      <div className="border p-4 rounded space-y-2">
        <h3 className="font-semibold">Add New Image</h3>
        <Label>Caption</Label>
        <Input name="caption" value={newImage.caption} onChange={e => handleChange(e, null)} />
        <Label>Image</Label>
        <Input type="file" accept="image/*" onChange={e => handleImageUpload(e, null)} />
        {newImage.url && <img src={newImage.url} alt="Gallery" className="max-h-32 mt-2" />}
        <Button className="mt-2" onClick={handleAdd}>Add Image</Button>
      </div>
    </div>
  );
};

export default GalleryManager;
