import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON>, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { AlertCircle, Eye } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  DragDropContext, 
  Droppable, 
  Draggable 
} from '@hello-pangea/dnd';
import { Plus, Grip, Trash2, Image as ImageIcon, ExternalLink } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { HeroSlide } from '@/types/content';
import { ImageUploader } from '../../ImageUploader';
import { toast } from 'sonner';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import { HeroPreview } from './HeroPreview';

interface HeroEditorProps {
  content: {
    slides: HeroSlide[];
    autoplaySpeed: number;
    isAutoplay: boolean;
  };
  onUpdate: (content: HeroEditorProps['content']) => void;
}

export const HeroEditor: React.FC<HeroEditorProps> = ({ content, onUpdate }) => {
  const [editingSlide, setEditingSlide] = useState<HeroSlide | null>(null);
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false);
  const [previewIndex, setPreviewIndex] = useState(0);

  const handleDragEnd = (result: any) => {
    if (!result.destination) return;

    const items = Array.from(content.slides);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    // Update order property
    const updatedSlides = items.map((slide, index) => ({
      ...slide,
      order: index
    }));

    onUpdate({
      ...content,
      slides: updatedSlides
    });
  };

  const handleAddSlide = () => {
    const newSlide: HeroSlide = {
      id: Date.now().toString(),
      title: 'New Slide',
      imageUrl: '/placeholder.svg',
      order: content.slides.length,
      active: true
    };
    onUpdate({
      ...content,
      slides: [...content.slides, newSlide]
    });
  };

  const handleDeleteSlide = (slideId: string) => {
    onUpdate({
      ...content,
      slides: content.slides.filter(slide => slide.id !== slideId)
    });
    toast.success('Slide deleted successfully');
  };

  const handleSlideUpdate = (slideId: string, updates: Partial<HeroSlide>) => {
    onUpdate({
      ...content,
      slides: content.slides.map(slide =>
        slide.id === slideId ? { ...slide, ...updates } : slide
      )
    });
  };

  const handleImageUpload = async (slideId: string, file: File) => {
    try {
      // Here you would typically upload the file to your storage
      // and get back a URL. This is a placeholder.
      const imageUrl = URL.createObjectURL(file);
      
      handleSlideUpdate(slideId, { imageUrl });
      setIsUploadDialogOpen(false);
      toast.success('Image uploaded successfully');
    } catch (error) {
      toast.error('Failed to upload image');
      console.error('Upload error:', error);
    }
  };

  const handleRichDescriptionChange = (slideId: string, value: string) => {
    handleSlideUpdate(slideId, { richDescription: value });
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Hero Slides</CardTitle>
            <Button onClick={handleAddSlide}>
              <Plus className="h-4 w-4 mr-2" />
              Add Slide
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-8">
            {/* Slide List & Editor */}
            <div className="flex-1 min-w-[320px]">
              <DragDropContext onDragEnd={handleDragEnd}>
                <Droppable droppableId="slides">
                  {(provided) => (
                    <div {...provided.droppableProps} ref={provided.innerRef}>
                      {content.slides.map((slide, index) => (
                        <Draggable key={slide.id} draggableId={slide.id} index={index}>
                          {(provided) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              className={
                                "bg-card border rounded-lg p-4 mb-4 flex flex-col gap-2" +
                                (previewIndex === index ? ' ring-2 ring-temple-gold' : '')
                              }
                              onClick={() => setPreviewIndex(index)}
                            >
                              <div className="flex items-start gap-4">
                                <div {...provided.dragHandleProps} className="mt-2">
                                  <Grip className="h-5 w-5 text-muted-foreground" />
                                </div>
                                <div className="flex-1 space-y-2">
                                  <Input
                                    placeholder="Slide Title"
                                    value={slide.title}
                                    onChange={(e) => handleSlideUpdate(slide.id, { title: e.target.value })}
                                  />
                                  <Input
                                    placeholder="Subtitle (optional)"
                                    value={slide.subtitle || ''}
                                    onChange={(e) => handleSlideUpdate(slide.id, { subtitle: e.target.value })}
                                  />
                                  <ReactQuill
                                    theme="snow"
                                    value={slide.richDescription || ''}
                                    onChange={(value) => handleRichDescriptionChange(slide.id, value)}
                                    placeholder="Rich description (optional)"
                                    className="mb-2"
                                  />
                                  <div className="flex gap-2">
                                    <Input
                                      placeholder="Button Text"
                                      value={slide.buttonText || ''}
                                      onChange={(e) => handleSlideUpdate(slide.id, { buttonText: e.target.value })}
                                    />
                                    <Input
                                      placeholder="Button Link"
                                      value={slide.buttonLink || ''}
                                      onChange={(e) => handleSlideUpdate(slide.id, { buttonLink: e.target.value })}
                                    />
                                  </div>
                                  <div className="flex gap-2 items-center">
                                    <Dialog>
                                      <DialogTrigger asChild>
                                        <Button variant="outline">
                                          <ImageIcon className="h-4 w-4 mr-2" />
                                          Change Image
                                        </Button>
                                      </DialogTrigger>
                                      <DialogContent>
                                        <DialogHeader>
                                          <DialogTitle>Upload Slide Image</DialogTitle>
                                        </DialogHeader>
                                        <ImageUploader onUpload={(file) => handleImageUpload(slide.id, file)} />
                                      </DialogContent>
                                    </Dialog>
                                    <Button variant="destructive" size="icon" onClick={() => handleDeleteSlide(slide.id)}>
                                      <Trash2 className="h-4 w-4" />
                                    </Button>
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>
              </DragDropContext>
            </div>
            {/* Live Preview */}
            <div className="flex-1 min-w-[320px]">
              <Card className="mb-4">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Eye className="h-4 w-4" /> Live Preview
                  </CardTitle>
                  <CardDescription>Preview the selected slide as it will appear on the site.</CardDescription>
                </CardHeader>
                <CardContent>
                  {content.slides[previewIndex] ? (
                    <HeroPreview slide={content.slides[previewIndex]} />
                  ) : (
                    <div className="text-muted-foreground text-center">No slide selected</div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
