import React, { useState, useRef, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { 
  Bold, 
  Italic, 
  Underline, 
  AlignLeft, 
  AlignCenter, 
  AlignRight,
  AlignJustify,
  Palette,
  Type,
  Save,
  RotateCcw
} from 'lucide-react';

interface TextEditorProps {
  initialText?: string;
  initialStyles?: TextStyles;
  onSave: (text: string, styles: TextStyles) => void;
  onClose: () => void;
}

interface TextStyles {
  fontSize: number;
  fontFamily: string;
  color: string;
  backgroundColor: string;
  fontWeight: 'normal' | 'bold';
  fontStyle: 'normal' | 'italic';
  textDecoration: 'none' | 'underline' | 'line-through';
  textAlign: 'left' | 'center' | 'right' | 'justify';
  lineHeight: number;
  letterSpacing: number;
  textShadow: string;
  borderRadius: number;
  padding: number;
  margin: number;
}

const fontFamilies = [
  'Arial',
  'Helvetica',
  'Times New Roman',
  'Georgia',
  'Verdana',
  'Courier New',
  'Impact',
  'Comic Sans MS',
  'Trebuchet MS',
  'Arial Black',
  'Palatino',
  'Garamond',
  'Bookman',
  'Avant Garde'
];

export const TextEditor: React.FC<TextEditorProps> = ({
  initialText = 'Enter your text here',
  initialStyles = {
    fontSize: 16,
    fontFamily: 'Arial',
    color: '#000000',
    backgroundColor: 'transparent',
    fontWeight: 'normal',
    fontStyle: 'normal',
    textDecoration: 'none',
    textAlign: 'left',
    lineHeight: 1.5,
    letterSpacing: 0,
    textShadow: 'none',
    borderRadius: 0,
    padding: 0,
    margin: 0
  },
  onSave,
  onClose
}) => {
  const [text, setText] = useState(initialText);
  const [styles, setStyles] = useState<TextStyles>(initialStyles);
  const textRef = useRef<HTMLDivElement>(null);

  const updateStyle = useCallback((property: keyof TextStyles, value: any) => {
    setStyles(prev => ({
      ...prev,
      [property]: value
    }));
  }, []);

  const toggleBold = useCallback(() => {
    updateStyle('fontWeight', styles.fontWeight === 'bold' ? 'normal' : 'bold');
  }, [styles.fontWeight, updateStyle]);

  const toggleItalic = useCallback(() => {
    updateStyle('fontStyle', styles.fontStyle === 'italic' ? 'normal' : 'italic');
  }, [styles.fontStyle, updateStyle]);

  const toggleUnderline = useCallback(() => {
    updateStyle('textDecoration', styles.textDecoration === 'underline' ? 'none' : 'underline');
  }, [styles.textDecoration, updateStyle]);

  const setAlignment = useCallback((alignment: 'left' | 'center' | 'right' | 'justify') => {
    updateStyle('textAlign', alignment);
  }, [updateStyle]);

  const resetStyles = useCallback(() => {
    setStyles(initialStyles);
  }, [initialStyles]);

  const handleSave = useCallback(() => {
    onSave(text, styles);
  }, [text, styles, onSave]);

  const previewStyles: React.CSSProperties = {
    fontSize: `${styles.fontSize}px`,
    fontFamily: styles.fontFamily,
    color: styles.color,
    backgroundColor: styles.backgroundColor,
    fontWeight: styles.fontWeight,
    fontStyle: styles.fontStyle,
    textDecoration: styles.textDecoration,
    textAlign: styles.textAlign,
    lineHeight: styles.lineHeight,
    letterSpacing: `${styles.letterSpacing}px`,
    textShadow: styles.textShadow,
    borderRadius: `${styles.borderRadius}px`,
    padding: `${styles.padding}px`,
    margin: `${styles.margin}px`,
    minHeight: '100px',
    border: '1px solid #e2e8f0',
    outline: 'none'
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gray-50 px-6 py-4 border-b flex items-center justify-between">
          <h2 className="text-xl font-bold text-gray-900">Text Editor</h2>
          <div className="flex items-center space-x-2">
            <Button variant="outline" onClick={resetStyles}>
              <RotateCcw className="w-4 h-4 mr-2" />
              Reset
            </Button>
            <Button onClick={handleSave}>
              <Save className="w-4 h-4 mr-2" />
              Save
            </Button>
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </div>
        </div>

        <div className="flex h-[calc(90vh-80px)]">
          {/* Controls Sidebar */}
          <div className="w-80 bg-gray-50 border-r overflow-y-auto p-4 space-y-6">
            {/* Text Content */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Text Content</CardTitle>
              </CardHeader>
              <CardContent>
                <textarea
                  value={text}
                  onChange={(e) => setText(e.target.value)}
                  className="w-full h-24 p-2 border rounded resize-none"
                  placeholder="Enter your text here..."
                />
              </CardContent>
            </Card>

            {/* Formatting Toolbar */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Formatting</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Bold, Italic, Underline */}
                <div className="flex space-x-2">
                  <Button
                    variant={styles.fontWeight === 'bold' ? 'default' : 'outline'}
                    size="sm"
                    onClick={toggleBold}
                  >
                    <Bold className="w-4 h-4" />
                  </Button>
                  <Button
                    variant={styles.fontStyle === 'italic' ? 'default' : 'outline'}
                    size="sm"
                    onClick={toggleItalic}
                  >
                    <Italic className="w-4 h-4" />
                  </Button>
                  <Button
                    variant={styles.textDecoration === 'underline' ? 'default' : 'outline'}
                    size="sm"
                    onClick={toggleUnderline}
                  >
                    <Underline className="w-4 h-4" />
                  </Button>
                </div>

                {/* Alignment */}
                <div className="flex space-x-2">
                  <Button
                    variant={styles.textAlign === 'left' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setAlignment('left')}
                  >
                    <AlignLeft className="w-4 h-4" />
                  </Button>
                  <Button
                    variant={styles.textAlign === 'center' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setAlignment('center')}
                  >
                    <AlignCenter className="w-4 h-4" />
                  </Button>
                  <Button
                    variant={styles.textAlign === 'right' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setAlignment('right')}
                  >
                    <AlignRight className="w-4 h-4" />
                  </Button>
                  <Button
                    variant={styles.textAlign === 'justify' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setAlignment('justify')}
                  >
                    <AlignJustify className="w-4 h-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Typography */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center">
                  <Type className="w-4 h-4 mr-2" />
                  Typography
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Font Family */}
                <div>
                  <Label>Font Family</Label>
                  <Select value={styles.fontFamily} onValueChange={(value) => updateStyle('fontFamily', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {fontFamilies.map((font) => (
                        <SelectItem key={font} value={font} style={{ fontFamily: font }}>
                          {font}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Font Size */}
                <div>
                  <Label>Font Size: {styles.fontSize}px</Label>
                  <Slider
                    value={[styles.fontSize]}
                    onValueChange={([value]) => updateStyle('fontSize', value)}
                    min={8}
                    max={72}
                    step={1}
                    className="mt-2"
                  />
                </div>

                {/* Line Height */}
                <div>
                  <Label>Line Height: {styles.lineHeight}</Label>
                  <Slider
                    value={[styles.lineHeight]}
                    onValueChange={([value]) => updateStyle('lineHeight', value)}
                    min={0.8}
                    max={3}
                    step={0.1}
                    className="mt-2"
                  />
                </div>

                {/* Letter Spacing */}
                <div>
                  <Label>Letter Spacing: {styles.letterSpacing}px</Label>
                  <Slider
                    value={[styles.letterSpacing]}
                    onValueChange={([value]) => updateStyle('letterSpacing', value)}
                    min={-5}
                    max={10}
                    step={0.5}
                    className="mt-2"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Colors */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center">
                  <Palette className="w-4 h-4 mr-2" />
                  Colors
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label>Text Color</Label>
                  <div className="flex items-center space-x-2 mt-2">
                    <input
                      type="color"
                      value={styles.color}
                      onChange={(e) => updateStyle('color', e.target.value)}
                      className="w-12 h-8 rounded border"
                    />
                    <Input
                      value={styles.color}
                      onChange={(e) => updateStyle('color', e.target.value)}
                      className="flex-1"
                    />
                  </div>
                </div>

                <div>
                  <Label>Background Color</Label>
                  <div className="flex items-center space-x-2 mt-2">
                    <input
                      type="color"
                      value={styles.backgroundColor === 'transparent' ? '#ffffff' : styles.backgroundColor}
                      onChange={(e) => updateStyle('backgroundColor', e.target.value)}
                      className="w-12 h-8 rounded border"
                    />
                    <Input
                      value={styles.backgroundColor}
                      onChange={(e) => updateStyle('backgroundColor', e.target.value)}
                      className="flex-1"
                      placeholder="transparent"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Spacing */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Spacing</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label>Padding: {styles.padding}px</Label>
                  <Slider
                    value={[styles.padding]}
                    onValueChange={([value]) => updateStyle('padding', value)}
                    min={0}
                    max={50}
                    step={1}
                    className="mt-2"
                  />
                </div>

                <div>
                  <Label>Margin: {styles.margin}px</Label>
                  <Slider
                    value={[styles.margin]}
                    onValueChange={([value]) => updateStyle('margin', value)}
                    min={0}
                    max={50}
                    step={1}
                    className="mt-2"
                  />
                </div>

                <div>
                  <Label>Border Radius: {styles.borderRadius}px</Label>
                  <Slider
                    value={[styles.borderRadius]}
                    onValueChange={([value]) => updateStyle('borderRadius', value)}
                    min={0}
                    max={25}
                    step={1}
                    className="mt-2"
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Preview Area */}
          <div className="flex-1 bg-gray-100 p-6">
            <div className="bg-white rounded-lg shadow-lg p-6 h-full">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Preview</h3>
              <div
                ref={textRef}
                style={previewStyles}
                contentEditable
                suppressContentEditableWarning
                onInput={(e) => setText(e.currentTarget.textContent || '')}
                className="w-full"
              >
                {text}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
