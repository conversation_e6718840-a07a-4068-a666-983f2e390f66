import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Play, 
  CheckCircle, 
  XCircle, 
  Clock, 
  RefreshCw,
  Database,
  Zap
} from 'lucide-react';
import { useContent } from '@/contexts/ContentContext';
import contentSyncTest from '@/utils/contentSyncTest';
import { toast } from 'sonner';

export const ContentSyncTestPanel: React.FC = () => {
  const { content, updateSection, syncStatus } = useContent();
  const [isRunningTests, setIsRunningTests] = useState(false);
  const [testResults, setTestResults] = useState<{
    passed: number;
    failed: number;
    results: Array<{ test: string; passed: boolean; error?: string }>;
  } | null>(null);
  const [performanceResults, setPerformanceResults] = useState<{
    loadTime: number;
    saveTime: number;
    syncTime: number;
  } | null>(null);

  const runAllTests = async () => {
    setIsRunningTests(true);
    try {
      const results = await contentSyncTest.runAllTests();
      setTestResults(results);
      
      if (results.failed === 0) {
        toast.success(`All ${results.passed} tests passed!`);
      } else {
        toast.error(`${results.failed} tests failed out of ${results.passed + results.failed}`);
      }
    } catch (error) {
      toast.error('Test suite failed to run');
      console.error('Test suite error:', error);
    } finally {
      setIsRunningTests(false);
    }
  };

  const runPerformanceTest = async () => {
    try {
      const results = await contentSyncTest.performanceTest();
      setPerformanceResults(results);
      toast.success('Performance test completed');
    } catch (error) {
      toast.error('Performance test failed');
      console.error('Performance test error:', error);
    }
  };

  const testFrontendBackendSync = async () => {
    try {
      const success = await contentSyncTest.testFrontendBackendSync();
      if (success) {
        toast.success('Frontend-backend sync test passed');
      } else {
        toast.error('Frontend-backend sync test failed');
      }
    } catch (error) {
      toast.error('Sync test failed');
      console.error('Sync test error:', error);
    }
  };

  const testRealTimeUpdate = async () => {
    try {
      const testName = `Test Update ${Date.now()}`;
      await updateSection('siteInfo', {
        ...content?.siteInfo,
        name: testName
      });
      
      // Check if the update was applied
      setTimeout(() => {
        if (content?.siteInfo?.name === testName) {
          toast.success('Real-time update test passed');
        } else {
          toast.error('Real-time update test failed');
        }
      }, 1000);
    } catch (error) {
      toast.error('Real-time update test failed');
      console.error('Real-time update error:', error);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Content Synchronization Test Panel</h2>
          <p className="text-muted-foreground">
            Test and verify content synchronization functionality
          </p>
        </div>
      </div>

      {/* Sync Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="w-5 h-5" />
            Current Sync Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-2">
              <Badge variant={syncStatus.isInitialized ? "default" : "destructive"}>
                {syncStatus.isInitialized ? (
                  <>
                    <CheckCircle className="w-3 h-3 mr-1" />
                    Initialized
                  </>
                ) : (
                  <>
                    <XCircle className="w-3 h-3 mr-1" />
                    Not Initialized
                  </>
                )}
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              <Clock className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm">
                Last Sync: {syncStatus.lastSync ? new Date(syncStatus.lastSync).toLocaleString() : 'Never'}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm">Active Listeners: {syncStatus.activeListeners}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Test Controls */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">Full Test Suite</CardTitle>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={runAllTests} 
              disabled={isRunningTests}
              className="w-full"
            >
              {isRunningTests ? (
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Play className="w-4 h-4 mr-2" />
              )}
              {isRunningTests ? 'Running...' : 'Run All Tests'}
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">Performance Test</CardTitle>
          </CardHeader>
          <CardContent>
            <Button onClick={runPerformanceTest} variant="outline" className="w-full">
              <Zap className="w-4 h-4 mr-2" />
              Performance
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">Sync Test</CardTitle>
          </CardHeader>
          <CardContent>
            <Button onClick={testFrontendBackendSync} variant="outline" className="w-full">
              <RefreshCw className="w-4 h-4 mr-2" />
              Sync Test
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">Real-time Test</CardTitle>
          </CardHeader>
          <CardContent>
            <Button onClick={testRealTimeUpdate} variant="outline" className="w-full">
              <Clock className="w-4 h-4 mr-2" />
              Real-time
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Test Results */}
      {testResults && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {testResults.failed === 0 ? (
                <CheckCircle className="w-5 h-5 text-green-500" />
              ) : (
                <XCircle className="w-5 h-5 text-red-500" />
              )}
              Test Results
            </CardTitle>
            <CardDescription>
              {testResults.passed} passed, {testResults.failed} failed
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {testResults.results.map((result, index) => (
                <Alert key={index} variant={result.passed ? "default" : "destructive"}>
                  <div className="flex items-center gap-2">
                    {result.passed ? (
                      <CheckCircle className="w-4 h-4 text-green-500" />
                    ) : (
                      <XCircle className="w-4 h-4 text-red-500" />
                    )}
                    <span className="font-medium">{result.test}</span>
                  </div>
                  {result.error && (
                    <AlertDescription className="mt-1 text-sm">
                      {result.error}
                    </AlertDescription>
                  )}
                </Alert>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Performance Results */}
      {performanceResults && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="w-5 h-5" />
              Performance Results
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {performanceResults.loadTime.toFixed(2)}ms
                </div>
                <div className="text-sm text-muted-foreground">Load Time</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {performanceResults.saveTime.toFixed(2)}ms
                </div>
                <div className="text-sm text-muted-foreground">Save Time</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {performanceResults.syncTime.toFixed(2)}ms
                </div>
                <div className="text-sm text-muted-foreground">Sync Time</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Current Content Info */}
      <Card>
        <CardHeader>
          <CardTitle>Current Content Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <strong>Site Name:</strong> {content?.siteInfo?.name || 'Not set'}
            </div>
            <div>
              <strong>Hero Slides:</strong> {content?.hero?.slides?.length || 0}
            </div>
            <div>
              <strong>Announcements:</strong> {content?.announcements?.length || 0}
            </div>
            <div>
              <strong>Gallery Items:</strong> {content?.gallery?.length || 0}
            </div>
            <div>
              <strong>Last Update:</strong> {content?.settings?.lastUpdate ? new Date(content.settings.lastUpdate).toLocaleString() : 'Never'}
            </div>
            <div>
              <strong>Migration Status:</strong> {content?.settings?.migrationCompleted ? 'Completed' : 'Pending'}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
