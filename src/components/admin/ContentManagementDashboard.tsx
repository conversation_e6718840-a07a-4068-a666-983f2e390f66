import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import {
  Save,
  RefreshCw,
  Download,
  Upload,
  Eye,
  Settings,
  Database,
  RotateCcw,
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react';
import { useContent } from '@/contexts/ContentContext';
import { toast } from 'sonner';

export const ContentManagementDashboard: React.FC = () => {
  const { 
    content, 
    loading, 
    updateSection, 
    batchUpdate, 
    migrateContent, 
    exportContent, 
    importContent,
    syncStatus 
  } = useContent();

  const [activeTab, setActiveTab] = useState('overview');
  const [isExporting, setIsExporting] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [isMigrating, setIsMigrating] = useState(false);
  const [importData, setImportData] = useState('');

  // Site Information Editor
  const [siteInfo, setSiteInfo] = useState({
    name: '',
    tagline: '',
    description: '',
    logo: ''
  });

  // Hero Section Editor
  const [heroSettings, setHeroSettings] = useState({
    autoplaySpeed: 5000,
    isAutoplay: true
  });

  // Initialize form data when content loads
  useEffect(() => {
    if (content) {
      setSiteInfo({
        name: content.siteInfo?.name || '',
        tagline: content.siteInfo?.tagline || '',
        description: content.siteInfo?.description || '',
        logo: content.siteInfo?.logo || ''
      });

      setHeroSettings({
        autoplaySpeed: content.hero?.autoplaySpeed || 5000,
        isAutoplay: content.hero?.isAutoplay !== false
      });
    }
  }, [content]);

  const handleSaveSiteInfo = async () => {
    try {
      await updateSection('siteInfo', siteInfo);
      toast.success('Site information updated successfully');
    } catch (error) {
      toast.error('Failed to update site information');
    }
  };

  const handleSaveHeroSettings = async () => {
    try {
      await updateSection('hero', {
        ...content?.hero,
        ...heroSettings
      });
      toast.success('Hero settings updated successfully');
    } catch (error) {
      toast.error('Failed to update hero settings');
    }
  };

  const handleExportContent = async () => {
    try {
      setIsExporting(true);
      const contentJson = await exportContent();
      
      // Create download link
      const blob = new Blob([contentJson], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `website-content-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      toast.success('Content exported successfully');
    } catch (error) {
      toast.error('Failed to export content');
    } finally {
      setIsExporting(false);
    }
  };

  const handleImportContent = async () => {
    if (!importData.trim()) {
      toast.error('Please paste content data to import');
      return;
    }

    try {
      setIsImporting(true);
      await importContent(importData);
      setImportData('');
      toast.success('Content imported successfully');
    } catch (error) {
      toast.error('Failed to import content. Please check the format.');
    } finally {
      setIsImporting(false);
    }
  };

  const handleMigrateContent = async () => {
    try {
      setIsMigrating(true);
      await migrateContent();
      toast.success('Content migration completed successfully');
    } catch (error) {
      toast.error('Failed to migrate content');
    } finally {
      setIsMigrating(false);
    }
  };

  const handleFileImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        setImportData(content);
      };
      reader.readAsText(file);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p>Loading content management...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Content Management</h1>
          <p className="text-muted-foreground">
            Manage all website content from this centralized dashboard
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Badge variant={syncStatus.isInitialized ? "default" : "secondary"}>
            {syncStatus.isInitialized ? (
              <>
                <CheckCircle className="w-3 h-3 mr-1" />
                Synchronized
              </>
            ) : (
              <>
                <AlertCircle className="w-3 h-3 mr-1" />
                Not Synced
              </>
            )}
          </Badge>
          
          <Button variant="outline" onClick={() => window.open('/', '_blank')}>
            <Eye className="w-4 h-4 mr-2" />
            Preview Site
          </Button>
        </div>
      </div>

      {/* Sync Status Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <RotateCcw className="w-5 h-5" />
            Synchronization Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-2">
              <div className={`w-3 h-3 rounded-full ${syncStatus.isInitialized ? 'bg-green-500' : 'bg-red-500'}`} />
              <span className="text-sm">
                {syncStatus.isInitialized ? 'Active' : 'Inactive'}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Clock className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm">
                Last Sync: {syncStatus.lastSync ? new Date(syncStatus.lastSync).toLocaleString() : 'Never'}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Database className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm">
                Active Listeners: {syncStatus.activeListeners}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="site-info">Site Info</TabsTrigger>
          <TabsTrigger value="hero">Hero Section</TabsTrigger>
          <TabsTrigger value="import-export">Import/Export</TabsTrigger>
          <TabsTrigger value="migration">Migration</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Hero Slides</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{content?.hero?.slides?.length || 0}</div>
                <p className="text-xs text-muted-foreground">Active slides</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Announcements</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{content?.announcements?.length || 0}</div>
                <p className="text-xs text-muted-foreground">Active announcements</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Gallery Items</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{content?.gallery?.length || 0}</div>
                <p className="text-xs text-muted-foreground">Gallery images</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">About Sections</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{content?.about?.sections?.length || 0}</div>
                <p className="text-xs text-muted-foreground">Content sections</p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="site-info" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Site Information</CardTitle>
              <CardDescription>
                Update basic site information that appears across the website
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="site-name">Site Name</Label>
                  <Input
                    id="site-name"
                    value={siteInfo.name}
                    onChange={(e) => setSiteInfo(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Enter site name"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="site-tagline">Tagline</Label>
                  <Input
                    id="site-tagline"
                    value={siteInfo.tagline}
                    onChange={(e) => setSiteInfo(prev => ({ ...prev, tagline: e.target.value }))}
                    placeholder="Enter site tagline"
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="site-description">Description</Label>
                <Textarea
                  id="site-description"
                  value={siteInfo.description}
                  onChange={(e) => setSiteInfo(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Enter site description"
                  rows={3}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="site-logo">Logo URL</Label>
                <Input
                  id="site-logo"
                  value={siteInfo.logo}
                  onChange={(e) => setSiteInfo(prev => ({ ...prev, logo: e.target.value }))}
                  placeholder="Enter logo URL"
                />
              </div>
              
              <Button onClick={handleSaveSiteInfo}>
                <Save className="w-4 h-4 mr-2" />
                Save Site Information
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="hero" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Hero Section Settings</CardTitle>
              <CardDescription>
                Configure hero slider behavior and timing
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="autoplay"
                  checked={heroSettings.isAutoplay}
                  onCheckedChange={(checked) => setHeroSettings(prev => ({ ...prev, isAutoplay: checked }))}
                />
                <Label htmlFor="autoplay">Enable Autoplay</Label>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="autoplay-speed">Autoplay Speed (milliseconds)</Label>
                <Input
                  id="autoplay-speed"
                  type="number"
                  value={heroSettings.autoplaySpeed}
                  onChange={(e) => setHeroSettings(prev => ({ ...prev, autoplaySpeed: parseInt(e.target.value) || 5000 }))}
                  min="1000"
                  max="10000"
                  step="500"
                />
              </div>
              
              <Button onClick={handleSaveHeroSettings}>
                <Save className="w-4 h-4 mr-2" />
                Save Hero Settings
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="import-export" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Export Content</CardTitle>
                <CardDescription>
                  Download all website content as a JSON file for backup
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button 
                  onClick={handleExportContent} 
                  disabled={isExporting}
                  className="w-full"
                >
                  {isExporting ? (
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  ) : (
                    <Download className="w-4 h-4 mr-2" />
                  )}
                  {isExporting ? 'Exporting...' : 'Export Content'}
                </Button>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Import Content</CardTitle>
                <CardDescription>
                  Upload and restore content from a JSON file
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="file-import">Upload File</Label>
                  <Input
                    id="file-import"
                    type="file"
                    accept=".json"
                    onChange={handleFileImport}
                  />
                </div>
                
                <div>
                  <Label htmlFor="content-data">Or Paste Content Data</Label>
                  <Textarea
                    id="content-data"
                    value={importData}
                    onChange={(e) => setImportData(e.target.value)}
                    placeholder="Paste JSON content here..."
                    rows={4}
                  />
                </div>
                
                <Button 
                  onClick={handleImportContent} 
                  disabled={isImporting || !importData.trim()}
                  className="w-full"
                >
                  {isImporting ? (
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  ) : (
                    <Upload className="w-4 h-4 mr-2" />
                  )}
                  {isImporting ? 'Importing...' : 'Import Content'}
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="migration" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Content Migration</CardTitle>
              <CardDescription>
                Migrate static content to the dynamic content management system
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="p-4 border rounded-lg bg-muted/50">
                <h4 className="font-medium mb-2">Migration includes:</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Static page content to dynamic sections</li>
                  <li>• Hero images and text</li>
                  <li>• Service information</li>
                  <li>• Event details</li>
                  <li>• Gallery items</li>
                  <li>• About page sections</li>
                </ul>
              </div>
              
              <Button 
                onClick={handleMigrateContent} 
                disabled={isMigrating}
                className="w-full"
              >
                {isMigrating ? (
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Settings className="w-4 h-4 mr-2" />
                )}
                {isMigrating ? 'Migrating...' : 'Start Migration'}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
