import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  TrendingUp, 
  TrendingDown, 
  Users, 
  Calendar, 
  Eye, 
  MessageSquare,
  Download,
  RefreshCw,
  BarChart3,
  PieChart,
  Activity,
  Globe,
  Smartphone,
  Monitor,
  Clock
} from 'lucide-react';
import { AnimatedCounter } from '@/components/3d/FloatingElements';

interface AnalyticsData {
  totalVisitors: number;
  totalEvents: number;
  totalDonations: number;
  activeUsers: number;
  pageViews: number;
  bounceRate: number;
  avgSessionDuration: string;
  topPages: Array<{ page: string; views: number; change: number }>;
  deviceStats: Array<{ device: string; percentage: number; count: number }>;
  eventAttendance: Array<{ event: string; attendees: number; capacity: number }>;
  monthlyVisitors: Array<{ month: string; visitors: number; events: number }>;
  recentActivity: Array<{ action: string; user: string; time: string; type: 'info' | 'success' | 'warning' }>;
}

const mockAnalytics: AnalyticsData = {
  totalVisitors: 12547,
  totalEvents: 45,
  totalDonations: 89650,
  activeUsers: 234,
  pageViews: 45678,
  bounceRate: 32.5,
  avgSessionDuration: '3m 45s',
  topPages: [
    { page: '/events', views: 8945, change: 12.5 },
    { page: '/about', views: 6234, change: -3.2 },
    { page: '/gallery', views: 4567, change: 8.7 },
    { page: '/contact', views: 3456, change: 15.3 },
    { page: '/services', views: 2345, change: -1.8 }
  ],
  deviceStats: [
    { device: 'Mobile', percentage: 65, count: 8156 },
    { device: 'Desktop', percentage: 28, count: 3513 },
    { device: 'Tablet', percentage: 7, count: 878 }
  ],
  eventAttendance: [
    { event: 'Sri Saraswati Yagnam', attendees: 450, capacity: 500 },
    { event: 'Vasant Panchami', attendees: 280, capacity: 300 },
    { event: 'Weekly Puja', attendees: 120, capacity: 150 },
    { event: 'Bhajan Evening', attendees: 85, capacity: 100 }
  ],
  monthlyVisitors: [
    { month: 'Jan', visitors: 8500, events: 4 },
    { month: 'Feb', visitors: 9200, events: 6 },
    { month: 'Mar', visitors: 11000, events: 8 },
    { month: 'Apr', visitors: 10500, events: 5 },
    { month: 'May', visitors: 12547, events: 7 }
  ],
  recentActivity: [
    { action: 'New event created: Sri Saraswati Yagnam', user: 'Admin', time: '2 minutes ago', type: 'success' },
    { action: 'Gallery updated with 15 new photos', user: 'Editor', time: '1 hour ago', type: 'info' },
    { action: 'High traffic detected on events page', user: 'System', time: '2 hours ago', type: 'warning' },
    { action: 'New user registration: <EMAIL>', user: 'System', time: '3 hours ago', type: 'info' },
    { action: 'Donation received: ₹5,000', user: 'System', time: '4 hours ago', type: 'success' }
  ]
};

export const AnalyticsDashboard: React.FC = () => {
  const [analytics] = useState<AnalyticsData>(mockAnalytics);
  const [timeRange, setTimeRange] = useState('30d');
  const [isLoading, setIsLoading] = useState(false);

  const handleRefresh = async () => {
    setIsLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsLoading(false);
  };

  const StatCard = ({ 
    title, 
    value, 
    change, 
    icon: Icon, 
    suffix = '', 
    prefix = '',
    animated = false 
  }: {
    title: string;
    value: number | string;
    change?: number;
    icon: React.ElementType;
    suffix?: string;
    prefix?: string;
    animated?: boolean;
  }) => (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-muted-foreground">{title}</p>
            <div className="text-2xl font-bold">
              {prefix}
              {animated && typeof value === 'number' ? (
                <AnimatedCounter end={value} suffix={suffix} />
              ) : (
                `${value}${suffix}`
              )}
            </div>
            {change !== undefined && (
              <div className={`flex items-center text-xs ${change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {change >= 0 ? <TrendingUp className="w-3 h-3 mr-1" /> : <TrendingDown className="w-3 h-3 mr-1" />}
                {Math.abs(change)}% from last month
              </div>
            )}
          </div>
          <div className="w-8 h-8 bg-muted rounded-lg flex items-center justify-center">
            <Icon className="w-4 h-4 text-muted-foreground" />
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const ProgressBar = ({ value, max, label, color = 'bg-blue-500' }: {
    value: number;
    max: number;
    label: string;
    color?: string;
  }) => {
    const percentage = (value / max) * 100;
    return (
      <div className="space-y-2">
        <div className="flex justify-between text-sm">
          <span>{label}</span>
          <span>{value} / {max}</span>
        </div>
        <div className="w-full bg-muted rounded-full h-2">
          <div 
            className={`h-2 rounded-full transition-all duration-500 ${color}`}
            style={{ width: `${percentage}%` }}
          />
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold">Analytics Dashboard</h1>
          <p className="text-muted-foreground">Monitor your temple website performance and engagement</p>
        </div>
        
        <div className="flex items-center gap-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
          
          <Button variant="outline" size="sm" onClick={handleRefresh} disabled={isLoading}>
            <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Visitors"
          value={analytics.totalVisitors}
          change={15.2}
          icon={Users}
          animated
        />
        <StatCard
          title="Active Events"
          value={analytics.totalEvents}
          change={8.7}
          icon={Calendar}
          animated
        />
        <StatCard
          title="Total Donations"
          value={analytics.totalDonations}
          change={23.1}
          icon={TrendingUp}
          prefix="₹"
          animated
        />
        <StatCard
          title="Active Users"
          value={analytics.activeUsers}
          change={-2.3}
          icon={Activity}
          animated
        />
      </div>

      {/* Detailed Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Website Performance */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="w-5 h-5" />
              Website Performance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  <AnimatedCounter end={analytics.pageViews} />
                </div>
                <p className="text-sm text-muted-foreground">Page Views</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{analytics.bounceRate}%</div>
                <p className="text-sm text-muted-foreground">Bounce Rate</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{analytics.avgSessionDuration}</div>
                <p className="text-sm text-muted-foreground">Avg. Session</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Device Statistics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChart className="w-5 h-5" />
              Device Usage
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analytics.deviceStats.map((device, index) => {
                const icons = { Mobile: Smartphone, Desktop: Monitor, Tablet: Smartphone };
                const Icon = icons[device.device as keyof typeof icons];
                const colors = ['bg-blue-500', 'bg-green-500', 'bg-purple-500'];
                
                return (
                  <div key={device.device} className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Icon className="w-4 h-4" />
                      <span className="text-sm">{device.device}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-20 bg-muted rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full ${colors[index]}`}
                          style={{ width: `${device.percentage}%` }}
                        />
                      </div>
                      <span className="text-sm font-medium w-8">{device.percentage}%</span>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Tables */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Pages */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="w-5 h-5" />
              Top Pages
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.topPages.map((page, index) => (
                <div key={page.page} className="flex items-center justify-between p-3 rounded-lg bg-muted/50">
                  <div>
                    <div className="font-medium">{page.page}</div>
                    <div className="text-sm text-muted-foreground">{page.views.toLocaleString()} views</div>
                  </div>
                  <Badge variant={page.change >= 0 ? 'default' : 'destructive'}>
                    {page.change >= 0 ? '+' : ''}{page.change}%
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Event Attendance */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="w-5 h-5" />
              Event Attendance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analytics.eventAttendance.map((event) => (
                <ProgressBar
                  key={event.event}
                  value={event.attendees}
                  max={event.capacity}
                  label={event.event}
                  color={event.attendees / event.capacity > 0.8 ? 'bg-green-500' : 'bg-blue-500'}
                />
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="w-5 h-5" />
            Recent Activity
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {analytics.recentActivity.map((activity, index) => (
              <div key={index} className="flex items-start gap-3 p-3 rounded-lg bg-muted/50">
                <div className={`w-2 h-2 rounded-full mt-2 ${
                  activity.type === 'success' ? 'bg-green-500' :
                  activity.type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500'
                }`} />
                <div className="flex-1">
                  <p className="text-sm">{activity.action}</p>
                  <div className="flex items-center gap-2 mt-1">
                    <span className="text-xs text-muted-foreground">{activity.user}</span>
                    <span className="text-xs text-muted-foreground">•</span>
                    <span className="text-xs text-muted-foreground">{activity.time}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
