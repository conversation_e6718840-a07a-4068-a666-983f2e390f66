import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  IndianRupee, 
  TrendingUp, 
  Users, 
  Calendar,
  Search,
  Filter,
  Download,
  Plus,
  Eye,
  Edit,
  Trash2,
  Heart,
  Gift,
  Star,
  CheckCircle,
  Clock,
  AlertCircle
} from 'lucide-react';
import { format } from 'date-fns';
import { AnimatedCounter } from '@/components/3d/FloatingElements';

interface Donation {
  id: string;
  donorName: string;
  donorEmail: string;
  donorPhone: string;
  amount: number;
  purpose: string;
  category: 'general' | 'yagnam' | 'construction' | 'festival' | 'annadanam';
  status: 'completed' | 'pending' | 'failed' | 'refunded';
  paymentMethod: 'online' | 'cash' | 'cheque' | 'bank_transfer';
  transactionId?: string;
  date: Date;
  isAnonymous: boolean;
  message?: string;
  receiptSent: boolean;
}

const mockDonations: Donation[] = [
  {
    id: '1',
    donorName: 'Rajesh Kumar',
    donorEmail: '<EMAIL>',
    donorPhone: '+91 **********',
    amount: 5000,
    purpose: 'Sri Saraswati Yagnam',
    category: 'yagnam',
    status: 'completed',
    paymentMethod: 'online',
    transactionId: 'TXN123456789',
    date: new Date('2024-12-20'),
    isAnonymous: false,
    message: 'For the success of the yagnam',
    receiptSent: true
  },
  {
    id: '2',
    donorName: 'Anonymous Devotee',
    donorEmail: '<EMAIL>',
    donorPhone: '+91 **********',
    amount: 2500,
    purpose: 'General Temple Fund',
    category: 'general',
    status: 'completed',
    paymentMethod: 'cash',
    date: new Date('2024-12-19'),
    isAnonymous: true,
    receiptSent: false
  },
  {
    id: '3',
    donorName: 'Priya Sharma',
    donorEmail: '<EMAIL>',
    donorPhone: '+91 9876543212',
    amount: 1000,
    purpose: 'Annadanam Service',
    category: 'annadanam',
    status: 'pending',
    paymentMethod: 'online',
    transactionId: 'TXN123456790',
    date: new Date('2024-12-21'),
    isAnonymous: false,
    message: 'Happy to contribute for feeding devotees',
    receiptSent: false
  }
];

export const DonationManagement: React.FC = () => {
  const [donations, setDonations] = useState<Donation[]>(mockDonations);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterCategory, setFilterCategory] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [selectedDonation, setSelectedDonation] = useState<Donation | null>(null);
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);

  const categoryColors = {
    general: 'bg-blue-100 text-blue-800',
    yagnam: 'bg-purple-100 text-purple-800',
    construction: 'bg-orange-100 text-orange-800',
    festival: 'bg-green-100 text-green-800',
    annadanam: 'bg-yellow-100 text-yellow-800'
  };

  const statusColors = {
    completed: 'bg-green-100 text-green-800',
    pending: 'bg-yellow-100 text-yellow-800',
    failed: 'bg-red-100 text-red-800',
    refunded: 'bg-gray-100 text-gray-800'
  };

  const statusIcons = {
    completed: CheckCircle,
    pending: Clock,
    failed: AlertCircle,
    refunded: AlertCircle
  };

  const filteredDonations = donations.filter(donation => {
    const matchesSearch = donation.donorName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         donation.purpose.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         donation.transactionId?.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = filterCategory === 'all' || donation.category === filterCategory;
    const matchesStatus = filterStatus === 'all' || donation.status === filterStatus;
    
    return matchesSearch && matchesCategory && matchesStatus;
  });

  const totalDonations = donations.reduce((sum, donation) => 
    donation.status === 'completed' ? sum + donation.amount : sum, 0
  );
  const totalDonors = new Set(donations.map(d => d.donorEmail)).size;
  const pendingAmount = donations
    .filter(d => d.status === 'pending')
    .reduce((sum, donation) => sum + donation.amount, 0);

  const DonationCard = ({ donation }: { donation: Donation }) => {
    const StatusIcon = statusIcons[donation.status];
    
    return (
      <Card className="hover:shadow-lg transition-shadow duration-200">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <CardTitle className="text-lg">
                  {donation.isAnonymous ? 'Anonymous Devotee' : donation.donorName}
                </CardTitle>
                {donation.amount >= 10000 && <Star className="w-4 h-4 text-yellow-500 fill-current" />}
              </div>
              <div className="flex items-center gap-2 flex-wrap">
                <Badge className={categoryColors[donation.category]}>
                  {donation.category.charAt(0).toUpperCase() + donation.category.slice(1)}
                </Badge>
                <Badge variant="outline" className={statusColors[donation.status]}>
                  <StatusIcon className="w-3 h-3 mr-1" />
                  {donation.status.charAt(0).toUpperCase() + donation.status.slice(1)}
                </Badge>
              </div>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-green-600">₹{donation.amount.toLocaleString()}</div>
              <div className="text-sm text-muted-foreground">{format(donation.date, 'MMM dd, yyyy')}</div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 mb-4">
            <div className="text-sm">
              <span className="font-medium">Purpose:</span> {donation.purpose}
            </div>
            <div className="text-sm">
              <span className="font-medium">Payment:</span> {donation.paymentMethod.replace('_', ' ').toUpperCase()}
            </div>
            {donation.transactionId && (
              <div className="text-sm">
                <span className="font-medium">Transaction ID:</span> {donation.transactionId}
              </div>
            )}
            {donation.message && (
              <div className="text-sm">
                <span className="font-medium">Message:</span> {donation.message}
              </div>
            )}
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {!donation.receiptSent && donation.status === 'completed' && (
                <Badge variant="outline" className="text-orange-600 border-orange-200">
                  Receipt Pending
                </Badge>
              )}
              {donation.receiptSent && (
                <Badge variant="outline" className="text-green-600 border-green-200">
                  Receipt Sent
                </Badge>
              )}
            </div>
            
            <div className="flex items-center gap-1">
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => {
                  setSelectedDonation(donation);
                  setIsDetailsOpen(true);
                }}
              >
                <Eye className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm">
                <Edit className="w-4 h-4" />
              </Button>
              {donation.status === 'completed' && !donation.receiptSent && (
                <Button variant="ghost" size="sm" className="text-blue-600">
                  Send Receipt
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold">Donation Management</h1>
          <p className="text-muted-foreground">Track and manage temple donations and contributions</p>
        </div>
        
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            Export Report
          </Button>
          <Button>
            <Plus className="w-4 h-4 mr-2" />
            Add Donation
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Donations</p>
                <div className="text-2xl font-bold text-green-600">
                  ₹<AnimatedCounter end={totalDonations} />
                </div>
              </div>
              <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <IndianRupee className="w-4 h-4 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Donors</p>
                <div className="text-2xl font-bold">
                  <AnimatedCounter end={totalDonors} />
                </div>
              </div>
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <Users className="w-4 h-4 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Pending Amount</p>
                <div className="text-2xl font-bold text-yellow-600">
                  ₹<AnimatedCounter end={pendingAmount} />
                </div>
              </div>
              <div className="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                <Clock className="w-4 h-4 text-yellow-600" />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">This Month</p>
                <div className="text-2xl font-bold">
                  ₹<AnimatedCounter end={Math.floor(totalDonations * 0.3)} />
                </div>
                <div className="flex items-center text-xs text-green-600">
                  <TrendingUp className="w-3 h-3 mr-1" />
                  +15% from last month
                </div>
              </div>
              <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                <Calendar className="w-4 h-4 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="Search donations..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <div className="flex gap-2">
              <Select value={filterCategory} onValueChange={setFilterCategory}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="general">General</SelectItem>
                  <SelectItem value="yagnam">Yagnam</SelectItem>
                  <SelectItem value="construction">Construction</SelectItem>
                  <SelectItem value="festival">Festival</SelectItem>
                  <SelectItem value="annadanam">Annadanam</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="failed">Failed</SelectItem>
                  <SelectItem value="refunded">Refunded</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Donations Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {filteredDonations.map((donation) => (
          <DonationCard key={donation.id} donation={donation} />
        ))}
      </div>

      {filteredDonations.length === 0 && (
        <Card>
          <CardContent className="pt-6 text-center py-12">
            <Heart className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="font-semibold mb-2">No donations found</h3>
            <p className="text-muted-foreground mb-4">
              {searchQuery || filterCategory !== 'all' || filterStatus !== 'all'
                ? 'Try adjusting your filters or search query'
                : 'No donations have been recorded yet'
              }
            </p>
          </CardContent>
        </Card>
      )}

      {/* Donation Details Dialog */}
      <Dialog open={isDetailsOpen} onOpenChange={setIsDetailsOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Donation Details</DialogTitle>
          </DialogHeader>
          {selectedDonation && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Donor Name</Label>
                  <p className="text-sm">{selectedDonation.isAnonymous ? 'Anonymous Devotee' : selectedDonation.donorName}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Amount</Label>
                  <p className="text-sm font-bold text-green-600">₹{selectedDonation.amount.toLocaleString()}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Email</Label>
                  <p className="text-sm">{selectedDonation.donorEmail}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Phone</Label>
                  <p className="text-sm">{selectedDonation.donorPhone}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Purpose</Label>
                  <p className="text-sm">{selectedDonation.purpose}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Category</Label>
                  <Badge className={categoryColors[selectedDonation.category]}>
                    {selectedDonation.category.charAt(0).toUpperCase() + selectedDonation.category.slice(1)}
                  </Badge>
                </div>
                <div>
                  <Label className="text-sm font-medium">Status</Label>
                  <Badge className={statusColors[selectedDonation.status]}>
                    {selectedDonation.status.charAt(0).toUpperCase() + selectedDonation.status.slice(1)}
                  </Badge>
                </div>
                <div>
                  <Label className="text-sm font-medium">Payment Method</Label>
                  <p className="text-sm">{selectedDonation.paymentMethod.replace('_', ' ').toUpperCase()}</p>
                </div>
                {selectedDonation.transactionId && (
                  <div className="col-span-2">
                    <Label className="text-sm font-medium">Transaction ID</Label>
                    <p className="text-sm font-mono">{selectedDonation.transactionId}</p>
                  </div>
                )}
                {selectedDonation.message && (
                  <div className="col-span-2">
                    <Label className="text-sm font-medium">Message</Label>
                    <p className="text-sm">{selectedDonation.message}</p>
                  </div>
                )}
              </div>
              
              <div className="flex justify-end gap-2 pt-4">
                <Button variant="outline">Download Receipt</Button>
                {!selectedDonation.receiptSent && selectedDonation.status === 'completed' && (
                  <Button>Send Receipt</Button>
                )}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};
