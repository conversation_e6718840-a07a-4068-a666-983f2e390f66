import React, { useState, useRef, useCallback } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  Eye,
  Upload,
  Type,
  Image as ImageIcon,
  Video,
  Save,
  Undo,
  Redo,
  Layers,
  Settings,
  Trash2,
  Copy,
  Move,
  RotateCcw,
  Edit,
  Monitor,
  Smartphone,
  Tablet
} from 'lucide-react';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { MediaEditor } from './MediaEditor';
import { TextEditor } from './TextEditor';
import { toast } from 'sonner';

interface PageElement {
  id: string;
  type: 'text' | 'image' | 'video' | 'section';
  content: any;
  styles: {
    position: { x: number; y: number };
    size: { width: number; height: number };
    zIndex: number;
  };
  metadata?: any;
}

interface PageData {
  id: string;
  name: string;
  elements: PageElement[];
  settings: {
    background: string;
    width: string;
    height: string;
  };
}

export const VisualPageBuilder: React.FC = () => {
  const [currentPage, setCurrentPage] = useState<PageData>({
    id: 'home',
    name: 'Home Page',
    elements: [],
    settings: {
      background: '#ffffff',
      width: '100%',
      height: '100vh'
    }
  });

  const [selectedElement, setSelectedElement] = useState<string | null>(null);
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [draggedElement, setDraggedElement] = useState<PageElement | null>(null);
  const [history, setHistory] = useState<PageData[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [showMediaEditor, setShowMediaEditor] = useState(false);
  const [showTextEditor, setShowTextEditor] = useState(false);
  const [editingElement, setEditingElement] = useState<PageElement | null>(null);
  const [viewportMode, setViewportMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop');

  const canvasRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Save to local storage
  const saveToLocalStorage = useCallback(() => {
    localStorage.setItem('pageBuilder_draft', JSON.stringify(currentPage));
    toast.success('Draft saved locally');
  }, [currentPage]);

  // Load from local storage
  const loadFromLocalStorage = useCallback(() => {
    const saved = localStorage.getItem('pageBuilder_draft');
    if (saved) {
      setCurrentPage(JSON.parse(saved));
      toast.success('Draft loaded');
    }
  }, []);

  // Add to history for undo/redo
  const addToHistory = useCallback((page: PageData) => {
    const newHistory = history.slice(0, historyIndex + 1);
    newHistory.push(JSON.parse(JSON.stringify(page)));
    setHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);
  }, [history, historyIndex]);

  // Undo
  const undo = useCallback(() => {
    if (historyIndex > 0) {
      setHistoryIndex(historyIndex - 1);
      setCurrentPage(history[historyIndex - 1]);
    }
  }, [history, historyIndex]);

  // Redo
  const redo = useCallback(() => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(historyIndex + 1);
      setCurrentPage(history[historyIndex + 1]);
    }
  }, [history, historyIndex]);

  // Handle file upload
  const handleFileUpload = useCallback((file: File, type: 'image' | 'video') => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const newElement: PageElement = {
        id: `${type}_${Date.now()}`,
        type,
        content: {
          src: e.target?.result,
          alt: file.name,
          originalFile: file
        },
        styles: {
          position: { x: 100, y: 100 },
          size: { width: type === 'image' ? 300 : 400, height: type === 'image' ? 200 : 225 },
          zIndex: currentPage.elements.length + 1
        },
        metadata: {
          fileName: file.name,
          fileSize: file.size,
          fileType: file.type
        }
      };

      const updatedPage = {
        ...currentPage,
        elements: [...currentPage.elements, newElement]
      };
      
      addToHistory(currentPage);
      setCurrentPage(updatedPage);
      saveToLocalStorage();
      toast.success(`${type} uploaded successfully`);
    };
    reader.readAsDataURL(file);
  }, [currentPage, addToHistory, saveToLocalStorage]);

  // Add text element
  const addTextElement = useCallback(() => {
    const newElement: PageElement = {
      id: `text_${Date.now()}`,
      type: 'text',
      content: {
        text: 'Click to edit text',
        fontSize: 16,
        fontFamily: 'Arial',
        color: '#000000',
        fontWeight: 'normal',
        textAlign: 'left',
        textDecoration: 'none'
      },
      styles: {
        position: { x: 100, y: 100 },
        size: { width: 200, height: 50 },
        zIndex: currentPage.elements.length + 1
      }
    };

    const updatedPage = {
      ...currentPage,
      elements: [...currentPage.elements, newElement]
    };
    
    addToHistory(currentPage);
    setCurrentPage(updatedPage);
    saveToLocalStorage();
  }, [currentPage, addToHistory, saveToLocalStorage]);

  // Delete element
  const deleteElement = useCallback((elementId: string) => {
    const updatedPage = {
      ...currentPage,
      elements: currentPage.elements.filter(el => el.id !== elementId)
    };
    
    addToHistory(currentPage);
    setCurrentPage(updatedPage);
    setSelectedElement(null);
    saveToLocalStorage();
  }, [currentPage, addToHistory, saveToLocalStorage]);

  // Duplicate element
  const duplicateElement = useCallback((elementId: string) => {
    const element = currentPage.elements.find(el => el.id === elementId);
    if (element) {
      const newElement: PageElement = {
        ...JSON.parse(JSON.stringify(element)),
        id: `${element.type}_${Date.now()}`,
        styles: {
          ...element.styles,
          position: {
            x: element.styles.position.x + 20,
            y: element.styles.position.y + 20
          },
          zIndex: currentPage.elements.length + 1
        }
      };

      const updatedPage = {
        ...currentPage,
        elements: [...currentPage.elements, newElement]
      };
      
      addToHistory(currentPage);
      setCurrentPage(updatedPage);
      saveToLocalStorage();
    }
  }, [currentPage, addToHistory, saveToLocalStorage]);

  // Handle drag and drop
  const onDragEnd = useCallback((result: any) => {
    if (!result.destination) return;

    const items = Array.from(currentPage.elements);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    const updatedPage = {
      ...currentPage,
      elements: items
    };
    
    setCurrentPage(updatedPage);
    saveToLocalStorage();
  }, [currentPage, saveToLocalStorage]);

  // Edit element
  const editElement = useCallback((elementId: string) => {
    const element = currentPage.elements.find(el => el.id === elementId);
    if (element) {
      setEditingElement(element);
      if (element.type === 'text') {
        setShowTextEditor(true);
      } else if (element.type === 'image' || element.type === 'video') {
        setShowMediaEditor(true);
      }
    }
  }, [currentPage.elements]);

  // Save edited element
  const saveEditedElement = useCallback((content: any, styles?: any) => {
    if (editingElement) {
      const updatedElements = currentPage.elements.map(el =>
        el.id === editingElement.id
          ? {
              ...el,
              content: { ...el.content, ...content },
              ...(styles && { styles: { ...el.styles, ...styles } })
            }
          : el
      );

      const updatedPage = {
        ...currentPage,
        elements: updatedElements
      };

      addToHistory(currentPage);
      setCurrentPage(updatedPage);
      saveToLocalStorage();

      setShowTextEditor(false);
      setShowMediaEditor(false);
      setEditingElement(null);
      toast.success('Element updated successfully');
    }
  }, [editingElement, currentPage, addToHistory, saveToLocalStorage]);

  // Get viewport dimensions
  const getViewportDimensions = useCallback(() => {
    switch (viewportMode) {
      case 'mobile':
        return { width: '375px', height: '667px' };
      case 'tablet':
        return { width: '768px', height: '1024px' };
      default:
        return { width: '1200px', height: '800px' };
    }
  }, [viewportMode]);

  // Push to live site
  const pushToLive = useCallback(() => {
    // Here you would implement the logic to push changes to the live site
    // This could involve API calls to update the content management system
    localStorage.setItem('pageBuilder_live', JSON.stringify(currentPage));
    toast.success('Changes pushed to live site!');
  }, [currentPage]);

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h1 className="text-xl font-bold text-gray-900">Visual Page Builder</h1>
            <span className="text-sm text-gray-500">Editing: {currentPage.name}</span>
          </div>
          
          <div className="flex items-center space-x-2">
            {/* Viewport Controls */}
            <div className="flex items-center space-x-1 border rounded-md p-1">
              <Button
                variant={viewportMode === 'desktop' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewportMode('desktop')}
              >
                <Monitor className="w-4 h-4" />
              </Button>
              <Button
                variant={viewportMode === 'tablet' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewportMode('tablet')}
              >
                <Tablet className="w-4 h-4" />
              </Button>
              <Button
                variant={viewportMode === 'mobile' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewportMode('mobile')}
              >
                <Smartphone className="w-4 h-4" />
              </Button>
            </div>

            <Button variant="outline" size="sm" onClick={undo} disabled={historyIndex <= 0}>
              <Undo className="w-4 h-4" />
            </Button>
            <Button variant="outline" size="sm" onClick={redo} disabled={historyIndex >= history.length - 1}>
              <Redo className="w-4 h-4" />
            </Button>
            <Button variant="outline" size="sm" onClick={saveToLocalStorage}>
              <Save className="w-4 h-4 mr-2" />
              Save Draft
            </Button>
            <Button variant="outline" size="sm" onClick={() => setIsPreviewMode(!isPreviewMode)}>
              <Eye className="w-4 h-4 mr-2" />
              {isPreviewMode ? 'Edit' : 'Preview'}
            </Button>
            <Button onClick={pushToLive} className="bg-green-600 hover:bg-green-700">
              <Upload className="w-4 h-4 mr-2" />
              Push Live
            </Button>
          </div>
        </div>
      </div>

      <div className="flex-1 flex">
        {/* Sidebar - Tools */}
        {!isPreviewMode && (
          <div className="w-80 bg-white border-r border-gray-200 overflow-y-auto">
            <Tabs defaultValue="elements" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="elements">Elements</TabsTrigger>
                <TabsTrigger value="layers">Layers</TabsTrigger>
                <TabsTrigger value="settings">Settings</TabsTrigger>
              </TabsList>

              <TabsContent value="elements" className="p-4 space-y-4">
                <div className="space-y-2">
                  <h3 className="font-medium text-gray-900">Add Elements</h3>
                  
                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    onClick={addTextElement}
                  >
                    <Type className="w-4 h-4 mr-2" />
                    Add Text
                  </Button>
                  
                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <ImageIcon className="w-4 h-4 mr-2" />
                    Add Image
                  </Button>
                  
                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => {
                      const input = document.createElement('input');
                      input.type = 'file';
                      input.accept = 'video/*';
                      input.onchange = (e) => {
                        const file = (e.target as HTMLInputElement).files?.[0];
                        if (file) handleFileUpload(file, 'video');
                      };
                      input.click();
                    }}
                  >
                    <Video className="w-4 h-4 mr-2" />
                    Add Video
                  </Button>
                </div>

                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) handleFileUpload(file, 'image');
                  }}
                />
              </TabsContent>

              <TabsContent value="layers" className="p-4">
                <div className="space-y-2">
                  <h3 className="font-medium text-gray-900">Layers</h3>
                  
                  <DragDropContext onDragEnd={onDragEnd}>
                    <Droppable droppableId="layers">
                      {(provided) => (
                        <div {...provided.droppableProps} ref={provided.innerRef} className="space-y-1">
                          {currentPage.elements.map((element, index) => (
                            <Draggable key={element.id} draggableId={element.id} index={index}>
                              {(provided) => (
                                <div
                                  ref={provided.innerRef}
                                  {...provided.draggableProps}
                                  {...provided.dragHandleProps}
                                  className={`p-2 bg-gray-50 rounded border cursor-pointer flex items-center justify-between ${
                                    selectedElement === element.id ? 'border-blue-500 bg-blue-50' : ''
                                  }`}
                                  onClick={() => setSelectedElement(element.id)}
                                >
                                  <div className="flex items-center space-x-2">
                                    <Layers className="w-4 h-4 text-gray-400" />
                                    <span className="text-sm">
                                      {element.type} - {element.id.split('_')[1]}
                                    </span>
                                  </div>
                                  
                                  <div className="flex items-center space-x-1">
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        duplicateElement(element.id);
                                      }}
                                    >
                                      <Copy className="w-3 h-3" />
                                    </Button>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        deleteElement(element.id);
                                      }}
                                    >
                                      <Trash2 className="w-3 h-3" />
                                    </Button>
                                  </div>
                                </div>
                              )}
                            </Draggable>
                          ))}
                          {provided.placeholder}
                        </div>
                      )}
                    </Droppable>
                  </DragDropContext>
                </div>
              </TabsContent>

              <TabsContent value="settings" className="p-4">
                <div className="space-y-4">
                  <h3 className="font-medium text-gray-900">Page Settings</h3>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Background Color</label>
                    <input
                      type="color"
                      value={currentPage.settings.background}
                      onChange={(e) => {
                        setCurrentPage({
                          ...currentPage,
                          settings: {
                            ...currentPage.settings,
                            background: e.target.value
                          }
                        });
                      }}
                      className="w-full h-10 rounded border"
                    />
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        )}

        {/* Canvas */}
        <div className="flex-1 overflow-auto bg-gray-100 p-4">
          <div className="flex justify-center">
            <div
              ref={canvasRef}
              className="bg-white shadow-lg relative transition-all duration-300"
              style={{
                ...getViewportDimensions(),
                minHeight: currentPage.settings.height,
                backgroundColor: currentPage.settings.background,
                transform: viewportMode === 'mobile' ? 'scale(0.8)' : viewportMode === 'tablet' ? 'scale(0.9)' : 'scale(1)',
                transformOrigin: 'top center'
              }}
            >
            {currentPage.elements.map((element) => (
              <div
                key={element.id}
                className={`absolute cursor-pointer group ${
                  selectedElement === element.id ? 'ring-2 ring-blue-500' : ''
                }`}
                style={{
                  left: element.styles.position.x,
                  top: element.styles.position.y,
                  width: element.styles.size.width,
                  height: element.styles.size.height,
                  zIndex: element.styles.zIndex
                }}
                onClick={() => setSelectedElement(element.id)}
              >
                {/* Edit Button */}
                {!isPreviewMode && selectedElement === element.id && (
                  <div className="absolute -top-8 right-0 flex space-x-1">
                    <Button
                      size="sm"
                      variant="secondary"
                      onClick={(e) => {
                        e.stopPropagation();
                        editElement(element.id);
                      }}
                      className="h-6 px-2"
                    >
                      <Edit className="w-3 h-3" />
                    </Button>
                    <Button
                      size="sm"
                      variant="secondary"
                      onClick={(e) => {
                        e.stopPropagation();
                        duplicateElement(element.id);
                      }}
                      className="h-6 px-2"
                    >
                      <Copy className="w-3 h-3" />
                    </Button>
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={(e) => {
                        e.stopPropagation();
                        deleteElement(element.id);
                      }}
                      className="h-6 px-2"
                    >
                      <Trash2 className="w-3 h-3" />
                    </Button>
                  </div>
                )}
                {element.type === 'text' && (
                  <div
                    style={{
                      fontSize: element.content.fontSize,
                      fontFamily: element.content.fontFamily,
                      color: element.content.color,
                      fontWeight: element.content.fontWeight,
                      textAlign: element.content.textAlign,
                      textDecoration: element.content.textDecoration
                    }}
                    contentEditable={!isPreviewMode}
                    suppressContentEditableWarning
                  >
                    {element.content.text}
                  </div>
                )}
                
                {element.type === 'image' && (
                  <img
                    src={element.content.src}
                    alt={element.content.alt}
                    className="w-full h-full object-cover rounded"
                  />
                )}
                
                {element.type === 'video' && (
                  <video
                    src={element.content.src}
                    controls
                    className="w-full h-full rounded"
                  />
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Text Editor Modal */}
      {showTextEditor && editingElement && editingElement.type === 'text' && (
        <TextEditor
          initialText={editingElement.content.text}
          initialStyles={editingElement.content}
          onSave={(text, styles) => {
            saveEditedElement({ text, ...styles });
          }}
          onClose={() => {
            setShowTextEditor(false);
            setEditingElement(null);
          }}
        />
      )}

      {/* Media Editor Modal */}
      {showMediaEditor && editingElement && (editingElement.type === 'image' || editingElement.type === 'video') && (
        <MediaEditor
          file={editingElement.content.src}
          type={editingElement.type}
          onSave={(editedFile) => {
            if (typeof editedFile === 'string') {
              saveEditedElement({ src: editedFile });
            } else {
              const reader = new FileReader();
              reader.onload = (e) => {
                saveEditedElement({ src: e.target?.result });
              };
              reader.readAsDataURL(editedFile);
            }
          }}
          onClose={() => {
            setShowMediaEditor(false);
            setEditingElement(null);
          }}
        />
      )}
    </div>
  );
};
