
import { useState, useEffect } from 'react';
import { X, Calendar, MapPin, Share } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { supabase } from '@/integrations/supabase/client';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';

interface EventPopupProps {
  eventId: string;
  onClose: () => void;
}

export function EventPopup({ eventId, onClose }: EventPopupProps) {
  const [event, setEvent] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Use a hardcoded event for the Yagnam
    const yagnamEvent = {
      id: "yagnam-2025",
      title: "504th Sri Saraswati Yagnam",
      date: "2025-05-25",
      time: "8:00 AM - 1:00 PM",
      description: "Join us for the sacred 504th Sri Saraswati Yagnam ceremony and receive divine blessings. This auspicious event includes special rituals, prayers, and prasadam distribution.\n\nThe Saraswati Yagnam is a sacred Vedic ritual dedicated to Goddess <PERSON>swati, the deity of knowledge, wisdom, music, and arts. This ceremony is performed to seek blessings for intellectual growth, academic success, and creative inspiration.\n\nDuring the yagnam, learned priests will conduct elaborate fire rituals (homa) while chanting Vedic mantras. Devotees are invited to participate in this auspicious ceremony and receive the divine blessings of Goddess Saraswati.",
      image_url: "/images/events/yagnam.jpg",
      location: "Anantasagar Kshetramu Main Temple Hall"
    };

    setEvent(yagnamEvent);
    setLoading(false);
  }, [eventId]);

  const handleShare = () => {
    const shareTitle = "504th Sri Saraswati Yagnam - May 25, 2025";
    const shareText = "Join us for the sacred 504th Sri Saraswati Yagnam ceremony at Anantasagar Kshetramu on May 25, 2025. Receive divine blessings through this auspicious event.";
    const shareUrl = window.location.origin + '/events';

    if (navigator.share) {
      navigator.share({
        title: shareTitle,
        text: shareText,
        url: shareUrl,
      }).catch((error) => console.error('Error sharing:', error));
    } else {
      // Fallback for browsers that don't support the Web Share API
      toast("Share via:", {
        description: (
          <div className="flex gap-3 mt-2">
            <Button size="sm" variant="outline" onClick={() => window.open(`https://wa.me/?text=${encodeURIComponent(`${shareTitle}: ${shareText} ${shareUrl}`)}`, '_blank')}>
              WhatsApp
            </Button>
            <Button size="sm" variant="outline" onClick={() => window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}&quote=${encodeURIComponent(shareTitle + ": " + shareText)}`, '_blank')}>
              Facebook
            </Button>
            <Button size="sm" variant="outline" onClick={() => window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(shareTitle + ": " + shareText)}&url=${encodeURIComponent(shareUrl)}`, '_blank')}>
              Twitter
            </Button>
          </div>
        ),
        duration: 5000,
      });
    }
  };

  return (
    <Dialog open={!!eventId} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle className="pr-8">
            {loading ? <Skeleton className="h-6 w-3/4" /> : event?.title}
          </DialogTitle>
          {loading ? (
            <Skeleton className="h-4 w-2/3" />
          ) : (
            <DialogDescription>
              {event?.date && (
                <div className="flex items-center gap-2 mt-2">
                  <Calendar className="h-4 w-4 text-temple-gold" />
                  <span>{new Date(event.date).toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}</span>
                </div>
              )}
              {event?.location && (
                <div className="flex items-center gap-2 mt-1">
                  <MapPin className="h-4 w-4 text-temple-gold" />
                  <span>{event.location}</span>
                </div>
              )}
            </DialogDescription>
          )}
        </DialogHeader>

        {loading ? (
          <div className="space-y-3">
            <Skeleton className="h-[200px] w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-2/3" />
          </div>
        ) : (
          <>
            {event?.image_url && (
              <div className="aspect-video rounded-md overflow-hidden mb-4">
                <img
                  src={event.image_url}
                  alt={event.title}
                  className="w-full h-full object-cover"
                />
              </div>
            )}
            <div className="text-sm space-y-2">
              <p>{event?.description}</p>
            </div>
          </>
        )}

        <DialogFooter className="gap-2 sm:gap-0">
          <Button
            variant="outline"
            className="gap-2"
            onClick={handleShare}
          >
            <Share className="h-4 w-4" />
            Share
          </Button>
          <Button
            className="bg-temple-gold hover:bg-temple-saffron text-white"
            onClick={() => window.location.href = `/events`}
          >
            View All Events
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
