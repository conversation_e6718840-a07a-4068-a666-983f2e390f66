
import { Header } from "./Header";
import { Footer } from "./Footer";
import { Announcements } from "./Announcements";
import { Toaster } from "@/components/ui/toaster";
import { useEffect } from "react";
import { SEO } from "./SEO";
import { useLocation } from "react-router-dom";

interface LayoutProps {
  children: React.ReactNode;
  hideAnnouncements?: boolean;
  title?: string;
  description?: string;
  image?: string;
  schemaType?: 'Organization' | 'LocalBusiness' | 'WebPage' | 'Article' | 'Event';
}

export function Layout({
  children,
  hideAnnouncements = false,
  title,
  description,
  image,
  schemaType = 'WebPage'
}: LayoutProps) {
  const location = useLocation();

  // Scroll to top when route changes
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Generate page-specific SEO data
  const getPageSeoData = () => {
    const path = location.pathname;

    // Default SEO data
    let seoData = {
      title: title || 'Anantasagar Kshetramu - Sri Saraswathi Temple',
      description: description || 'Anantasagar Kshetramu is a sacred sanctuary dedicated to Goddess <PERSON><PERSON><PERSON><PERSON><PERSON>, an embodiment of divine feminine energy and infinite cosmic power.',
      image: image || '/images/AS_Overview.jpg',
      schemaType
    };

    // Page-specific SEO data
    if (path === '/') {
      seoData = {
        ...seoData,
        title: title || 'Anantasagar Kshetramu - Home | Sri Saraswathi Temple',
        description: description || 'Welcome to Anantasagar Kshetramu, a sacred sanctuary dedicated to Goddess Anantasagareshwari. Experience divine blessings and spiritual growth.',
        schemaType: 'Organization'
      };
    } else if (path === '/about') {
      seoData = {
        ...seoData,
        title: title || 'About Anantasagar Kshetramu | Temple History & Legacy',
        description: description || 'Learn about the rich history and spiritual legacy of Anantasagar Kshetramu, a temple dedicated to preserving Hindu traditions and serving the community.',
      };
    } else if (path === '/services') {
      seoData = {
        ...seoData,
        title: title || 'Temple Services | Pujas, Ceremonies & Rituals',
        description: description || 'Explore our range of traditional Hindu religious services, from daily rituals to special ceremonies for important life events.',
      };
    } else if (path === '/events') {
      seoData = {
        ...seoData,
        title: title || 'Temple Events & Festivals | Upcoming Celebrations',
        description: description || 'Stay updated with upcoming events, festivals and celebrations at Anantasagar Kshetramu Temple.',
        schemaType: 'Event'
      };
    } else if (path === '/contact') {
      seoData = {
        ...seoData,
        title: title || 'Contact Us | Anantasagar Kshetramu Temple',
        description: description || 'Get in touch with Anantasagar Kshetramu Temple. Find our location, contact details, and visiting hours.',
        schemaType: 'LocalBusiness'
      };
    }

    return seoData;
  };

  const seoData = getPageSeoData();

  return (
    <div className="flex flex-col min-h-screen">
      <SEO
        title={seoData.title}
        description={seoData.description}
        image={seoData.image}
        url={location.pathname}
        schemaType={seoData.schemaType as any}
      />
      <Header />
      {/* Announcements bar - with appropriate spacing for fixed header */}
      {!hideAnnouncements && (
        <div className="pt-[76px] md:pt-[84px]">
          <Announcements />
        </div>
      )}
      {/* Main content */}
      <main className={`flex-grow ${hideAnnouncements ? 'pt-[76px] md:pt-[84px]' : ''}`}>
        {children}
      </main>
      <Footer />
      <Toaster />
    </div>
  );
}
