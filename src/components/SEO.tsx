import { Helmet } from 'react-helmet-async';

interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: 'website' | 'article';
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  twitterHandle?: string;
  schemaType?: 'Organization' | 'LocalBusiness' | 'WebPage' | 'Article' | 'Event';
  children?: React.ReactNode;
}

export function SEO({
  title = 'Anantasagar Kshetramu - Sri Saraswathi Temple',
  description = 'Anantasagar Kshetramu is a sacred sanctuary dedicated to Goddess <PERSON><PERSON><PERSON><PERSON>, an embodiment of divine feminine energy and infinite cosmic power.',
  keywords = 'temple, hindu temple, anantasagar, kshetramu, saraswathi, goddess, spirituality, worship, puja, ceremonies',
  image = '/images/AS_Overview.jpg',
  url = 'https://anantasagareshwari.org',
  type = 'website',
  publishedTime,
  modifiedTime,
  author = 'Anantasagar Kshetramu',
  twitterHandle = '@anantasagar',
  schemaType = 'LocalBusiness',
  children,
}: SEOProps) {
  const siteUrl = 'https://anantasagareshwari.org';
  const fullUrl = url.startsWith('http') ? url : `${siteUrl}${url}`;
  const fullImage = image.startsWith('http') ? image : `${siteUrl}${image}`;
  
  // Generate structured data based on schema type
  const getStructuredData = () => {
    const baseData = {
      '@context': 'https://schema.org',
      '@type': schemaType,
      name: 'Anantasagar Kshetramu',
      description,
      url: siteUrl,
      logo: `${siteUrl}/images/logo.png`,
      image: fullImage,
    };
    
    if (schemaType === 'LocalBusiness') {
      return {
        ...baseData,
        address: {
          '@type': 'PostalAddress',
          streetAddress: 'Anantasagar Rajiv Rahadari Highway',
          addressLocality: 'Siddipet',
          addressRegion: 'Telangana',
          postalCode: '502103',
          addressCountry: 'IN'
        },
        geo: {
          '@type': 'GeoCoordinates',
          latitude: '18.205861254071316',
          longitude: '78.98599080741408'
        },
        telephone: '+918247721046',
        email: '<EMAIL>',
        openingHours: [
          'Mo-Sa 09:00-12:00',
          'Mo-Sa 16:00-18:00'
        ],
        priceRange: '₹₹'
      };
    }
    
    if (schemaType === 'Article') {
      return {
        ...baseData,
        headline: title,
        author: {
          '@type': 'Person',
          name: author
        },
        publisher: {
          '@type': 'Organization',
          name: 'Anantasagar Kshetramu',
          logo: {
            '@type': 'ImageObject',
            url: `${siteUrl}/images/logo.png`
          }
        },
        datePublished: publishedTime,
        dateModified: modifiedTime || publishedTime
      };
    }
    
    if (schemaType === 'Event') {
      return {
        ...baseData,
        startDate: publishedTime,
        endDate: modifiedTime,
        location: {
          '@type': 'Place',
          name: 'Anantasagar Kshetramu',
          address: {
            '@type': 'PostalAddress',
            streetAddress: 'Anantasagar Rajiv Rahadari Highway',
            addressLocality: 'Siddipet',
            addressRegion: 'Telangana',
            postalCode: '502103',
            addressCountry: 'IN'
          }
        },
        organizer: {
          '@type': 'Organization',
          name: 'Anantasagar Kshetramu',
          url: siteUrl
        }
      };
    }
    
    return baseData;
  };

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{title}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      <link rel="canonical" href={fullUrl} />
      
      {/* Open Graph / Facebook */}
      <meta property="og:type" content={type} />
      <meta property="og:url" content={fullUrl} />
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={fullImage} />
      <meta property="og:site_name" content="Anantasagar Kshetramu" />
      
      {/* Twitter */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:site" content={twitterHandle} />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={fullImage} />
      
      {/* Article specific meta tags */}
      {type === 'article' && publishedTime && (
        <>
          <meta property="article:published_time" content={publishedTime} />
          {modifiedTime && <meta property="article:modified_time" content={modifiedTime} />}
          <meta property="article:author" content={author} />
        </>
      )}
      
      {/* Structured Data / JSON-LD */}
      <script type="application/ld+json">
        {JSON.stringify(getStructuredData())}
      </script>
      
      {/* Additional SEO elements */}
      {children}
    </Helmet>
  );
}
