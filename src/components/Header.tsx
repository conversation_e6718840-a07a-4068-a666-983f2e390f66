
import { useState, useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { ThemeToggle } from "./ThemeToggle";
import { MusicPlayer } from "./MusicPlayer";
import { NotificationBell } from "./NotificationBell";
import { cn } from "@/lib/utils";
import { MenuIcon, X } from "lucide-react";
import { useContent } from "@/contexts/ContentContext";
import { MobileOptimized } from "@/components/mobile/MobileOptimized";

// Default navigation items as fallback
const defaultNavItems = [
  { name: "Home", href: "/" },
  { name: "About Temple", href: "/about" },
  { name: "Founders", href: "/founders" },
  { name: "Services", href: "/services" },
  { name: "Events", href: "/events" },
  { name: "Gallery", href: "/gallery" },
  { name: "Contact Us", href: "/contact" },
];

export function Header() {
  const { content, loading } = useContent();
  const [isScrolled, setIsScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const location = useLocation();

  // Get dynamic content or use defaults
  const siteName = content?.siteInfo?.name || "Anantasagar Kshetramu";
  const siteLogo = content?.siteInfo?.logo || "/images/logo.png";
  const navItems = content?.navigation?.mainMenu?.map(item => ({
    name: item.title,
    href: item.path
  })) || defaultNavItems;

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 0);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Close mobile menu when route changes
  useEffect(() => {
    setMobileMenuOpen(false);
  }, [location.pathname]);

  return (
    <MobileOptimized>
      <header
        className={cn(
          "fixed top-0 w-full z-50 transition-all duration-500",
          isScrolled
            ? "bg-background/95 backdrop-blur-md shadow-md py-2"
            : "bg-background/50 backdrop-blur-sm py-4"
        )}
      >
      <div className="container mx-auto px-4 flex items-center justify-between">
        {/* Logo */}
        <Link to="/" className="group transition-transform hover:opacity-90">
          <img
            src={siteLogo}
            alt={`${siteName} Logo`}
            className="h-12 w-auto object-contain transition-all duration-300 hover:scale-105"
          />
        </Link>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center space-x-2">
          {navItems.map((item) => (
            <Link
              key={item.name}
              to={item.href}
              className={cn(
                "px-4 py-2.5 rounded-lg text-sm font-semibold transition-all duration-300 relative group overflow-hidden",
                location.pathname === item.href
                  ? "text-white bg-temple-maroon shadow-md"
                  : "text-foreground hover:bg-temple-gold/10 hover:text-temple-maroon"
              )}
            >
              <span className="relative z-10">{item.name}</span>
              <span className={cn(
                "absolute bottom-0 left-0 w-full h-0.5 bg-temple-saffron transform origin-left transition-all duration-300 scale-x-0",
                location.pathname === item.href ? "scale-x-100 opacity-100" : "group-hover:scale-x-100 opacity-0 group-hover:opacity-100"
              )}></span>
            </Link>
          ))}
        </nav>

        {/* Right side controls */}
        <div className="flex items-center space-x-3">
          <NotificationBell />
          <ThemeToggle />
          <MusicPlayer />

          {/* Mobile menu button */}
          <Button
            variant="ghost"
            size="icon"
            className="md:hidden rounded-lg w-10 h-10 hover:bg-temple-gold/20 hover:text-temple-maroon transition-colors"
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            aria-label="Toggle menu"
          >
            {mobileMenuOpen ? (
              <X className="h-5 w-5" />
            ) : (
              <MenuIcon className="h-5 w-5" />
            )}
          </Button>
        </div>
      </div>

      {/* Mobile Navigation */}
      {mobileMenuOpen && (
        <div className="md:hidden absolute top-full left-0 right-0 bg-background/95 backdrop-blur-md shadow-md animate-fade-in">
          <div className="container mx-auto py-3 px-4">
            <nav className="flex flex-col space-y-1">
              {navItems.map((item) => (
                <Link
                  key={item.name}
                  to={item.href}
                  className={cn(
                    "flex items-center px-4 py-3 rounded-md text-base font-medium transition-colors",
                    location.pathname === item.href
                      ? "bg-primary/10 text-primary"
                      : "text-foreground hover:bg-primary/5 hover:text-primary"
                  )}
                >
                  {item.name}
                </Link>
              ))}
            </nav>
          </div>
        </div>
      )}
    </header>
    </MobileOptimized>
  );
}
