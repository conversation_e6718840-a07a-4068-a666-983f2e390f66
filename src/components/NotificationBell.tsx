
import { useState, useEffect } from 'react';
import { Bell } from 'lucide-react';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useNotifications } from '@/contexts/NotificationContext';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { AutoEventPopup } from './AutoEventPopup';

export function NotificationBell() {
  const { notifications, unreadCount, markAsRead, markAllAsRead } = useNotifications();
  const [isOpen, setIsOpen] = useState(false);
  const [showEventPopup, setShowEventPopup] = useState<boolean>(false);
  const [hasInitialized, setHasInitialized] = useState(false);

  // Ensure we have at least one notification for the event
  useEffect(() => {
    if (!hasInitialized && notifications.length > 0) {
      setHasInitialized(true);
    }
  }, [notifications, hasInitialized]);

  const handleNotificationClick = (id: string, type: string, data: any) => {
    markAsRead(id);

    if (type === 'event' && data?.event_id === 'may-25-2025') {
      setShowEventPopup(true);
      setIsOpen(false);
    }
  };

  return (
    <>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button variant="ghost" size="icon" className="relative rounded-full">
            <Bell className={cn("h-5 w-5", unreadCount > 0 && "text-temple-orange animate-pulse")} />
            {unreadCount > 0 && (
              <span className="absolute -top-1 -right-1 h-4 w-4 rounded-full bg-temple-orange flex items-center justify-center text-[10px] text-white animate-ping shadow-lg shadow-temple-orange/30 ring-2 ring-temple-orange/50">
                {unreadCount > 9 ? '9+' : unreadCount}
              </span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent align="end" className="w-[90vw] sm:w-80 p-0">
          <div className="flex items-center justify-between p-3 sm:p-4 border-b">
            <h3 className="font-medium">Notifications</h3>
            {unreadCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                className="text-xs px-2 sm:px-3"
                onClick={markAllAsRead}
              >
                Mark all as read
              </Button>
            )}
          </div>
          <ScrollArea className="h-[300px]">
            {notifications.length > 0 ? (
              <div className="flex flex-col divide-y">
                {notifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={cn(
                      "p-3 sm:p-4 cursor-pointer hover:bg-muted transition-colors active:bg-muted",
                      !notification.read && "bg-accent/10"
                    )}
                    onClick={() => handleNotificationClick(notification.id, notification.type, notification.data)}
                  >
                    <div className="flex justify-between items-start gap-2">
                      <div className="flex-1">
                        <p className="font-medium text-sm sm:text-base">{notification.title}</p>
                        {notification.type === 'event' && notification.data?.event_id === 'may-25-2025' && (
                          <div className="mt-2 mb-2 rounded-md overflow-hidden border border-temple-gold/30 shadow-sm hover:shadow-md transition-shadow">
                            <img
                              src="/images/events/25-5-2025.jpeg"
                              alt="Sri Saraswati Yagnam"
                              className="w-full h-auto object-cover rounded-md cursor-pointer"
                              onClick={(e) => {
                                e.stopPropagation();
                                setShowEventPopup(true);
                                setIsOpen(false);
                              }}
                            />
                          </div>
                        )}
                        <p className="text-xs sm:text-sm text-muted-foreground line-clamp-2">
                          {notification.content}
                        </p>
                      </div>
                      {!notification.read && (
                        <div className="h-3 w-3 rounded-full bg-temple-orange flex-shrink-0 animate-pulse shadow-sm shadow-temple-orange/30" />
                      )}
                    </div>
                    <p className="text-xs text-muted-foreground mt-2">
                      {format(notification.createdAt, 'MMM dd, h:mm a')}
                    </p>
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center h-full p-4">
                <p className="text-muted-foreground">No notifications yet</p>
              </div>
            )}
          </ScrollArea>
        </PopoverContent>
      </Popover>

      {showEventPopup && (
        <AutoEventPopup
          onClose={() => setShowEventPopup(false)}
        />
      )}
    </>
  );
}
