export interface HeroSlide {
  id: string;
  title: string;
  subtitle?: string;
  description?: string;
  richDescription?: string;
  imageUrl: string;
  videoUrl?: string;
  thumbnail?: string;
  buttonText?: string;
  buttonLink?: string;
  buttonStyle?: 'primary' | 'secondary' | 'outline';
  order: number;
  backgroundColor?: string;
  overlayColor?: string;
  overlayOpacity?: number;
  alignment?: 'left' | 'center' | 'right';
  animation?: 'fade' | 'slide' | 'zoom';
  active: boolean;
}

export interface Announcement {
  id: string;
  title: string;
  content: string;
  type: 'info' | 'alert' | 'event';
  startDate: string;
  endDate?: string;
  isActive: boolean;
  priority: 'high' | 'medium' | 'low';
}

export interface MenuItem {
  id: string;
  title: string;
  path: string;
  order: number;
  parentId?: string;
  isExternal?: boolean;
}

export interface Review {
  id: string;
  name: string;
  rating: number;
  comment: string;
  date: string;
  avatar?: string;
  isApproved: boolean;
}

export interface Location {
  name: string;
  address: string;
  latitude: number;
  longitude: number;
  qrCode?: string;
}

export interface SocialMedia {
  platform: string;
  url: string;
  icon: string;
}

export interface AboutSection {
  id: string;
  title: string;
  content: string;
  image: string;
  order: number;
}

export interface AboutContent {
  hero: {
    showContent: boolean;
    title: string;
    subtitle: string;
    backgroundImage: string;
  };
  sections: AboutSection[];
}

export interface GalleryImage {
  id: string;
  url: string;
  caption?: string;
  order: number;
}

export interface FooterContent {
  text: string;
  links: { label: string; url: string }[];
  logo?: string;
  backgroundImage?: string;
}

export interface SiteInfo {
  name: string;
  tagline: string;
  description: string;
  logo?: string;
}

export interface ContactInfo {
  phone: string;
  email: string;
  address: {
    line1: string;
    line2: string;
  };
  coordinates: {
    lat: number;
    lng: number;
  };
}

export interface WebsiteContent {
  siteInfo: SiteInfo;
  hero: {
    slides: HeroSlide[];
    autoplaySpeed: number;
    isAutoplay: boolean;
  };
  announcements: Announcement[];
  navigation: {
    mainMenu: MenuItem[];
    footerMenu: MenuItem[];
  };
  reviews: Review[];
  locations: Location[];
  contact: ContactInfo;
  socialMedia: SocialMedia[];
  settings: {
    isMaintenanceMode: boolean;
    maintenanceMessage?: string;
    popups: {
      isEnabled: boolean;
      title: string;
      content: string;
      showDelay: number;
    };
    lastUpdate?: string;
  };
  about: AboutContent;
  gallery: GalleryImage[];
  footer: FooterContent;
  // Versioning
  _history?: WebsiteContent[];
  _historyTimestamps?: string[];
}
