/* Mobile Enhancements and 3D Effects */

/* Smooth scrolling for all devices */
html {
  scroll-behavior: smooth;
}

/* Enhanced mobile touch interactions */
@media (max-width: 768px) {
  /* Improved touch targets */
  button, .btn, a[role="button"] {
    min-height: 44px;
    min-width: 44px;
    padding: 12px 16px;
  }

  /* Better spacing for mobile */
  .container {
    padding-left: 16px;
    padding-right: 16px;
  }

  /* Mobile-optimized text sizes */
  h1 {
    font-size: 1.875rem; /* 30px */
    line-height: 1.2;
  }

  h2 {
    font-size: 1.5rem; /* 24px */
    line-height: 1.3;
  }

  h3 {
    font-size: 1.25rem; /* 20px */
    line-height: 1.4;
  }

  /* Improved mobile navigation */
  .mobile-nav {
    transform: translateX(-100%);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .mobile-nav.open {
    transform: translateX(0);
  }
}

/* 3D Card Effects */
.card-3d {
  transform-style: preserve-3d;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  perspective: 1000px;
}

.card-3d:hover {
  transform: rotateY(5deg) rotateX(5deg) translateZ(10px);
}

/* Floating Animation */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.float-animation {
  animation: float 3s ease-in-out infinite;
}

/* Glow Effects */
.glow-effect {
  position: relative;
  overflow: hidden;
}

.glow-effect::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 215, 0, 0.1), transparent);
  transform: rotate(45deg);
  transition: transform 0.6s;
  pointer-events: none;
}

.glow-effect:hover::before {
  transform: rotate(45deg) translate(50%, 50%);
}

/* Parallax Scrolling */
.parallax-element {
  transform: translateZ(0);
  will-change: transform;
}

/* Enhanced Button Styles */
.btn-3d {
  position: relative;
  transform-style: preserve-3d;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-3d:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.btn-3d:active {
  transform: translateY(0) scale(0.98);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Ripple Effect */
.ripple {
  position: relative;
  overflow: hidden;
}

.ripple::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.ripple:active::after {
  width: 300px;
  height: 300px;
}

/* Smooth Transitions */
.smooth-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Glass Morphism Effect */
.glass-morphism {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Gradient Text */
.gradient-text {
  background: linear-gradient(135deg, #DAA520, #FFD700, #FFA500);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Loading Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.pulse-animation {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Slide In Animations */
@keyframes slideInUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-30px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(30px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.slide-in-up {
  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.slide-in-left {
  animation: slideInLeft 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.slide-in-right {
  animation: slideInRight 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* Intersection Observer Animations */
.fade-in {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Enhanced Mobile Hero */
@media (max-width: 768px) {
  .hero-mobile {
    min-height: 60vh;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .hero-content-mobile {
    text-align: center;
    padding: 2rem 1rem;
  }

  .hero-title-mobile {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
  }

  .hero-subtitle-mobile {
    font-size: 1.125rem;
    margin-bottom: 2rem;
    opacity: 0.9;
  }
}

/* Improved Mobile Footer */
@media (max-width: 768px) {
  .footer-mobile {
    padding: 2rem 1rem;
  }

  .footer-grid-mobile {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .footer-section-mobile {
    margin-bottom: 1.5rem;
  }

  .footer-title-mobile {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
  }

  .footer-links-mobile {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }
}

/* Enhanced Mobile Cards */
@media (max-width: 768px) {
  .card-mobile {
    margin-bottom: 1rem;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .card-mobile:active {
    transform: scale(0.98);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .card-content-mobile {
    padding: 1.5rem;
  }

  .card-title-mobile {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  .card-description-mobile {
    font-size: 0.875rem;
    line-height: 1.5;
    opacity: 0.8;
  }
}

/* Performance Optimizations */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
