
@keyframes marquee {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

.animate-marquee-extremely-slow {
  animation: marquee 90s linear infinite;
}

.animate-marquee-very-slow {
  animation: marquee 60s linear infinite;
}

.animate-marquee-slow {
  animation: marquee 40s linear infinite;
}

.animate-marquee-moderate {
  animation: marquee 30s linear infinite;
}

.animate-marquee {
  animation: marquee 20s linear infinite;
}

.animate-marquee-fast {
  animation: marquee 13s linear infinite;
}

.animate-marquee-very-fast {
  animation: marquee 8s linear infinite;
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(255, 170, 0, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(255, 170, 0, 0.6);
  }
}

@keyframes shimmer {
  0% {
    text-shadow: 0 0 5px rgba(255, 193, 7, 0.3);
    color: #FFC107;
  }
  50% {
    text-shadow: 0 0 15px rgba(255, 193, 7, 0.8), 0 0 5px rgba(255, 224, 130, 0.5);
    color: #FFE082;
  }
  100% {
    text-shadow: 0 0 5px rgba(255, 193, 7, 0.3);
    color: #FFC107;
  }
}

.text-shimmer {
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes icon-glow {
  0%, 100% {
    filter: drop-shadow(0 0 3px rgba(255, 193, 7, 0.3));
  }
  50% {
    filter: drop-shadow(0 0 10px rgba(255, 193, 7, 0.7));
  }
}

.drop-shadow-glow {
  filter: drop-shadow(0 0 5px rgba(255, 193, 7, 0.5));
  animation: icon-glow 2s ease-in-out infinite;
}

.hover-glow:hover {
  animation: glow 1.5s infinite;
}

.temple-link {
  position: relative;
  display: inline-block;
}

.temple-link:after {
  content: '';
  position: absolute;
  width: 100%;
  height: 2px;
  bottom: -2px;
  left: 0;
  background: linear-gradient(90deg, #c13e43 0%, #f3c06b 100%);
  transform: scaleX(0);
  transform-origin: bottom right;
  transition: transform 0.3s;
}

.temple-link:hover:after {
  transform: scaleX(1);
  transform-origin: bottom left;
}

.hover-float {
  transition: transform 0.3s ease;
}

.hover-float:hover {
  transform: translateY(-5px);
}

.text-gradient {
  background: linear-gradient(90deg, #c13e43 0%, #f3c06b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.button-gradient {
  background: linear-gradient(90deg, #c13e43 0%, #f3c06b 100%);
  color: white;
  transition: all 0.3s ease;
}

.button-gradient:hover {
  background: linear-gradient(90deg, #a33338 0%, #e3b05b 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
