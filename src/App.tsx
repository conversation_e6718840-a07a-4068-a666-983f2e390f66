import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { ThemeProvider } from "./components/ThemeProvider";
import { AuthProvider } from "./contexts/AuthContext";
import { NotificationProvider } from "./contexts/NotificationContext";
import { ContentProvider } from "./contexts/ContentContext";
import { SupabaseProvider } from "./contexts/SupabaseContext";
import { HelmetProvider } from 'react-helmet-async';

// Pages
import Index from "./pages/Index";
import About from "./pages/About";
import Founders from "./pages/Founders";
import Services from "./pages/Services";
import Events from "./pages/Events";
import Gallery from "./pages/Gallery";
import Contact from "./pages/Contact";
import NotFound from "./pages/NotFound";

// Admin Pages
import AdminLogin from "./pages/admin/Login";
import AdminSetup from "./pages/admin/Setup";
import ProtectedAdminLayout from "./components/admin/ProtectedAdminLayout";
import AdminDashboard from "./pages/admin/Dashboard";
import ContentManager from "./pages/admin/ContentManager";
import EventsManager from "./pages/admin/EventsManager";
import GalleryManager from "./pages/admin/GalleryManager";
import UsersManager from "./pages/admin/UsersManager";
import AnnouncementsManager from "./pages/admin/AnnouncementsManager";
import MessagesManager from "./pages/admin/MessagesManager";
import SettingsManager from "./pages/admin/SettingsManager";
import AdminPanel from "./pages/admin/AdminPanel";
import { EventsManagement } from "./components/admin/EventsManagement";
import { AnalyticsDashboard } from "./components/admin/AnalyticsDashboard";
import { DonationManagement } from "./components/admin/DonationManagement";
import DesignSystem from "./pages/admin/DesignSystem";
import SeoManager from "./pages/admin/SeoManager";
import SecurityManager from "./pages/admin/SecurityManager";
import { PageBuilderManager } from "./components/admin/PageBuilder/PageBuilderManager";
import { ContentManagementDashboard } from "./components/admin/ContentManagementDashboard";
import { ContentSyncTestPanel } from "./components/admin/ContentSyncTestPanel";
import { VisualPageBuilder } from "./components/admin/VisualPageBuilder";
import { MinimalDashboard } from "./components/admin/MinimalDashboard";

const queryClient = new QueryClient();

const App = () => (
  <HelmetProvider>
    <QueryClientProvider client={queryClient}>
      <ThemeProvider defaultTheme="light">
        <SupabaseProvider>
          <AuthProvider>
            <NotificationProvider>
              <ContentProvider>
                <TooltipProvider>
              <Toaster />
              <Sonner />
              <BrowserRouter>
              <Routes>
                {/* Public Routes */}
                <Route path="/" element={<Index />} />
                <Route path="/about" element={<About />} />
                <Route path="/founders" element={<Founders />} />
                <Route path="/services" element={<Services />} />
                <Route path="/events" element={<Events />} />
                <Route path="/gallery" element={<Gallery />} />
                <Route path="/contact" element={<Contact />} />

                {/* Admin Routes */}
                <Route path="/admin/login" element={<AdminLogin />} />
                <Route path="/admin/setup" element={<AdminSetup />} />
                <Route path="/admin" element={<ProtectedAdminLayout />}>
                  <Route index element={<MinimalDashboard />} />
                  <Route path="overview" element={<AdminDashboard />} />
                  <Route path="events" element={<EventsManager />} />
                  <Route path="events-advanced" element={<EventsManagement />} />
                  <Route path="gallery" element={<GalleryManager />} />
                  <Route path="content" element={<ContentManager />} />
                  <Route path="users" element={<UsersManager />} />
                  <Route path="announcements" element={<AnnouncementsManager />} />
                  <Route path="messages" element={<MessagesManager />} />
                  <Route path="settings" element={<SettingsManager />} />
                  <Route path="dynamic" element={<AdminPanel />} />
                  <Route path="design" element={<DesignSystem />} />
                  <Route path="seo" element={<SeoManager />} />
                  <Route path="analytics" element={<AnalyticsDashboard />} />
                  <Route path="donations" element={<DonationManagement />} />
                  <Route path="page-builder" element={<PageBuilderManager />} />
                  <Route path="visual-builder" element={<VisualPageBuilder />} />
                  <Route path="content-management" element={<ContentManagementDashboard />} />
                  <Route path="content-sync-test" element={<ContentSyncTestPanel />} />
                  <Route path="security" element={<SecurityManager />} />
                </Route>

                {/* 404 */}
                <Route path="*" element={<NotFound />} />
              </Routes>
            </BrowserRouter>
              </TooltipProvider>
            </ContentProvider>
          </NotificationProvider>
        </AuthProvider>
        </SupabaseProvider>
      </ThemeProvider>
    </QueryClientProvider>
  </HelmetProvider>
);

export default App;
