import { WebsiteContent } from '@/types/content';
import { supabase } from '@/integrations/supabase/client';

// Default content structure for initialization
const DEFAULT_CONTENT: WebsiteContent = {
  siteInfo: {
    name: "<PERSON><PERSON><PERSON><PERSON>shetramu",
    tagline: "Divine Spiritual Center",
    description: "Devoted to spiritual growth, community service, and preserving Hindu traditions.",
    logo: "/images/logo.png"
  },
  hero: {
    slides: [
      {
        id: "1",
        title: "Welcome to Anantasagar Kshetramu",
        subtitle: "A Sacred Place of Worship and Spiritual Growth",
        imageUrl: "/images/hero/hero_1.jpg",
        order: 1,
        active: true
      },
      {
        id: "2",
        title: "Sri Saraswati Yagnam",
        subtitle: "500+ Yagnams Performed Worldwide",
        imageUrl: "/images/hero/hero_2.jpg",
        order: 2,
        active: true
      },
      {
        id: "3",
        title: "Spiritual Heritage",
        subtitle: "Preserving Ancient Traditions",
        imageUrl: "/images/hero/hero_3.jpeg",
        order: 3,
        active: true
      }
    ],
    autoplaySpeed: 5000,
    isAutoplay: true
  },
  announcements: [
    {
      id: "1",
      title: "Sri Saraswati Yagnam",
      content: "శ్రీ సరస్వతీ యజ్ఞం - మే 25, 2025 నుండి ప్రారంభం",
      type: "event",
      startDate: "2025-05-25",
      isActive: true,
      priority: "high"
    },
    {
      id: "2",
      title: "Sri Saraswati Yagnam",
      content: "Sri Saraswati Yagnam - Starting from May 25, 2025",
      type: "event",
      startDate: "2025-05-25",
      isActive: true,
      priority: "high"
    },
    {
      id: "3",
      title: "Temple Timings",
      content: "Daily temple timings: 5:30 AM to 9:00 PM",
      type: "info",
      startDate: "2024-01-01",
      isActive: true,
      priority: "medium"
    }
  ],
  navigation: {
    mainMenu: [
      { id: "home", title: "Home", path: "/", order: 1 },
      { id: "about", title: "About", path: "/about", order: 2 },
      { id: "services", title: "Services", path: "/services", order: 3 },
      { id: "events", title: "Events", path: "/events", order: 4 },
      { id: "gallery", title: "Gallery", path: "/gallery", order: 5 },
      { id: "contact", title: "Contact", path: "/contact", order: 6 }
    ],
    footerMenu: [
      { id: "privacy", title: "Privacy Policy", path: "/privacy", order: 1 },
      { id: "terms", title: "Terms of Service", path: "/terms", order: 2 },
      { id: "sitemap", title: "Sitemap", path: "/sitemap", order: 3 }
    ]
  },
  reviews: [],
  locations: [],
  contact: {
    phone: "+91 82477 21046",
    email: "<EMAIL>",
    address: {
      line1: "Anantasagar Rajiv Rahadari Highway",
      line2: "Siddipet, India"
    },
    coordinates: {
      lat: 18.2058612,
      lng: 78.9885657
    }
  },
  socialMedia: [],
  settings: {
    isMaintenanceMode: false,
    popups: {
      isEnabled: false,
      title: "",
      content: "",
      showDelay: 3000
    },
    lastUpdate: new Date().toISOString()
  },
  about: {
    hero: {
      showContent: false,
      title: "",
      subtitle: "",
      backgroundImage: "/images/AS_Overview.jpg"
    },
    sections: [
      {
        id: "history",
        title: "Our History",
        content: "Anantasagar Kshetramu stands as a beacon of spiritual enlightenment and cultural preservation. Founded with the vision of creating a sacred space for worship, learning, and community gathering, our temple has been serving devotees for years.",
        image: "/images/as_1.jpg",
        order: 1
      },
      {
        id: "temple",
        title: "The Temple",
        content: "Our temple complex is designed to provide a serene environment for prayer, meditation, and spiritual activities. The architecture reflects traditional Hindu temple design while incorporating modern amenities for the comfort of all visitors.",
        image: "/images/AS_Overview.jpg",
        order: 2
      }
    ]
  },
  gallery: [],
  footer: {
    text: "Devoted to spiritual growth, community service, and preserving Hindu traditions.",
    links: [
      { label: "About Us", url: "/about" },
      { label: "Services", url: "/services" },
      { label: "Events", url: "/events" },
      { label: "Contact", url: "/contact" },
      { label: "Privacy Policy", url: "/privacy" },
      { label: "Terms of Service", url: "/terms" }
    ],
    logo: "/images/logo.png"
  }
};

class LocalContentManager {
  private static instance: LocalContentManager;
  private content: WebsiteContent | null = null;
  private readonly STORAGE_KEY = 'website_content';
  private readonly BACKUPS_KEY = 'website_content_backups';
  private readonly SUPABASE_CONTENT_ID = 'main_content';
  private listeners: Array<(content: WebsiteContent) => void> = [];

  private constructor() {
    this.initializeContent();
  }

  public static getInstance(): LocalContentManager {
    if (!LocalContentManager.instance) {
      LocalContentManager.instance = new LocalContentManager();
    }
    return LocalContentManager.instance;
  }

  private async initializeContent() {
    try {
      // Try to load from Supabase first
      const supabaseContent = await this.loadFromSupabase();
      if (supabaseContent) {
        this.content = supabaseContent;
        this.saveToLocalStorage(supabaseContent);
        return;
      }

      // Fallback to localStorage
      const savedContent = localStorage.getItem(this.STORAGE_KEY);
      if (savedContent) {
        this.content = JSON.parse(savedContent);
        // Sync to Supabase
        await this.saveToSupabase(this.content);
        return;
      }

      // Use default content and save to both
      this.content = DEFAULT_CONTENT;
      await this.saveToSupabase(this.content);
      this.saveToLocalStorage(this.content);

    } catch (error) {
      console.error('Failed to initialize content:', error);
      // Use default content as fallback
      this.content = DEFAULT_CONTENT;
    }
  }

  // Supabase integration methods
  private async loadFromSupabase(): Promise<WebsiteContent | null> {
    try {
      const { data, error } = await supabase
        .from('website_content')
        .select('data')
        .eq('id', this.SUPABASE_CONTENT_ID)
        .single();

      if (error) {
        console.warn('No content found in Supabase:', error.message);
        return null;
      }

      return data?.data || null;
    } catch (error) {
      console.error('Failed to load from Supabase:', error);
      return null;
    }
  }

  private async saveToSupabase(content: WebsiteContent): Promise<void> {
    try {
      const { error } = await supabase
        .from('website_content')
        .upsert({
          id: this.SUPABASE_CONTENT_ID,
          data: content,
          updated_at: new Date().toISOString()
        });

      if (error) {
        console.error('Failed to save to Supabase:', error);
        throw error;
      }
    } catch (error) {
      console.error('Failed to save to Supabase:', error);
      // Don't throw here to allow fallback to localStorage
    }
  }

  private saveToLocalStorage(content: WebsiteContent): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(content));
    } catch (error) {
      console.error('Failed to save to localStorage:', error);
    }
  }

  // Event listener management
  public addListener(listener: (content: WebsiteContent) => void): void {
    this.listeners.push(listener);
  }

  public removeListener(listener: (content: WebsiteContent) => void): void {
    const index = this.listeners.indexOf(listener);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  private notifyListeners(content: WebsiteContent): void {
    this.listeners.forEach(listener => {
      try {
        listener(content);
      } catch (error) {
        console.error('Error in content listener:', error);
      }
    });
  }

  // Public API methods
  public getContent(): WebsiteContent | null {
    return this.content;
  }

  public async loadContent(): Promise<WebsiteContent> {
    if (!this.content) {
      await this.initializeContent();
      if (!this.content) {
        throw new Error('Content not initialized');
      }
    }
    return this.content;
  }

  public async saveContent(content: WebsiteContent): Promise<void> {
    try {
      // Save to both Supabase and localStorage
      await this.saveToSupabase(content);
      this.saveToLocalStorage(content);

      this.content = content;
      this.notifyListeners(content);
    } catch (error) {
      console.error('Failed to save content:', error);
      throw new Error('Failed to save content');
    }
  }

  public async createBackup(): Promise<string> {
    try {
      const timestamp = new Date().toISOString();
      const backups = this.getBackups();
      backups.unshift({ timestamp, content: this.content! });

      // Keep only the last 10 backups
      const trimmedBackups = backups.slice(0, 10);
      localStorage.setItem(this.BACKUPS_KEY, JSON.stringify(trimmedBackups));

      return timestamp;
    } catch (error) {
      console.error('Failed to create backup:', error);
      throw new Error('Failed to create backup');
    }
  }

  public async restoreBackup(timestamp: string): Promise<WebsiteContent> {
    try {
      const backups = this.getBackups();
      const backup = backups.find(b => b.timestamp === timestamp);
      
      if (!backup) {
        throw new Error('Backup not found');
      }

      await this.saveContent(backup.content);
      return backup.content;
    } catch (error) {
      console.error('Failed to restore backup:', error);
      throw new Error('Failed to restore backup');
    }
  }

  public getBackups(): Array<{ timestamp: string; content: WebsiteContent }> {
    try {
      const backupsString = localStorage.getItem(this.BACKUPS_KEY);
      return backupsString ? JSON.parse(backupsString) : [];
    } catch (error) {
      console.error('Failed to get backups:', error);
      return [];
    }
  }
}

export const contentManager = LocalContentManager.getInstance();
export default contentManager;
