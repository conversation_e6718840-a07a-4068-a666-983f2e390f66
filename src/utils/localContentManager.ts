import { WebsiteContent } from '@/types/content';

class LocalContentManager {
  private static instance: LocalContentManager;
  private content: WebsiteContent | null = null;
  private readonly STORAGE_KEY = 'website_content';
  private readonly BACKUPS_KEY = 'website_content_backups';

  private constructor() {
    this.initializeContent();
  }

  public static getInstance(): LocalContentManager {
    if (!LocalContentManager.instance) {
      LocalContentManager.instance = new LocalContentManager();
    }
    return LocalContentManager.instance;
  }

  private initializeContent() {
    try {
      const savedContent = localStorage.getItem(this.STORAGE_KEY);
      if (savedContent) {
        this.content = JSON.parse(savedContent);
      }
    } catch (error) {
      console.error('Failed to initialize content:', error);
      throw new Error('Failed to initialize content');
    }
  }

  public getContent(): WebsiteContent | null {
    return this.content;
  }

  public async loadContent(): Promise<WebsiteContent> {
    if (!this.content) {
      throw new Error('Content not initialized');
    }
    return this.content;
  }

  public async saveContent(content: WebsiteContent): Promise<void> {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(content));
      this.content = content;
    } catch (error) {
      console.error('Failed to save content:', error);
      throw new Error('Failed to save content');
    }
  }

  public async createBackup(): Promise<string> {
    try {
      const timestamp = new Date().toISOString();
      const backups = this.getBackups();
      backups.unshift({ timestamp, content: this.content! });

      // Keep only the last 10 backups
      const trimmedBackups = backups.slice(0, 10);
      localStorage.setItem(this.BACKUPS_KEY, JSON.stringify(trimmedBackups));

      return timestamp;
    } catch (error) {
      console.error('Failed to create backup:', error);
      throw new Error('Failed to create backup');
    }
  }

  public async restoreBackup(timestamp: string): Promise<WebsiteContent> {
    try {
      const backups = this.getBackups();
      const backup = backups.find(b => b.timestamp === timestamp);
      
      if (!backup) {
        throw new Error('Backup not found');
      }

      await this.saveContent(backup.content);
      return backup.content;
    } catch (error) {
      console.error('Failed to restore backup:', error);
      throw new Error('Failed to restore backup');
    }
  }

  public getBackups(): Array<{ timestamp: string; content: WebsiteContent }> {
    try {
      const backupsString = localStorage.getItem(this.BACKUPS_KEY);
      return backupsString ? JSON.parse(backupsString) : [];
    } catch (error) {
      console.error('Failed to get backups:', error);
      return [];
    }
  }
}

export const contentManager = LocalContentManager.getInstance();
export default contentManager;
