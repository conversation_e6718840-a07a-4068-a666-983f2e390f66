import authData from '@/data/auth.json';

export interface AuthData {
  admin: {
    username: string;
    password: string;
    lastLogin: string | null;
    lastPasswordChange: string;
  };
}

export const hashPassword = async (password: string): Promise<string> => {
  const encoder = new TextEncoder();
  const data = encoder.encode(password);
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  return hashHex;
};

export const verifyCredentials = async (username: string, password: string): Promise<boolean> => {
  const storedAuth = localStorage.getItem('temple_auth');
  if (!storedAuth) return false;

  const auth = JSON.parse(storedAuth) as AuthData;
  const hashedPassword = await hashPassword(password);
  
  return (
    username === auth.admin.username &&
    hashedPassword === auth.admin.password
  );
};

export const updateCredentials = async (
  newUsername: string,
  newPassword: string
): Promise<void> => {
  const storedAuth = localStorage.getItem('temple_auth');
  if (!storedAuth) throw new Error('No admin account exists');

  const auth = JSON.parse(storedAuth) as AuthData;
  auth.admin.username = newUsername;
  auth.admin.password = await hashPassword(newPassword);
  auth.admin.lastPasswordChange = new Date().toISOString();
  
  localStorage.setItem('temple_auth', JSON.stringify(auth));
};

export const createInitialAdmin = async (username: string, password: string): Promise<void> => {
  const hashedPassword = await hashPassword(password);
  
  const auth: AuthData = {
    admin: {
      username,
      password: hashedPassword,
      lastLogin: null,
      lastPasswordChange: new Date().toISOString()
    }
  };
  
  localStorage.setItem('temple_auth', JSON.stringify(auth));
};
