import { WebsiteContent } from '@/types/content';
import contentManager from './localContentManager';

// Static content from existing files that needs to be migrated
const STATIC_CONTENT = {
  services: [
    {
      id: "daily-prayers",
      title: "Daily Prayers",
      description: "Join us for daily prayers and spiritual activities",
      icon: "prayer",
      schedule: "5:30 AM - 9:00 PM",
      image: "/images/services/prayers.jpg"
    },
    {
      id: "special-events",
      title: "Special Events",
      description: "Participate in our special religious ceremonies and festivals",
      icon: "event",
      schedule: "As per calendar",
      image: "/images/services/events.jpg"
    },
    {
      id: "yagnam",
      title: "Sri Saraswati Yagnam",
      description: "500+ Yagnams performed worldwide - still ongoing",
      icon: "fire",
      schedule: "Ongoing",
      image: "/images/yagnam.jpg"
    },
    {
      id: "community-service",
      title: "Community Service",
      description: "Engage in community service and social welfare activities",
      icon: "community",
      schedule: "Weekly",
      image: "/images/services/community.jpg"
    }
  ],
  
  events: [
    {
      id: "saraswati-yagnam-2025",
      title: "Sri Saraswati Yagnam",
      description: "Join us for the sacred Sri Saraswati Yagnam ceremony",
      date: "2025-05-25",
      endDate: "2025-05-27",
      time: "6:00 AM",
      location: "Main Temple Hall",
      image: "/images/yagnam.jpg",
      type: "upcoming",
      featured: true,
      registrationRequired: true,
      maxParticipants: 500
    }
  ],

  gallery: [
    {
      id: "temple-overview",
      title: "Temple Overview",
      description: "Beautiful view of our temple complex",
      image: "/images/AS_Overview.jpg",
      category: "temple",
      type: "image",
      order: 1
    },
    {
      id: "temple-interior",
      title: "Temple Interior",
      description: "Sacred interior of the main temple",
      image: "/images/as_1.jpg",
      category: "temple",
      type: "image",
      order: 2
    },
    {
      id: "yagnam-ceremony",
      title: "Yagnam Ceremony",
      description: "Sri Saraswati Yagnam in progress",
      image: "/images/yagnam.jpg",
      category: "events",
      type: "image",
      order: 3
    }
  ],

  aboutSections: [
    {
      id: "history",
      title: "Our History",
      content: "Anantasagar Kshetramu stands as a beacon of spiritual enlightenment and cultural preservation. Founded with the vision of creating a sacred space for worship, learning, and community gathering, our temple has been serving devotees for years. What began as a small prayer hall has grown into a magnificent temple complex that attracts devotees from across the region.",
      image: "/images/as_1.jpg",
      order: 1
    },
    {
      id: "temple",
      title: "The Temple",
      content: "Our temple complex is designed to provide a serene environment for prayer, meditation, and spiritual activities. The architecture reflects traditional Hindu temple design while incorporating modern amenities for the comfort of all visitors. The main sanctum houses the divine deity, surrounded by beautifully carved pillars and intricate artwork.",
      image: "/images/AS_Overview.jpg",
      order: 2
    },
    {
      id: "yagnam",
      title: "Sri Saraswati Yagnam",
      content: "The Sri Saraswati Yagnam is our most significant spiritual offering, with over 500 ceremonies performed worldwide. This sacred fire ritual is dedicated to Goddess Saraswati, the deity of knowledge, music, arts, and wisdom. The yagnam continues to be performed regularly, bringing blessings of knowledge and prosperity to all participants.",
      image: "/images/yagnam.jpg",
      order: 3
    },
    {
      id: "location",
      title: "Our Location",
      content: "Strategically located on the Anantasagar Rajiv Rahadari Highway in Siddipet, our temple is easily accessible to devotees from surrounding areas. The peaceful location provides an ideal setting for spiritual contemplation and community gatherings.",
      image: "/images/AS_Overview.jpg",
      order: 4
    }
  ],

  quotes: [
    {
      id: "saraswati-stotram",
      text: "సరస్వతి నమస్తుభ్యం వరదే కామరూపిణి | విద్యారంభం కరిష్యామి సిద్ధిర్భవతు మే సదా ||",
      translation: "Salutations to Goddess Saraswati, the giver of boons and fulfiller of wishes. I begin my studies; may I always be blessed with success.",
      author: "Saraswati Stotram",
      language: "te"
    },
    {
      id: "daily-quote",
      text: "Knowledge is the greatest wealth that cannot be stolen or diminished when shared.",
      translation: "",
      author: "Ancient Wisdom",
      language: "en"
    }
  ]
};

export class ContentMigrationService {
  private static instance: ContentMigrationService;

  public static getInstance(): ContentMigrationService {
    if (!ContentMigrationService.instance) {
      ContentMigrationService.instance = new ContentMigrationService();
    }
    return ContentMigrationService.instance;
  }

  /**
   * Migrate all static content to the dynamic content system
   */
  public async migrateAllContent(): Promise<void> {
    try {
      console.log('Starting content migration...');
      
      // Load current content
      const currentContent = await contentManager.loadContent();
      
      // Create enhanced content with migrated data
      const enhancedContent: WebsiteContent = {
        ...currentContent,
        
        // Enhance existing sections
        about: {
          ...currentContent.about,
          sections: [
            ...currentContent.about.sections,
            ...STATIC_CONTENT.aboutSections.filter(section => 
              !currentContent.about.sections.some(existing => existing.id === section.id)
            )
          ]
        },

        // Add gallery items if not already present
        gallery: [
          ...currentContent.gallery,
          ...STATIC_CONTENT.gallery.filter(item => 
            !currentContent.gallery.some(existing => existing.id === item.id)
          )
        ],

        // Add services data (extend the content type to include services)
        services: STATIC_CONTENT.services,
        
        // Add events data (extend the content type to include events)
        events: STATIC_CONTENT.events,
        
        // Add quotes data (extend the content type to include quotes)
        quotes: STATIC_CONTENT.quotes,

        // Update settings
        settings: {
          ...currentContent.settings,
          lastUpdate: new Date().toISOString(),
          migrationCompleted: true,
          migrationDate: new Date().toISOString()
        }
      };

      // Save the enhanced content
      await contentManager.saveContent(enhancedContent);
      
      console.log('Content migration completed successfully');
      
    } catch (error) {
      console.error('Content migration failed:', error);
      throw new Error('Failed to migrate content');
    }
  }

  /**
   * Check if content migration is needed
   */
  public async isMigrationNeeded(): Promise<boolean> {
    try {
      const content = await contentManager.loadContent();
      return !content.settings?.migrationCompleted;
    } catch (error) {
      console.error('Failed to check migration status:', error);
      return true;
    }
  }

  /**
   * Migrate specific content section
   */
  public async migrateSection(sectionName: keyof typeof STATIC_CONTENT): Promise<void> {
    try {
      const currentContent = await contentManager.loadContent();
      const sectionData = STATIC_CONTENT[sectionName];
      
      const updatedContent = {
        ...currentContent,
        [sectionName]: sectionData,
        settings: {
          ...currentContent.settings,
          lastUpdate: new Date().toISOString()
        }
      };

      await contentManager.saveContent(updatedContent);
      console.log(`Successfully migrated ${sectionName} section`);
      
    } catch (error) {
      console.error(`Failed to migrate ${sectionName} section:`, error);
      throw error;
    }
  }

  /**
   * Reset content to default state
   */
  public async resetToDefaults(): Promise<void> {
    try {
      // This will reinitialize with default content
      localStorage.removeItem('website_content');
      await contentManager.loadContent();
      console.log('Content reset to defaults');
    } catch (error) {
      console.error('Failed to reset content:', error);
      throw error;
    }
  }

  /**
   * Export current content for backup
   */
  public async exportContent(): Promise<string> {
    try {
      const content = await contentManager.loadContent();
      return JSON.stringify(content, null, 2);
    } catch (error) {
      console.error('Failed to export content:', error);
      throw error;
    }
  }

  /**
   * Import content from backup
   */
  public async importContent(contentJson: string): Promise<void> {
    try {
      const content = JSON.parse(contentJson) as WebsiteContent;
      await contentManager.saveContent(content);
      console.log('Content imported successfully');
    } catch (error) {
      console.error('Failed to import content:', error);
      throw error;
    }
  }
}

export const contentMigration = ContentMigrationService.getInstance();
export default contentMigration;
