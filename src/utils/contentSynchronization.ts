import { WebsiteContent } from '@/types/content';
import contentManager from './localContentManager';
import { supabase } from '@/integrations/supabase/client';

export interface ContentChangeEvent {
  type: 'update' | 'create' | 'delete';
  section: string;
  data: any;
  timestamp: string;
  source: 'admin' | 'frontend' | 'api';
}

export class ContentSynchronizationService {
  private static instance: ContentSynchronizationService;
  private listeners: Map<string, Array<(event: ContentChangeEvent) => void>> = new Map();
  private isInitialized = false;
  private syncInterval: NodeJS.Timeout | null = null;
  private lastSyncTimestamp: string | null = null;

  public static getInstance(): ContentSynchronizationService {
    if (!ContentSynchronizationService.instance) {
      ContentSynchronizationService.instance = new ContentSynchronizationService();
    }
    return ContentSynchronizationService.instance;
  }

  /**
   * Initialize the synchronization service
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Set up real-time subscription for content changes
      this.setupRealtimeSubscription();
      
      // Start periodic sync
      this.startPeriodicSync();
      
      // Listen to content manager changes
      contentManager.addListener(this.handleContentChange.bind(this));
      
      this.isInitialized = true;
      console.log('Content synchronization service initialized');
      
    } catch (error) {
      console.error('Failed to initialize content synchronization:', error);
      throw error;
    }
  }

  /**
   * Set up real-time subscription for content changes from Supabase
   */
  private setupRealtimeSubscription(): void {
    supabase
      .channel('website_content_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'website_content'
        },
        (payload) => {
          this.handleRealtimeChange(payload);
        }
      )
      .subscribe();
  }

  /**
   * Handle real-time changes from Supabase
   */
  private async handleRealtimeChange(payload: any): Promise<void> {
    try {
      if (payload.eventType === 'UPDATE' && payload.new) {
        const newContent = payload.new.data as WebsiteContent;
        
        // Update local content if it's different
        const currentContent = contentManager.getContent();
        if (!currentContent || this.hasContentChanged(currentContent, newContent)) {
          await contentManager.saveContent(newContent);
          
          // Notify all listeners
          this.notifyListeners('content', {
            type: 'update',
            section: 'all',
            data: newContent,
            timestamp: new Date().toISOString(),
            source: 'api'
          });
        }
      }
    } catch (error) {
      console.error('Error handling realtime change:', error);
    }
  }

  /**
   * Check if content has changed
   */
  private hasContentChanged(current: WebsiteContent, updated: WebsiteContent): boolean {
    return JSON.stringify(current) !== JSON.stringify(updated);
  }

  /**
   * Start periodic synchronization
   */
  private startPeriodicSync(): void {
    // Sync every 30 seconds
    this.syncInterval = setInterval(() => {
      this.performSync();
    }, 30000);
  }

  /**
   * Perform synchronization check
   */
  private async performSync(): Promise<void> {
    try {
      // Get latest content from Supabase
      const { data, error } = await supabase
        .from('website_content')
        .select('data, updated_at')
        .eq('id', 'main_content')
        .single();

      if (error) {
        console.warn('Sync check failed:', error.message);
        return;
      }

      if (data && data.updated_at !== this.lastSyncTimestamp) {
        const remoteContent = data.data as WebsiteContent;
        const localContent = contentManager.getContent();

        if (!localContent || this.hasContentChanged(localContent, remoteContent)) {
          await contentManager.saveContent(remoteContent);
          this.lastSyncTimestamp = data.updated_at;
          
          // Notify listeners of sync update
          this.notifyListeners('sync', {
            type: 'update',
            section: 'all',
            data: remoteContent,
            timestamp: new Date().toISOString(),
            source: 'api'
          });
        }
      }
    } catch (error) {
      console.error('Periodic sync failed:', error);
    }
  }

  /**
   * Handle content changes from local content manager
   */
  private handleContentChange(content: WebsiteContent): void {
    // Notify all content listeners
    this.notifyListeners('content', {
      type: 'update',
      section: 'all',
      data: content,
      timestamp: new Date().toISOString(),
      source: 'frontend'
    });
  }

  /**
   * Subscribe to content changes
   */
  public subscribe(
    eventType: string,
    callback: (event: ContentChangeEvent) => void
  ): () => void {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, []);
    }
    
    this.listeners.get(eventType)!.push(callback);
    
    // Return unsubscribe function
    return () => {
      const callbacks = this.listeners.get(eventType);
      if (callbacks) {
        const index = callbacks.indexOf(callback);
        if (index > -1) {
          callbacks.splice(index, 1);
        }
      }
    };
  }

  /**
   * Notify all listeners of a specific event type
   */
  private notifyListeners(eventType: string, event: ContentChangeEvent): void {
    const callbacks = this.listeners.get(eventType);
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(event);
        } catch (error) {
          console.error('Error in content sync listener:', error);
        }
      });
    }
  }

  /**
   * Force synchronization
   */
  public async forceSync(): Promise<void> {
    await this.performSync();
  }

  /**
   * Update specific content section
   */
  public async updateSection(
    sectionName: string,
    sectionData: any,
    source: 'admin' | 'frontend' = 'admin'
  ): Promise<void> {
    try {
      const currentContent = await contentManager.loadContent();
      const updatedContent = {
        ...currentContent,
        [sectionName]: sectionData,
        settings: {
          ...currentContent.settings,
          lastUpdate: new Date().toISOString()
        }
      };

      await contentManager.saveContent(updatedContent);

      // Notify listeners of section update
      this.notifyListeners('section', {
        type: 'update',
        section: sectionName,
        data: sectionData,
        timestamp: new Date().toISOString(),
        source
      });

    } catch (error) {
      console.error(`Failed to update section ${sectionName}:`, error);
      throw error;
    }
  }

  /**
   * Get current content with real-time updates
   */
  public async getContent(): Promise<WebsiteContent> {
    // Ensure we have the latest content
    await this.performSync();
    return await contentManager.loadContent();
  }

  /**
   * Batch update multiple sections
   */
  public async batchUpdate(
    updates: Record<string, any>,
    source: 'admin' | 'frontend' = 'admin'
  ): Promise<void> {
    try {
      const currentContent = await contentManager.loadContent();
      const updatedContent = {
        ...currentContent,
        ...updates,
        settings: {
          ...currentContent.settings,
          lastUpdate: new Date().toISOString()
        }
      };

      await contentManager.saveContent(updatedContent);

      // Notify listeners of batch update
      Object.keys(updates).forEach(sectionName => {
        this.notifyListeners('section', {
          type: 'update',
          section: sectionName,
          data: updates[sectionName],
          timestamp: new Date().toISOString(),
          source
        });
      });

    } catch (error) {
      console.error('Failed to perform batch update:', error);
      throw error;
    }
  }

  /**
   * Cleanup and stop synchronization
   */
  public cleanup(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
    
    this.listeners.clear();
    this.isInitialized = false;
    
    console.log('Content synchronization service cleaned up');
  }

  /**
   * Get synchronization status
   */
  public getStatus(): {
    isInitialized: boolean;
    lastSync: string | null;
    activeListeners: number;
  } {
    const totalListeners = Array.from(this.listeners.values())
      .reduce((total, callbacks) => total + callbacks.length, 0);

    return {
      isInitialized: this.isInitialized,
      lastSync: this.lastSyncTimestamp,
      activeListeners: totalListeners
    };
  }
}

export const contentSync = ContentSynchronizationService.getInstance();
export default contentSync;
