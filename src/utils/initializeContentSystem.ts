import contentManager from './localContentManager';
import contentSync from './contentSynchronization';
import contentMigration from './contentMigration';

/**
 * Initialize the complete content management system
 */
export async function initializeContentSystem(): Promise<{
  success: boolean;
  message: string;
  details?: any;
}> {
  try {
    console.log('🚀 Initializing Content Management System...');

    // Step 1: Initialize content manager
    console.log('📦 Loading content manager...');
    const content = await contentManager.loadContent();
    
    if (!content) {
      throw new Error('Failed to load initial content');
    }

    // Step 2: Check and perform migration if needed
    console.log('🔄 Checking migration status...');
    const migrationNeeded = await contentMigration.isMigrationNeeded();
    
    if (migrationNeeded) {
      console.log('📋 Performing content migration...');
      await contentMigration.migrateAllContent();
      console.log('✅ Content migration completed');
    } else {
      console.log('✅ Content already migrated');
    }

    // Step 3: Initialize synchronization service
    console.log('🔗 Initializing synchronization service...');
    await contentSync.initialize();
    
    const syncStatus = contentSync.getStatus();
    if (!syncStatus.isInitialized) {
      throw new Error('Failed to initialize synchronization service');
    }

    // Step 4: Verify system integrity
    console.log('🔍 Verifying system integrity...');
    const verificationResult = await verifySystemIntegrity();
    
    if (!verificationResult.success) {
      console.warn('⚠️ System integrity check failed:', verificationResult.issues);
    }

    console.log('🎉 Content Management System initialized successfully!');
    
    return {
      success: true,
      message: 'Content Management System initialized successfully',
      details: {
        contentLoaded: !!content,
        migrationCompleted: !migrationNeeded,
        syncInitialized: syncStatus.isInitialized,
        integrityCheck: verificationResult
      }
    };

  } catch (error) {
    console.error('❌ Failed to initialize Content Management System:', error);
    
    return {
      success: false,
      message: `Failed to initialize Content Management System: ${(error as Error).message}`,
      details: { error: error }
    };
  }
}

/**
 * Verify the integrity of the content management system
 */
async function verifySystemIntegrity(): Promise<{
  success: boolean;
  issues: string[];
}> {
  const issues: string[] = [];

  try {
    // Check content structure
    const content = await contentManager.loadContent();
    
    if (!content.siteInfo) {
      issues.push('Missing siteInfo section');
    }
    
    if (!content.hero || !content.hero.slides || content.hero.slides.length === 0) {
      issues.push('Missing or empty hero slides');
    }
    
    if (!content.navigation || !content.navigation.mainMenu) {
      issues.push('Missing navigation menu');
    }
    
    if (!content.contact) {
      issues.push('Missing contact information');
    }

    // Check synchronization status
    const syncStatus = contentSync.getStatus();
    if (!syncStatus.isInitialized) {
      issues.push('Synchronization service not initialized');
    }

    // Check if content is properly structured for dynamic updates
    if (!content.settings) {
      issues.push('Missing settings section');
    }

    return {
      success: issues.length === 0,
      issues
    };

  } catch (error) {
    issues.push(`Integrity check failed: ${(error as Error).message}`);
    return {
      success: false,
      issues
    };
  }
}

/**
 * Reset the entire content system (use with caution)
 */
export async function resetContentSystem(): Promise<{
  success: boolean;
  message: string;
}> {
  try {
    console.log('🔄 Resetting Content Management System...');

    // Clear local storage
    localStorage.removeItem('website_content');
    localStorage.removeItem('website_content_backups');

    // Cleanup synchronization service
    contentSync.cleanup();

    // Reinitialize the system
    const initResult = await initializeContentSystem();

    if (initResult.success) {
      console.log('✅ Content Management System reset and reinitialized successfully');
      return {
        success: true,
        message: 'Content Management System reset successfully'
      };
    } else {
      throw new Error(initResult.message);
    }

  } catch (error) {
    console.error('❌ Failed to reset Content Management System:', error);
    return {
      success: false,
      message: `Failed to reset Content Management System: ${(error as Error).message}`
    };
  }
}

/**
 * Get current system status
 */
export function getSystemStatus(): {
  contentManager: {
    hasContent: boolean;
    lastUpdate?: string;
  };
  synchronization: {
    isInitialized: boolean;
    lastSync: string | null;
    activeListeners: number;
  };
  migration: {
    completed: boolean;
    date?: string;
  };
} {
  const content = contentManager.getContent();
  const syncStatus = contentSync.getStatus();

  return {
    contentManager: {
      hasContent: !!content,
      lastUpdate: content?.settings?.lastUpdate
    },
    synchronization: {
      isInitialized: syncStatus.isInitialized,
      lastSync: syncStatus.lastSync,
      activeListeners: syncStatus.activeListeners
    },
    migration: {
      completed: !!content?.settings?.migrationCompleted,
      date: content?.settings?.migrationDate
    }
  };
}

/**
 * Perform a health check on the content system
 */
export async function performHealthCheck(): Promise<{
  overall: 'healthy' | 'warning' | 'critical';
  checks: Array<{
    name: string;
    status: 'pass' | 'warn' | 'fail';
    message: string;
  }>;
}> {
  const checks: Array<{
    name: string;
    status: 'pass' | 'warn' | 'fail';
    message: string;
  }> = [];

  // Check 1: Content availability
  try {
    const content = await contentManager.loadContent();
    if (content) {
      checks.push({
        name: 'Content Availability',
        status: 'pass',
        message: 'Content loaded successfully'
      });
    } else {
      checks.push({
        name: 'Content Availability',
        status: 'fail',
        message: 'No content available'
      });
    }
  } catch (error) {
    checks.push({
      name: 'Content Availability',
      status: 'fail',
      message: `Content loading failed: ${(error as Error).message}`
    });
  }

  // Check 2: Synchronization service
  const syncStatus = contentSync.getStatus();
  if (syncStatus.isInitialized) {
    checks.push({
      name: 'Synchronization Service',
      status: 'pass',
      message: `Active with ${syncStatus.activeListeners} listeners`
    });
  } else {
    checks.push({
      name: 'Synchronization Service',
      status: 'fail',
      message: 'Synchronization service not initialized'
    });
  }

  // Check 3: Migration status
  const content = contentManager.getContent();
  if (content?.settings?.migrationCompleted) {
    checks.push({
      name: 'Content Migration',
      status: 'pass',
      message: 'Migration completed successfully'
    });
  } else {
    checks.push({
      name: 'Content Migration',
      status: 'warn',
      message: 'Migration may be needed'
    });
  }

  // Check 4: Data integrity
  const integrityResult = await verifySystemIntegrity();
  if (integrityResult.success) {
    checks.push({
      name: 'Data Integrity',
      status: 'pass',
      message: 'All data structures are valid'
    });
  } else {
    checks.push({
      name: 'Data Integrity',
      status: 'warn',
      message: `Issues found: ${integrityResult.issues.join(', ')}`
    });
  }

  // Determine overall status
  const failCount = checks.filter(c => c.status === 'fail').length;
  const warnCount = checks.filter(c => c.status === 'warn').length;

  let overall: 'healthy' | 'warning' | 'critical';
  if (failCount > 0) {
    overall = 'critical';
  } else if (warnCount > 0) {
    overall = 'warning';
  } else {
    overall = 'healthy';
  }

  return { overall, checks };
}

// Auto-initialize when this module is imported
let initializationPromise: Promise<any> | null = null;

export function getInitializationPromise(): Promise<any> {
  if (!initializationPromise) {
    initializationPromise = initializeContentSystem();
  }
  return initializationPromise;
}

// Export for manual initialization if needed
export { initializeContentSystem as manualInitialize };
