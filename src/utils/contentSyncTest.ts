import contentManager from './localContentManager';
import contentSync from './contentSynchronization';
import contentMigration from './contentMigration';

/**
 * Test suite for content synchronization functionality
 */
export class ContentSyncTestSuite {
  private static instance: ContentSyncTestSuite;

  public static getInstance(): ContentSyncTestSuite {
    if (!ContentSyncTestSuite.instance) {
      ContentSyncTestSuite.instance = new ContentSyncTestSuite();
    }
    return ContentSyncTestSuite.instance;
  }

  /**
   * Run all content synchronization tests
   */
  public async runAllTests(): Promise<{
    passed: number;
    failed: number;
    results: Array<{ test: string; passed: boolean; error?: string }>;
  }> {
    const results: Array<{ test: string; passed: boolean; error?: string }> = [];
    
    console.log('🧪 Starting Content Synchronization Test Suite...');

    // Test 1: Content Manager Initialization
    try {
      await this.testContentManagerInitialization();
      results.push({ test: 'Content Manager Initialization', passed: true });
      console.log('✅ Content Manager Initialization - PASSED');
    } catch (error) {
      results.push({ 
        test: 'Content Manager Initialization', 
        passed: false, 
        error: (error as Error).message 
      });
      console.log('❌ Content Manager Initialization - FAILED:', error);
    }

    // Test 2: Content Synchronization Service
    try {
      await this.testContentSynchronization();
      results.push({ test: 'Content Synchronization Service', passed: true });
      console.log('✅ Content Synchronization Service - PASSED');
    } catch (error) {
      results.push({ 
        test: 'Content Synchronization Service', 
        passed: false, 
        error: (error as Error).message 
      });
      console.log('❌ Content Synchronization Service - FAILED:', error);
    }

    // Test 3: Content Migration
    try {
      await this.testContentMigration();
      results.push({ test: 'Content Migration', passed: true });
      console.log('✅ Content Migration - PASSED');
    } catch (error) {
      results.push({ 
        test: 'Content Migration', 
        passed: false, 
        error: (error as Error).message 
      });
      console.log('❌ Content Migration - FAILED:', error);
    }

    // Test 4: Real-time Updates
    try {
      await this.testRealTimeUpdates();
      results.push({ test: 'Real-time Updates', passed: true });
      console.log('✅ Real-time Updates - PASSED');
    } catch (error) {
      results.push({ 
        test: 'Real-time Updates', 
        passed: false, 
        error: (error as Error).message 
      });
      console.log('❌ Real-time Updates - FAILED:', error);
    }

    // Test 5: Content Persistence
    try {
      await this.testContentPersistence();
      results.push({ test: 'Content Persistence', passed: true });
      console.log('✅ Content Persistence - PASSED');
    } catch (error) {
      results.push({ 
        test: 'Content Persistence', 
        passed: false, 
        error: (error as Error).message 
      });
      console.log('❌ Content Persistence - FAILED:', error);
    }

    const passed = results.filter(r => r.passed).length;
    const failed = results.filter(r => !r.passed).length;

    console.log(`\n📊 Test Results: ${passed} passed, ${failed} failed`);
    
    return { passed, failed, results };
  }

  /**
   * Test content manager initialization
   */
  private async testContentManagerInitialization(): Promise<void> {
    const content = await contentManager.loadContent();
    
    if (!content) {
      throw new Error('Content manager failed to load content');
    }

    if (!content.siteInfo) {
      throw new Error('Site info not found in content');
    }

    if (!content.hero || !content.hero.slides) {
      throw new Error('Hero section not properly initialized');
    }

    if (!content.navigation || !content.navigation.mainMenu) {
      throw new Error('Navigation not properly initialized');
    }
  }

  /**
   * Test content synchronization service
   */
  private async testContentSynchronization(): Promise<void> {
    await contentSync.initialize();
    
    const status = contentSync.getStatus();
    
    if (!status.isInitialized) {
      throw new Error('Content synchronization service not initialized');
    }

    // Test section update
    const testData = { name: 'Test Site Name', tagline: 'Test Tagline' };
    await contentSync.updateSection('siteInfo', testData);
    
    const updatedContent = await contentManager.loadContent();
    
    if (updatedContent.siteInfo.name !== testData.name) {
      throw new Error('Section update not reflected in content');
    }
  }

  /**
   * Test content migration
   */
  private async testContentMigration(): Promise<void> {
    const migrationNeeded = await contentMigration.isMigrationNeeded();
    
    if (migrationNeeded) {
      await contentMigration.migrateAllContent();
    }

    const content = await contentManager.loadContent();
    
    if (!content.settings?.migrationCompleted) {
      throw new Error('Content migration not completed properly');
    }

    // Test export/import functionality
    const exportedContent = await contentMigration.exportContent();
    
    if (!exportedContent || typeof exportedContent !== 'string') {
      throw new Error('Content export failed');
    }

    const parsedContent = JSON.parse(exportedContent);
    
    if (!parsedContent.siteInfo || !parsedContent.hero) {
      throw new Error('Exported content structure invalid');
    }
  }

  /**
   * Test real-time updates
   */
  private async testRealTimeUpdates(): Promise<void> {
    let updateReceived = false;
    
    // Subscribe to content changes
    const unsubscribe = contentSync.subscribe('content', (event) => {
      if (event.type === 'update') {
        updateReceived = true;
      }
    });

    // Trigger an update
    await contentSync.updateSection('siteInfo', { 
      name: 'Real-time Test', 
      tagline: 'Testing real-time updates' 
    });

    // Wait a bit for the update to propagate
    await new Promise(resolve => setTimeout(resolve, 100));

    unsubscribe();

    if (!updateReceived) {
      throw new Error('Real-time update not received');
    }
  }

  /**
   * Test content persistence
   */
  private async testContentPersistence(): Promise<void> {
    const originalContent = await contentManager.loadContent();
    
    // Make a change
    const testSiteInfo = {
      ...originalContent.siteInfo,
      name: 'Persistence Test',
      tagline: 'Testing persistence'
    };

    await contentManager.saveContent({
      ...originalContent,
      siteInfo: testSiteInfo
    });

    // Reload content
    const reloadedContent = await contentManager.loadContent();

    if (reloadedContent.siteInfo.name !== testSiteInfo.name) {
      throw new Error('Content changes not persisted');
    }

    // Test backup functionality
    const backupTimestamp = await contentManager.createBackup();
    
    if (!backupTimestamp) {
      throw new Error('Backup creation failed');
    }

    const backups = contentManager.getBackups();
    
    if (backups.length === 0) {
      throw new Error('Backup not found in backup list');
    }

    // Test restore functionality
    await contentManager.restoreBackup(backupTimestamp);
    
    const restoredContent = await contentManager.loadContent();
    
    if (!restoredContent) {
      throw new Error('Content restore failed');
    }
  }

  /**
   * Test frontend-backend synchronization
   */
  public async testFrontendBackendSync(): Promise<boolean> {
    try {
      console.log('🔄 Testing frontend-backend synchronization...');

      // Test 1: Update content through admin interface simulation
      const adminUpdate = {
        siteInfo: {
          name: 'Admin Updated Name',
          tagline: 'Updated via Admin',
          description: 'This was updated through the admin interface'
        }
      };

      await contentSync.batchUpdate(adminUpdate, 'admin');

      // Test 2: Verify frontend receives the update
      const frontendContent = await contentSync.getContent();
      
      if (frontendContent.siteInfo.name !== adminUpdate.siteInfo.name) {
        throw new Error('Frontend did not receive admin update');
      }

      // Test 3: Update content through frontend simulation
      const frontendUpdate = {
        hero: {
          autoplaySpeed: 3000,
          isAutoplay: false
        }
      };

      await contentSync.batchUpdate(frontendUpdate, 'frontend');

      // Test 4: Verify admin interface would receive the update
      const adminContent = await contentSync.getContent();
      
      if (adminContent.hero.autoplaySpeed !== frontendUpdate.hero.autoplaySpeed) {
        throw new Error('Admin interface did not receive frontend update');
      }

      console.log('✅ Frontend-backend synchronization test passed');
      return true;

    } catch (error) {
      console.log('❌ Frontend-backend synchronization test failed:', error);
      return false;
    }
  }

  /**
   * Performance test for content operations
   */
  public async performanceTest(): Promise<{
    loadTime: number;
    saveTime: number;
    syncTime: number;
  }> {
    console.log('⚡ Running performance tests...');

    // Test load performance
    const loadStart = performance.now();
    await contentManager.loadContent();
    const loadTime = performance.now() - loadStart;

    // Test save performance
    const content = await contentManager.loadContent();
    const saveStart = performance.now();
    await contentManager.saveContent(content);
    const saveTime = performance.now() - saveStart;

    // Test sync performance
    const syncStart = performance.now();
    await contentSync.forceSync();
    const syncTime = performance.now() - syncStart;

    console.log(`📈 Performance Results:
      - Load Time: ${loadTime.toFixed(2)}ms
      - Save Time: ${saveTime.toFixed(2)}ms
      - Sync Time: ${syncTime.toFixed(2)}ms`);

    return { loadTime, saveTime, syncTime };
  }
}

export const contentSyncTest = ContentSyncTestSuite.getInstance();
export default contentSyncTest;
