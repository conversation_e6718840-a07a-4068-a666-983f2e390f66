
import { supabase } from '@/integrations/supabase/client';

// Function to clean up authentication state to prevent issues
export const cleanupAuthState = () => {
  // Remove standard auth tokens
  localStorage.removeItem('supabase.auth.token');
  
  // Remove all Supabase auth keys from localStorage
  Object.keys(localStorage).forEach((key) => {
    if (key.startsWith('supabase.auth.') || key.includes('sb-')) {
      localStorage.removeItem(key);
    }
  });
  
  // Remove from sessionStorage if in use
  Object.keys(sessionStorage || {}).forEach((key) => {
    if (key.startsWith('supabase.auth.') || key.includes('sb-')) {
      sessionStorage.removeItem(key);
    }
  });
};

export const signIn = async (email: string, password: string) => {
  try {
    // Clean up existing state
    cleanupAuthState();
    
    // Attempt global sign out first
    try {
      await supabase.auth.signOut({ scope: 'global' });
    } catch (err) {
      // Continue even if this fails
    }
    
    // Sign in with email/password
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    
    if (error) throw error;
    
    return { data, error: null };
  } catch (error: any) {
    return { data: null, error };
  }
};

export const signOut = async () => {
  try {
    // Clean up auth state
    cleanupAuthState();
    
    // Attempt global sign out
    try {
      await supabase.auth.signOut({ scope: 'global' });
    } catch (err) {
      // Continue even if this fails
    }
    
    // Redirect to login page with a full page refresh
    window.location.href = '/admin/login';
    
    return { error: null };
  } catch (error: any) {
    return { error };
  }
};

export const requireAuth = (navigateCallback: () => void) => {
  supabase.auth.getSession().then(({ data: { session } }) => {
    if (!session) {
      navigateCallback();
    }
  });
};

// Check if user has admin role
export const isAdmin = (user: any) => {
  if (!user) return false;
  
  // Check from user metadata first
  if (user.user_metadata && user.user_metadata.role === 'admin') {
    return true;
  }
  
  // Fallback: check from custom claims if available
  if (user.app_metadata && user.app_metadata.role === 'admin') {
    return true;
  }
  
  return false;
};
