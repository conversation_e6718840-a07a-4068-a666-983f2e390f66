import { DEFAULT_ADMIN } from '@/config/auth';

class AuthManager {
  private static instance: AuthManager;
  private readonly STORAGE_KEY = 'admin_auth';
  private readonly SESSION_KEY = 'admin_session';

  private constructor() {}

  public static getInstance(): AuthManager {
    if (!AuthManager.instance) {
      AuthManager.instance = new AuthManager();
    }
    return AuthManager.instance;
  }

  private async hashPassword(password: string): Promise<string> {
    const encoder = new TextEncoder();
    const data = encoder.encode(password);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  public async initialize(username: string, password: string): Promise<void> {
    const hashedPassword = await this.hashPassword(password);
    const auth = {
      username,
      password: hashedPassword,
      created: new Date().toISOString()
    };
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(auth));
  }

  public async login(username: string, password: string): Promise<boolean> {
    try {
      const authData = localStorage.getItem(this.STORAGE_KEY);
      if (!authData) {
        return false;
      }

      const auth = JSON.parse(authData);
      const hashedPassword = await this.hashPassword(password);

      if (auth.username === username && auth.password === hashedPassword) {
        // Create session
        const session = {
          username,
          loginTime: new Date().toISOString(),
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours
        };
        localStorage.setItem(this.SESSION_KEY, JSON.stringify(session));
        return true;
      }

      return false;
    } catch (error) {
      console.error('Login failed:', error);
      return false;
    }
  }

  public async updateCredentials(username: string, password: string): Promise<boolean> {
    try {
      const hashedPassword = await this.hashPassword(password);
      const auth = {
        username,
        password: hashedPassword,
        updated: new Date().toISOString()
      };
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(auth));
      return true;
    } catch (error) {
      console.error('Failed to update credentials:', error);
      return false;
    }
  }

  public isAuthenticated(): boolean {
    try {
      const session = localStorage.getItem(this.SESSION_KEY);
      if (!session) {
        return false;
      }

      const { expiresAt } = JSON.parse(session);
      return new Date(expiresAt) > new Date();
    } catch {
      return false;
    }
  }

  public logout(): void {
    localStorage.removeItem(this.SESSION_KEY);
  }

  public getCurrentUser(): string | null {
    try {
      const session = localStorage.getItem(this.SESSION_KEY);
      if (!session) {
        return null;
      }

      const { username } = JSON.parse(session);
      return username;
    } catch {
      return null;
    }
  }
}

export const authManager = AuthManager.getInstance();
export default authManager;
