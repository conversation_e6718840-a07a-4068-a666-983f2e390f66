
import { supabase } from "@/integrations/supabase/client";

export const createEventNotification = async (
  eventId: string,
  eventTitle: string,
  eventDate: string
) => {
  try {
    await supabase.from("notifications").insert({
      title: "New Event: " + eventTitle,
      content: `A new event "${eventTitle}" has been scheduled for ${eventDate}. Click to see more details!`,
      type: "event",
      data: { event_id: eventId },
    });
    return true;
  } catch (error) {
    console.error("Failed to create notification:", error);
    return false;
  }
};

export const createAnnouncementNotification = async (
  announcementContent: string
) => {
  try {
    await supabase.from("notifications").insert({
      title: "New Announcement",
      content: announcementContent,
      type: "announcement",
    });
    return true;
  } catch (error) {
    console.error("Failed to create notification:", error);
    return false;
  }
};
