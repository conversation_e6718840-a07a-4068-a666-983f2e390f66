// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://mzwvhyrcgaxyrihbolsv.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im16d3ZoeXJjZ2F4eXJpaGJvbHN2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzk5MDA0ODUsImV4cCI6MjA1NTQ3NjQ4NX0.2mgXhbTE7oXkxges9Z-nsN4ngvCNTDTUiGmYfg4WWQc";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);