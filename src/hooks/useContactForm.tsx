
import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

interface ContactFormData {
  name: string;
  email: string;
  phone?: string;
  message: string;
}

export const useContactForm = () => {
  const [loading, setLoading] = useState(false);

  const submitContactForm = async (formData: ContactFormData) => {
    try {
      setLoading(true);
      
      const { error } = await supabase.from('contact_messages').insert([formData]);
      
      if (error) throw error;
      
      toast.success('Your message has been sent successfully!');
      return { success: true, error: null };
    } catch (error: any) {
      console.error('Error submitting contact form:', error);
      toast.error('Failed to send message. Please try again later.');
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  };

  return { submitContactForm, loading };
};
