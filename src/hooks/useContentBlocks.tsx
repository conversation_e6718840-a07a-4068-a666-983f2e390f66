
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Tables } from '@/integrations/supabase/types';

export const useContentBlocks = (page: string, section?: string) => {
  const [contentBlocks, setContentBlocks] = useState<Tables<'content_blocks'>[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchContentBlocks() {
      try {
        setLoading(true);
        let query = supabase
          .from('content_blocks')
          .select('*')
          .eq('page', page);

        if (section) {
          query = query.eq('section', section);
        }

        const { data, error } = await query;

        if (error) throw error;
        setContentBlocks(data || []);
      } catch (err: any) {
        console.error('Error fetching content blocks:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }

    fetchContentBlocks();
  }, [page, section]);

  return { contentBlocks, loading, error };
};
