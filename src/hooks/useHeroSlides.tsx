
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Tables } from '@/integrations/supabase/types';

export const useHeroSlides = () => {
  const [slides, setSlides] = useState<Tables<'hero_slides'>[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchSlides() {
      try {
        setLoading(true);
        const { data, error } = await supabase
          .from('hero_slides')
          .select('*')
          .order('order_index', { ascending: true })
          .eq('is_active', true);

        if (error) throw error;
        setSlides(data || []);
      } catch (err: any) {
        console.error('Error fetching hero slides:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }

    fetchSlides();
  }, []);

  return { slides, loading, error };
};
