
import { Layout } from "@/components/Layout";
import { Hero } from "@/components/Hero";
import { TempleIntro } from "@/components/TempleIntro";
import { FeaturedEvents } from "@/components/FeaturedEvents";
import { TempleServices } from "@/components/TempleServices";
import { QuoteOfTheDay } from "@/components/QuoteOfTheDay";
import { DynamicAnnouncements } from "@/components/DynamicAnnouncements";
import { useState, useEffect } from "react";
import { AutoEventPopup } from "@/components/AutoEventPopup";

const Index = () => {
  const [showEventPopup, setShowEventPopup] = useState(true);
  return (
    <>
      {showEventPopup && <AutoEventPopup onClose={() => setShowEventPopup(false)} />}
      <Layout hideAnnouncements={true}>
        <Hero />
        <DynamicAnnouncements />
        <TempleIntro />
        <TempleServices />
        <FeaturedEvents />
        <div className="py-12 bg-secondary/50">
          <div className="container mx-auto px-4">
            <div className="max-w-2xl mx-auto">
              <QuoteOfTheDay />
            </div>
          </div>
        </div>
      </Layout>
    </>
  );
};

export default Index;
