
import { Layout } from "@/components/Layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "@/hooks/use-toast";
import {
  DarshanamIcon,
  ArchanaIcon,
  AksharasrikaramIcon,
  KeshaKandanaIcon,
  AnnaprasanaIcon,
  OdibiyyamIcon,
  MudupuIcon,
  PustakaPoojaIcon,
  VahanaPoojaIcon
} from "@/components/ServiceIcons";

// Temple services data
const services = [
  {
    category: "darshan",
    title: "Darshanam",
    time: "Monday to Saturday 09:00 AM to 12:00 PM and 04:00 PM to 06:00 PM",
    description: "Darshanam Tickets are available at Temple",
    bookable: false,
    price: "",
    icon: DarshanamIcon,
    iconColor: "text-temple-gold"
  },
  {
    category: "puja",
    title: "Archana",
    time: "During temple timings",
    description: "Archana will be performed during temple timings. Archana tickets are available at Temple.",
    bookable: false,
    price: "",
    icon: ArchanaIcon,
    iconColor: "text-temple-saffron"
  },
  {
    category: "ceremony",
    title: "Aksharasrikaram",
    time: "By appointment",
    description: "Contact temple Authorities before a day to perform Aksharasrikaram. Kindly, Attend in Traditional Attire.",
    bookable: true,
    price: "",
    icon: AksharasrikaramIcon,
    iconColor: "text-temple-maroon"
  },
  {
    category: "ceremony",
    title: "KeshaKandana",
    time: "By appointment",
    description: "Contact temple Authorities before a day to perform KeshaKandana. Kindly, Attend in Traditional Attire.",
    bookable: true,
    price: "",
    icon: KeshaKandanaIcon,
    iconColor: "text-temple-gold"
  },
  {
    category: "ceremony",
    title: "Annaprasana",
    time: "By appointment",
    description: "Contact temple Authorities before a day to perform Annaprasana. Kindly, Attend in Traditional Attire.",
    bookable: true,
    price: "",
    icon: AnnaprasanaIcon,
    iconColor: "text-temple-saffron"
  },
  {
    category: "ceremony",
    title: "Odibiyyam",
    time: "By appointment",
    description: "Contact temple Authorities before a day to perform Odibiyyam. Kindly, Attend in Traditional Attire.",
    bookable: true,
    price: "",
    icon: OdibiyyamIcon,
    iconColor: "text-temple-maroon"
  },
  {
    category: "puja",
    title: "Mudupu",
    time: "By appointment",
    description: "Contact temple Authorities before a day to perform Mudupu. Kindly, Attend in Traditional Attire.",
    bookable: true,
    price: "",
    icon: MudupuIcon,
    iconColor: "text-temple-gold"
  },
  {
    category: "puja",
    title: "Pustaka Pooja",
    time: "By appointment",
    description: "Contact temple Authorities before a day to perform Pustaka Pooja. Kindly, Attend in Traditional Attire.",
    bookable: true,
    price: "",
    icon: PustakaPoojaIcon,
    iconColor: "text-temple-saffron"
  },
  {
    category: "puja",
    title: "Vahana Pooja",
    time: "By appointment",
    description: "Contact temple Authorities before a day to perform Vahana Pooja. Kindly, Attend in Traditional Attire.",
    bookable: true,
    price: "",
    icon: VahanaPoojaIcon,
    iconColor: "text-temple-maroon"
  }
];

const Services = () => {
  const handleBookService = () => {
    toast({
      title: "Booking Request Received",
      description: "We'll contact you shortly to confirm your booking details.",
    });
  };

  return (
    <Layout>
      {/* Hero Section */}
      <div
        className="h-[40vh] bg-cover bg-center flex items-center justify-center"
        style={{ backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.7)), url('/images/AS_Overview.jpg')` }}
      >
        <div className="text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-temple-ivory mb-4 font-heading tracking-wider">
            Temple Services
          </h1>
          <p className="text-xl text-temple-ivory/90 max-w-2xl mx-auto px-4">
            Explore our range of religious ceremonies and spiritual services
          </p>
        </div>
      </div>

      {/* Services List */}
      <div className="py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto mb-10">
            <h2 className="text-3xl font-heading text-temple-maroon dark:text-temple-gold mb-6 text-center">
              Our Services
            </h2>

            <div className="temple-divider w-32 mx-auto mb-8"></div>

            <p className="text-center text-muted-foreground">
              We offer a variety of traditional Hindu religious services, from daily rituals to special ceremonies
              for important life events. Our experienced priests perform all rituals according to Vedic traditions.
            </p>
          </div>

          <Tabs defaultValue="all" className="max-w-5xl mx-auto">
            <TabsList className="grid grid-cols-4 mb-8">
              <TabsTrigger value="all">All Services</TabsTrigger>
              <TabsTrigger value="darshan">Darshanam</TabsTrigger>
              <TabsTrigger value="puja">Pujas</TabsTrigger>
              <TabsTrigger value="ceremony">Ceremonies</TabsTrigger>
            </TabsList>

            <TabsContent value="all" className="mt-0">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {services.map((service, index) => (
                  <Card key={index} className="hover-glow border-temple-gold/20 overflow-hidden group">
                    <div className="h-48 flex items-center justify-center bg-gradient-to-br from-temple-light-ivory to-temple-ivory dark:from-temple-dark-maroon dark:to-temple-maroon/80 transition-all duration-300 group-hover:from-temple-light-gold/20 group-hover:to-temple-gold/30 dark:group-hover:from-temple-saffron/20 dark:group-hover:to-temple-gold/30">
                      {service.icon && (
                        <service.icon className={`h-24 w-24 ${service.iconColor} transition-all duration-300 group-hover:scale-110 group-hover:drop-shadow-glow`} />
                      )}
                    </div>
                    <CardHeader>
                      <CardTitle className="font-heading text-temple-maroon dark:text-temple-gold">
                        <span>{service.title}</span>
                      </CardTitle>
                      <CardDescription>{service.time}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground">{service.description}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="darshan" className="mt-0">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {services
                  .filter(service => service.category === "darshan")
                  .map((service, index) => (
                    <Card key={index} className="hover-glow border-temple-gold/20 overflow-hidden group">
                      <div className="h-48 flex items-center justify-center bg-gradient-to-br from-temple-light-ivory to-temple-ivory dark:from-temple-dark-maroon dark:to-temple-maroon/80 transition-all duration-300 group-hover:from-temple-light-gold/20 group-hover:to-temple-gold/30 dark:group-hover:from-temple-saffron/20 dark:group-hover:to-temple-gold/30">
                        {service.icon && (
                          <service.icon className={`h-24 w-24 ${service.iconColor} transition-all duration-300 group-hover:scale-110 group-hover:drop-shadow-glow`} />
                        )}
                      </div>
                      <CardHeader>
                        <CardTitle className="font-heading text-temple-maroon dark:text-temple-gold">
                          <span>{service.title}</span>
                        </CardTitle>
                        <CardDescription>{service.time}</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-muted-foreground">{service.description}</p>
                      </CardContent>
                    </Card>
                  ))}
              </div>
            </TabsContent>

            <TabsContent value="puja" className="mt-0">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {services
                  .filter(service => service.category === "puja")
                  .map((service, index) => (
                    <Card key={index} className="hover-glow border-temple-gold/20 overflow-hidden group">
                      <div className="h-48 flex items-center justify-center bg-gradient-to-br from-temple-light-ivory to-temple-ivory dark:from-temple-dark-maroon dark:to-temple-maroon/80 transition-all duration-300 group-hover:from-temple-light-gold/20 group-hover:to-temple-gold/30 dark:group-hover:from-temple-saffron/20 dark:group-hover:to-temple-gold/30">
                        {service.icon && (
                          <service.icon className={`h-24 w-24 ${service.iconColor} transition-all duration-300 group-hover:scale-110 group-hover:drop-shadow-glow`} />
                        )}
                      </div>
                      <CardHeader>
                        <CardTitle className="font-heading text-temple-maroon dark:text-temple-gold">
                          <span>{service.title}</span>
                        </CardTitle>
                        <CardDescription>{service.time}</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-muted-foreground">{service.description}</p>
                      </CardContent>
                    </Card>
                  ))}
              </div>
            </TabsContent>

            <TabsContent value="ceremony" className="mt-0">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {services
                  .filter(service => service.category === "ceremony")
                  .map((service, index) => (
                    <Card key={index} className="hover-glow border-temple-gold/20 overflow-hidden group">
                      <div className="h-48 flex items-center justify-center bg-gradient-to-br from-temple-light-ivory to-temple-ivory dark:from-temple-dark-maroon dark:to-temple-maroon/80 transition-all duration-300 group-hover:from-temple-light-gold/20 group-hover:to-temple-gold/30 dark:group-hover:from-temple-saffron/20 dark:group-hover:to-temple-gold/30">
                        {service.icon && (
                          <service.icon className={`h-24 w-24 ${service.iconColor} transition-all duration-300 group-hover:scale-110 group-hover:drop-shadow-glow`} />
                        )}
                      </div>
                      <CardHeader>
                        <CardTitle className="font-heading text-temple-maroon dark:text-temple-gold">
                          <span>{service.title}</span>
                        </CardTitle>
                        <CardDescription>{service.time}</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-muted-foreground">{service.description}</p>
                      </CardContent>
                    </Card>
                  ))}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* Additional Information */}
      <div className="py-12 bg-secondary/50">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto">
            <h3 className="text-2xl font-heading text-temple-maroon dark:text-temple-gold mb-4 text-center">
              Booking Information
            </h3>

            <Card className="border-temple-gold/20">
              <CardContent className="pt-6">
                <div className="prose prose-lg max-w-none dark:prose-invert">
                  <p>
                    To book a service or for more information, please contact our temple office:
                  </p>
                  <ul>
                    <li>Phone: +91 82477 21046</li>
                    <li>Email: <EMAIL></li>
                    <li>Visit in person: Speak with any of our temple priests or office staff</li>
                  </ul>
                  <p>
                    <strong>Please note:</strong> For special ceremonies and life events, we recommend booking at least
                    two weeks in advance to ensure availability of priests and proper preparation.
                  </p>
                  <p>
                    All services include the necessary puja items. However, devotees may bring additional offerings
                    like fruits, flowers, or sweets if they wish.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Services;
