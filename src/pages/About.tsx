import { useEffect } from 'react';
import { Layout } from "@/components/Layout";
import { FaBook, FaPrayingHands, FaMapMarkerAlt, FaHistory, FaRegCalendarAlt, FaWater, FaMountain } from "react-icons/fa";
import { GiMeditation, GiSpellBook, GiStonePath, GiPrayer, GiTempleGate } from "react-icons/gi";
import { motion } from 'framer-motion';
import { contactInfo } from '@/config/contact.tsx';

// Animation variants
const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.6 }
  }
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const About = () => {
  useEffect(() => {
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      });
    });

    // Add scroll reveal animation
    const animateOnScroll = () => {
      const elements = document.querySelectorAll('.animate-on-scroll');
      elements.forEach(element => {
        const elementTop = element.getBoundingClientRect().top;
        const windowHeight = window.innerHeight;
        if (elementTop < windowHeight - 100) {
          element.classList.add('animate-fadeInUp');
        }
      });
    };

    window.addEventListener('scroll', animateOnScroll);
    // Initial check
    animateOnScroll();

    return () => {
      window.removeEventListener('scroll', animateOnScroll);
    };
  }, []);

  return (
    <Layout>
      {/* Hero Section */}
      <section
        className="relative h-[80vh] min-h-[600px] bg-cover bg-center flex items-center justify-center overflow-hidden"
        style={{ backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.7)), url('/images/AS_Overview.jpg')` }}
      >
        <div className="absolute inset-0 bg-gradient-to-b from-transparent to-temple-maroon/30"></div>
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="relative z-10 text-center px-4 w-full max-w-6xl mx-auto"
        >



        </motion.div>

        {/* Scroll indicator */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.5, duration: 0.8, repeat: Infinity, repeatType: 'reverse' }}
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex flex-col items-center"
        >
          <span className="text-temple-ivory/80 text-sm mb-2">Scroll Down</span>
          <div className="w-6 h-10 border-2 border-temple-gold/50 rounded-full flex justify-center p-1">
            <motion.div
              className="w-1 h-2 bg-temple-gold rounded-full"
              animate={{ y: [0, 8, 0] }}
              transition={{ duration: 1.5, repeat: Infinity, repeatType: 'loop' }}
            />
          </div>
        </motion.div>
      </section>

      {/* Quick Navigation */}
      <div className="sticky top-0 z-40 bg-white/80 dark:bg-gray-900/80 backdrop-blur-md shadow-sm">
        <div className="container mx-auto px-4">
          <nav className="flex overflow-x-auto py-4 hide-scrollbar">
            <div className="flex space-x-6 mx-auto">
              {['History', 'Temple', 'Yagnam', 'Location', 'Events'].map((item) => (
                <a
                  key={item}
                  href={`#${item.toLowerCase()}`}
                  className="whitespace-nowrap px-4 py-2 text-sm font-medium text-gray-600 hover:text-temple-maroon dark:text-gray-300 dark:hover:text-temple-gold transition-colors duration-200 border-b-2 border-transparent hover:border-temple-gold"
                >
                  {item}
                </a>
              ))}
            </div>
          </nav>
        </div>
      </div>

      {/* Temple History */}
      <section id="history" className="py-24 bg-gradient-to-b from-white to-gray-50 dark:from-gray-900 dark:to-gray-800">
        <div className="container mx-auto px-4">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.2 }}
            variants={staggerContainer}
            className="max-w-5xl mx-auto"
          >
            <motion.div
              variants={fadeInUp}
              className="text-center mb-20"
            >
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ type: 'spring', stiffness: 200, damping: 20 }}
                className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-temple-gold/10 text-temple-gold mb-6"
              >
                <FaHistory className="w-10 h-10" />
              </motion.div>
              <motion.h2
                variants={fadeInUp}
                className="text-4xl md:text-5xl font-heading text-temple-maroon dark:text-temple-gold mb-4"
              >
                Our Sacred Heritage
              </motion.h2>
              <motion.div
                variants={fadeInUp}
                className="w-24 h-1 bg-temple-gold mx-auto mb-6"
              ></motion.div>
              <motion.p
                variants={fadeInUp}
                className="text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto leading-relaxed"
              >
                A journey through time, faith, and divine intervention
              </motion.p>
            </motion.div>

            <div className="prose prose-lg max-w-none dark:prose-invert prose-headings:font-heading prose-headings:text-temple-maroon dark:prose-headings:text-temple-gold">
              <motion.div
                variants={fadeInUp}
                className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 mb-12 border-l-4 border-temple-gold"
              >
                <p className="text-lg leading-relaxed text-gray-700 dark:text-gray-300 mb-6">
                  Anantasagar Kshetramu was established with the vision to create a spiritual sanctuary
                  that honors the divine feminine energy and serves the Hindu community. What began as a small prayer hall has
                  grown into a magnificent temple complex that attracts devotees from across the region.
                </p>

                <div className="grid md:grid-cols-2 gap-8 items-center my-10">
                  <div>
                    <p className="text-lg leading-relaxed text-gray-700 dark:text-gray-300">
                      The temple is named Anantasagar Kshetramu, representing the sacred land near Anantasagar.
                      The name combines "Ananta" (endless) with "Sagara" (ocean), symbolizing the limitless divine grace that flows
                      to all devotees who worship here with devotion.
                    </p>
                  </div>
                  <div className="bg-temple-maroon/5 dark:bg-temple-gold/5 p-6 rounded-lg border border-temple-maroon/10 dark:border-temple-gold/10">
                    <h3 className="flex items-center text-xl font-heading text-temple-maroon dark:text-temple-gold mb-3">
                      <GiTempleGate className="mr-2" /> Temple Architecture
                    </h3>
                    <p className="text-gray-700 dark:text-gray-300">
                      Built in the traditional South Indian architectural style, our temple features intricate carvings,
                      majestic gopurams, and sacred spaces designed according to ancient Agama Shastra principles.
                    </p>
                  </div>
                </div>

                <p className="text-lg leading-relaxed text-gray-700 dark:text-gray-300">
                  Over the years, the temple has expanded its services and facilities to better serve the community. Today,
                  we not only conduct regular pujas and religious ceremonies but also host cultural events, educational programs,
                  and community service activities that promote Hindu values and traditions.
                </p>
              </motion.div>

              {/* Saraswati Temple Section */}
              <motion.div
                variants={fadeInUp}
                className="bg-gradient-to-r from-temple-maroon/5 to-temple-gold/5 dark:from-temple-maroon/10 dark:to-temple-gold/10 rounded-2xl p-8 mb-12 border border-temple-maroon/10 dark:border-temple-gold/10"
              >
                <div className="flex items-center mb-6">
                  <div className="bg-temple-gold/10 p-3 rounded-full mr-4">
                    <GiSpellBook className="text-temple-gold w-6 h-6" />
                  </div>
                  <h3 className="text-2xl font-heading text-temple-maroon dark:text-temple-gold">Rare Temple of Goddess Saraswati</h3>
                </div>
                <div className="space-y-6">
                  <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                    Among the many temples in India, those dedicated to Goddess Saraswati are rare. The oldest is the Vaishnavi temple in Kashmir,
                    followed by the famous temple at Basara (Vasara) in Adilabad District of Andhra Pradesh. The temple at Ananthasagar in Siddipet
                    District of Telangana has gained prominence in recent times.
                  </p>

                  <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                    The rarity and sanctity of our temple lie in the unique idol of Goddess Saraswati in a standing posture, holding a Veena in one
                    hand (Veenapani), a Book, and Japmala in her other hands. Alongside Goddess Saraswati, the temple also houses idols of
                    Sowbhagyalaxmi and Dakshinakali, both in standing positions, as well as a Sivalingam. Another unique feature is the
                    north-facing idol of Lord Hanuman, which is quite uncommon in India.
                  </p>
                </div>
              </motion.div>

              {/* Location Section */}
              <div className="grid md:grid-cols-2 gap-8 mb-12">
                <motion.div
                  variants={fadeInUp}
                  className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 border border-gray-100 dark:border-gray-700"
                >
                  <div className="flex items-center mb-4">
                    <div className="bg-temple-gold/10 p-2 rounded-full mr-3">
                      <FaMapMarkerAlt className="text-temple-gold w-5 h-5" />
                    </div>
                    <h3 className="text-xl font-heading text-temple-maroon dark:text-temple-gold">Location</h3>
                  </div>
                  <p className="text-gray-700 dark:text-gray-300">
                    The temple is located at Swethachalam, near Ananthasagar village, on the hillocks of Ananthasagar village along the
                    Rajiv Rahadari highway from Hyderabad to Karimnagar, about 20 km from Siddipet town.
                  </p>
                  <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                    <h4 className="font-medium text-temple-maroon dark:text-temple-gold mb-2">Contact Information:</h4>
                    <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                      <li className="flex items-start">
                        <span className="text-temple-gold mr-2">•</span>
                        <a href={contactInfo.phone.href} className="hover:text-temple-gold transition-colors flex items-center gap-1">
                          {contactInfo.phone.icon}
                          {contactInfo.phone.display}
                        </a>
                      </li>
                      <li className="flex items-start">
                        <span className="text-temple-gold mr-2">•</span>
                        <a href={contactInfo.email.href} className="hover:text-temple-gold transition-colors flex items-center gap-1">
                          {contactInfo.email.icon}
                          {contactInfo.email.display}
                        </a>
                      </li>
                      <li className="flex items-start">
                        <span className="text-temple-gold mr-2">•</span>
                        <span className="flex items-center gap-1">
                          {contactInfo.address.icon}
                          {contactInfo.address.display}
                        </span>
                      </li>
                    </ul>

                    <h4 className="font-medium text-temple-maroon dark:text-temple-gold mt-4 mb-2">Distances:</h4>
                    <ul className="space-y-1 text-sm text-gray-600 dark:text-gray-300">
                      <li>• 2 km from Sanigaram RTC Express Bus stop</li>
                      <li>• 20 km from Siddipet</li>
                      <li>• 40 km from Karimnagar</li>
                    </ul>
                  </div>
                </motion.div>

                {/* Healing Waters Section */}
                <motion.div
                  variants={fadeInUp}
                  className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 border border-gray-100 dark:border-gray-700"
                >
                  <div className="flex items-center mb-4">
                    <div className="bg-temple-gold/10 p-2 rounded-full mr-3">
                      <FaWater className="text-temple-gold w-5 h-5" />
                    </div>
                    <h3 className="text-xl font-heading text-temple-maroon dark:text-temple-gold">Healing Waters</h3>
                  </div>
                  <p className="text-gray-700 dark:text-gray-300 mb-4">
                    The area is blessed with three natural water caves known as Ragi Donelu, Cheekati Donelu, and Pala Donelu.
                    The local community believes these waters have healing properties and can cure various diseases.
                  </p>
                  <div className="bg-temple-gold/5 dark:bg-temple-gold/10 p-4 rounded-lg border-l-4 border-temple-gold">
                    <p className="text-sm text-gray-600 dark:text-gray-300 italic">
                      "Scientific analysis has revealed that the water from Ragidonelu contains Copper oxide, a rare composition.
                      It is said that in ancient times, these caves were used by Rishis for meditation and spiritual practices."
                    </p>
                  </div>
                </motion.div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Sri Saraswati Yagnam */}
      <section id="yagnam" className="relative py-24 bg-gradient-to-br from-temple-maroon to-temple-maroon/90 dark:from-gray-900 dark:to-temple-maroon/80 overflow-hidden">
        <div className="absolute inset-0 bg-[url('/images/pattern.png')] opacity-5"></div>
        <div className="absolute inset-0 bg-radial-gradient from-temple-gold/5 to-transparent opacity-30"></div>
        <div className="relative z-10 container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="max-w-5xl mx-auto text-center mb-20"
          >
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ type: 'spring', stiffness: 200, damping: 20, delay: 0.2 }}
              className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-temple-gold/20 text-temple-gold mb-6"
            >
              <GiPrayer className="w-10 h-10" />
            </motion.div>
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="text-4xl md:text-5xl font-heading text-temple-ivory mb-4"
            >
              Sri Saraswati Yagnam
            </motion.h2>
            <motion.div
              initial={{ scaleX: 0 }}
              animate={{ scaleX: 1 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="w-24 h-1 bg-temple-gold mx-auto mb-6"
            ></motion.div>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="text-xl text-temple-ivory/90 max-w-3xl mx-auto leading-relaxed"
            >
              A sacred fire ritual for spiritual growth and divine blessings
            </motion.p>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={staggerContainer}
            className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto"
          >
            <motion.div
              variants={fadeInUp}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-xl p-8 transform transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl border border-white/10"
            >
              <div className="bg-temple-gold/10 w-14 h-14 rounded-full flex items-center justify-center mb-6 mx-auto">
                <FaBook className="text-temple-gold w-6 h-6" />
              </div>
              <h3 className="text-xl font-heading text-temple-maroon dark:text-temple-gold text-center mb-4">
                About the Yagnam
              </h3>
              <p className="text-gray-700 dark:text-gray-300 text-center">
                The founder of the temple, Sri Astakala Narashimharama Sharma Garu, initiated the Sri Saraswati Yagnam for the
                well-being of people. To date, over 500+ yagnams have been performed across different parts of the world and still Ongoing.
              </p>
            </motion.div>

            <motion.div
              variants={fadeInUp}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-xl p-8 transform transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl border border-white/10"
            >
              <div className="bg-temple-gold/10 w-14 h-14 rounded-full flex items-center justify-center mb-6 mx-auto">
                <FaPrayingHands className="text-temple-gold w-6 h-6" />
              </div>
              <h3 className="text-xl font-heading text-temple-maroon dark:text-temple-gold text-center mb-4">
                Participate
              </h3>
              <p className="text-gray-700 dark:text-gray-300 text-center mb-4">
                Devotees can participate in the Sri Saraswati Yagnam by contacting the temple committee. This sacred ritual is
                performed for spiritual growth, knowledge, and overall well-being.
              </p>
              <div className="mt-4 text-center">
                <div className="space-y-2">
                  <a
                    href={contactInfo.phone.href}
                    className="inline-flex items-center gap-2 bg-temple-gold hover:bg-temple-gold/90 text-white font-medium py-2 px-6 rounded-full transition-colors duration-300 w-full justify-center"
                  >
                    {contactInfo.phone.icon}
                    {contactInfo.phone.display}
                  </a>
                  <a
                    href={contactInfo.email.href}
                    className="inline-flex items-center gap-2 border-2 border-temple-gold text-temple-gold hover:bg-temple-gold/10 font-medium py-2 px-6 rounded-full transition-colors duration-300 w-full justify-center"
                  >
                    {contactInfo.email.icon}
                    Email Us
                  </a>
                </div>
              </div>
            </motion.div>

            <motion.div
              variants={fadeInUp}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-xl p-8 transform transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl border border-white/10"
            >
              <div className="bg-temple-gold/10 w-14 h-14 rounded-full flex items-center justify-center mb-6 mx-auto">
                <FaRegCalendarAlt className="text-temple-gold w-6 h-6" />
              </div>
              <h3 className="text-xl font-heading text-temple-maroon dark:text-temple-gold text-center mb-4">
                Upcoming Events
              </h3>
              <p className="text-gray-700 dark:text-gray-300 text-center mb-4">
                Join us for our next Sri Saraswati Yagnam. Dates and details will be announced soon.
              </p>
              <div className="mt-4 text-center">
                <a
                  href="/events"
                  className="inline-block border-2 border-temple-gold text-temple-gold hover:bg-temple-gold/10 font-medium py-2 px-6 rounded-full transition-colors duration-300"
                >
                  View Events
                </a>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>
    </Layout>
  );
};

// Add global styles for animations
const styles = `
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fadeInUp {
    animation: fadeInUp 0.8s ease-out forwards;
  }

  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }
  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .bg-radial-gradient {
    background: radial-gradient(circle at center, var(--tw-gradient-stops));
  }
`;

// Add styles to head
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style');
  styleElement.textContent = styles;
  document.head.appendChild(styleElement);
}

export default About;
