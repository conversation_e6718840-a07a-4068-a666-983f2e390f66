
import { Layout } from "@/components/Layout";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/hooks/use-toast";
import { Label } from "@/components/ui/label";
import { useState, FormEvent } from "react";
import { Facebook, Instagram, Youtube, Mail, Phone } from "lucide-react";
import { SocialShare } from "@/components/SocialShare";

const Contact = () => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    message: ""
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (!formData.name || !formData.email || !formData.message) {
      toast({
        title: "Error",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      toast({
        title: "Error",
        description: "Please enter a valid email address.",
        variant: "destructive",
      });
      return;
    }

    // In a real application, this would submit to a backend
    console.log("Form submitted:", formData);

    toast({
      title: "Message Sent!",
      description: "We've received your message and will get back to you soon.",
    });

    // Reset form
    setFormData({
      name: "",
      email: "",
      phone: "",
      message: ""
    });
  };

  return (
    <Layout>
      {/* Hero Section */}
      <div
        className="h-[40vh] bg-cover bg-center flex items-center justify-center"
        style={{ backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.7)), url('/images/AS_Overview.jpg')` }}
      >
        <div className="text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-temple-ivory mb-4 font-heading tracking-wider">
            Contact Us
          </h1>
          <p className="text-xl text-temple-ivory/90 max-w-2xl mx-auto px-4">
            Reach out to us for any inquiries about our temple and services
          </p>
        </div>
      </div>

      {/* Contact Form & Info */}
      <div className="py-8 sm:py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8">
              {/* Contact Form */}
              <div className="bg-card rounded-lg shadow-md p-4 sm:p-6 border border-temple-gold/20">
                <h2 className="text-xl sm:text-2xl font-heading text-temple-maroon dark:text-temple-gold mb-4 sm:mb-6 text-center sm:text-left">
                  Send us a Message
                </h2>

                <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                    <Label htmlFor="name" className="text-foreground">
                      Your Name <span className="text-temple-maroon dark:text-temple-gold">*</span>
                    </Label>
                    <Input
                      id="name"
                      name="name"
                      placeholder="Enter your full name"
                      value={formData.name}
                      onChange={handleChange}
                      className="mt-1 border-temple-gold/20"
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="email" className="text-foreground">
                      Email Address <span className="text-temple-maroon dark:text-temple-gold">*</span>
                    </Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      placeholder="Enter your email"
                      value={formData.email}
                      onChange={handleChange}
                      className="mt-1 border-temple-gold/20"
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="phone" className="text-foreground">
                      Phone Number
                    </Label>
                    <Input
                      id="phone"
                      name="phone"
                      placeholder="Enter your phone number (optional)"
                      value={formData.phone}
                      onChange={handleChange}
                      className="mt-1 border-temple-gold/20"
                    />
                  </div>

                  <div>
                    <Label htmlFor="message" className="text-foreground">
                      Message <span className="text-temple-maroon dark:text-temple-gold">*</span>
                    </Label>
                    <Textarea
                      id="message"
                      name="message"
                      placeholder="How can we help you?"
                      value={formData.message}
                      onChange={handleChange}
                      className="mt-1 min-h-[120px] border-temple-gold/20"
                      required
                    />
                  </div>

                  <Button
                    type="submit"
                    className="bg-temple-maroon hover:bg-temple-dark-maroon text-white w-full"
                  >
                    Send Message
                  </Button>
                </form>
              </div>

              {/* Contact Information */}
              <div>
                {/* Map */}
                <div className="rounded-lg overflow-hidden shadow-md h-64 sm:h-80 mb-6 border border-temple-gold/20 bg-gray-100">
                  <iframe
                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d1895.0441976478921!2d78.98599080741408!3d18.205861254071316!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3bcce989b4adff49%3A0x7acae8263d0538f!2sSri%20Saraswathi%20Kshetramu%20Main%20Temple!5e0!3m2!1sen!2sin!4v1748032207471!5m2!1sen!2sin"
                    width="100%"
                    height="100%"
                    style={{ border: 0, minHeight: '256px', display: 'block' }}
                    allowFullScreen
                    loading="lazy"
                    referrerPolicy="no-referrer-when-downgrade"
                    title="Anantasagar Kshetramu Temple Location Map"
                    frameBorder="0"
                  ></iframe>
                </div>

                {/* Contact Details */}
                <div className="bg-card rounded-lg shadow-md p-4 sm:p-6 border border-temple-gold/20">
                  <h2 className="text-xl sm:text-2xl font-heading text-temple-maroon dark:text-temple-gold mb-4 sm:mb-6 text-center sm:text-left">
                    Contact Information
                  </h2>

                  <div className="space-y-6">
                    <div className="flex items-start space-x-3">
                      <div className="bg-temple-maroon/10 dark:bg-temple-gold/10 rounded-full p-2 mt-0.5">
                        <Phone className="h-5 w-5 text-temple-maroon dark:text-temple-gold" />
                      </div>
                      <div>
                        <h3 className="font-medium text-lg mb-1">Phone</h3>
                        <a href="tel:+918247721046" className="text-muted-foreground hover:text-temple-maroon dark:hover:text-temple-gold transition-colors underline-offset-2 hover:underline text-base block">+91 82477 21046</a>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3">
                      <div className="bg-temple-maroon/10 dark:bg-temple-gold/10 rounded-full p-2 mt-0.5">
                        <Mail className="h-5 w-5 text-temple-maroon dark:text-temple-gold" />
                      </div>
                      <div>
                        <h3 className="font-medium text-lg mb-1">Email</h3>
                        <a href="mailto:<EMAIL>" className="text-muted-foreground hover:text-temple-maroon dark:hover:text-temple-gold transition-colors underline-offset-2 hover:underline text-base block"><EMAIL></a>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3">
                      <div className="bg-temple-maroon/10 dark:bg-temple-gold/10 rounded-full p-2 mt-0.5">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-temple-maroon dark:text-temple-gold">
                          <path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z" />
                          <circle cx="12" cy="10" r="3" />
                        </svg>
                      </div>
                      <div>
                        <h3 className="font-medium text-lg mb-1">Address</h3>
                        <p className="text-muted-foreground leading-relaxed">Anantasagar Rajiv Rahadari Highway</p>
                        <p className="text-muted-foreground leading-relaxed">Siddipet, India</p>
                      </div>
                    </div>

                    <div className="pt-4">
                      <h3 className="font-medium text-lg mb-2">Connect With Us</h3>
                      <div className="flex space-x-3">
                        <a href="https://facebook.com" className="hover-float bg-temple-maroon/10 dark:bg-temple-gold/10 rounded-full p-2" target="_blank" rel="noreferrer">
                          <Facebook className="h-5 w-5 text-temple-maroon dark:text-temple-gold" />
                          <span className="sr-only">Facebook</span>
                        </a>
                        <a href="https://instagram.com" className="hover-float bg-temple-maroon/10 dark:bg-temple-gold/10 rounded-full p-2" target="_blank" rel="noreferrer">
                          <Instagram className="h-5 w-5 text-temple-maroon dark:text-temple-gold" />
                          <span className="sr-only">Instagram</span>
                        </a>
                        <a href="https://youtube.com" className="hover-float bg-temple-maroon/10 dark:bg-temple-gold/10 rounded-full p-2" target="_blank" rel="noreferrer">
                          <Youtube className="h-5 w-5 text-temple-maroon dark:text-temple-gold" />
                          <span className="sr-only">YouTube</span>
                        </a>
                      </div>
                    </div>

                    <div className="pt-4">
                      <h3 className="font-medium text-lg mb-2">Share This Page</h3>
                      <SocialShare
                        url="/contact"
                        title="Contact Anantasagar Kshetramu Temple"
                        description="Get in touch with Anantasagar Kshetramu Temple. Find our location, contact details, and visiting hours."
                        iconSize={28}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Contact;
