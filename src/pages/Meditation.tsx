
import { useEffect, useState, useRef } from "react";
import { Layout } from "@/components/Layout";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { cn } from "@/lib/utils";
import { Play, Pause, Volume2, VolumeX } from "lucide-react";

const Meditation = () => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [volume, setVolume] = useState(60);
  const [isMuted, setIsMuted] = useState(false);
  const [activeMeditation, setActiveMeditation] = useState("om");
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const orbRef = useRef<HTMLDivElement>(null);
  
  // Create audio element on component mount
  useEffect(() => {
    audioRef.current = new Audio("/meditation/om-chant.mp3");
    audioRef.current.loop = true;
    
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }
    };
  }, []);
  
  // Effect for playing/pausing audio
  useEffect(() => {
    if (!audioRef.current) return;
    
    if (isPlaying) {
      const playPromise = audioRef.current.play();
      if (playPromise !== undefined) {
        playPromise.catch(error => {
          console.error("Audio playback failed:", error);
          setIsPlaying(false);
        });
      }
    } else {
      audioRef.current.pause();
    }
  }, [isPlaying]);
  
  // Effect for volume control
  useEffect(() => {
    if (!audioRef.current) return;
    
    audioRef.current.volume = isMuted ? 0 : volume / 100;
  }, [volume, isMuted]);
  
  // 3D animation effect
  useEffect(() => {
    if (!orbRef.current || !isPlaying) return;
    
    const handleMouseMove = (e: MouseEvent) => {
      const orb = orbRef.current;
      if (!orb) return;
      
      const rect = orb.getBoundingClientRect();
      const x = e.clientX - rect.left - rect.width / 2;
      const y = e.clientY - rect.top - rect.height / 2;
      
      // Calculate distance from center
      const distance = Math.sqrt(x * x + y * y);
      const maxDistance = Math.max(rect.width, rect.height) / 2;
      
      // Limit the tilt
      const tiltX = (y / maxDistance) * 15; // Max 15 degrees tilt
      const tiltY = (x / maxDistance) * -15;
      
      // Apply the transform
      orb.style.transform = `perspective(1000px) rotateX(${tiltX}deg) rotateY(${tiltY}deg)`;
      
      // Adjust glow based on distance
      const glow = Math.max(0, 1 - distance / maxDistance);
      orb.style.boxShadow = `0 0 ${30 + glow * 30}px ${glow * 15}px rgba(255, 193, 7, ${0.3 + glow * 0.4})`;
    };
    
    document.addEventListener('mousemove', handleMouseMove);
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      
      // Reset transform when unmounting
      if (orbRef.current) {
        orbRef.current.style.transform = '';
        orbRef.current.style.boxShadow = '';
      }
    };
  }, [isPlaying]);
  
  const togglePlay = () => {
    setIsPlaying(!isPlaying);
  };
  
  const toggleMute = () => {
    setIsMuted(!isMuted);
  };
  
  const handleVolumeChange = (value: number[]) => {
    setVolume(value[0]);
    if (isMuted && value[0] > 0) {
      setIsMuted(false);
    }
  };
  
  const meditationTypes = [
    { id: "om", name: "ॐ Om Chanting", description: "Traditional Om mantra for deep meditation", duration: "10 minutes" },
    { id: "chakra", name: "chakra alignment", description: "Balance your energy centers", duration: "15 minutes" },
    { id: "nature", name: "temple bells", description: "Temple bells with nature sounds", duration: "20 minutes" },
  ];
  
  return (
    <Layout hideAnnouncements={true}>
      <div className="min-h-screen bg-gradient-to-br from-background to-secondary/80 pt-24">
        <div className="container mx-auto px-4 py-12">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-heading font-bold mb-4 text-gradient">
              Divine Meditation Experience
            </h1>
            <p className="text-lg md:text-xl max-w-2xl mx-auto text-muted-foreground">
              <span className="telugu-text">శాంతి మరియు ప్రశాంతత కోసం</span> - For peace and tranquility
            </p>
            <div className="temple-divider w-32 mx-auto my-6"></div>
          </div>
          
          {/* 3D Meditation Orb */}
          <div className="flex flex-col md:flex-row items-center justify-center gap-12 mb-16">
            <div className="w-full md:w-1/2 flex justify-center">
              <div 
                ref={orbRef}
                className={cn(
                  "meditation-orb w-64 h-64 md:w-80 md:h-80 flex items-center justify-center transition-all duration-500",
                  isPlaying ? "animate-pulse-glow" : "opacity-80"
                )}
                style={{ transformStyle: 'preserve-3d' }}
              >
                <div className="relative w-full h-full flex items-center justify-center">
                  <div className="absolute inset-4 rounded-full bg-temple-gold/20 backdrop-blur-sm"></div>
                  <div className="absolute w-3/4 h-3/4 rounded-full bg-temple-gold/30"></div>
                  <div className={cn(
                    "absolute w-1/2 h-1/2 rounded-full bg-white/80 backdrop-blur-md flex items-center justify-center transform transition-transform duration-700",
                    isPlaying && "animate-pulse"
                  )}>
                    <span className="text-5xl md:text-6xl text-temple-maroon font-sanskrit">ॐ</span>
                  </div>
                  <div className="absolute inset-0 rounded-full bg-gradient-to-r from-temple-purple/10 to-temple-gold/10 animate-spin-slow"></div>
                </div>
              </div>
            </div>
            
            <div className="w-full md:w-1/2 glass-card p-6 md:p-8 rounded-2xl">
              <h2 className="text-2xl font-heading font-semibold mb-4">Meditation Control Panel</h2>
              
              {/* Meditation Type Selector */}
              <div className="mb-6">
                <h3 className="text-lg font-medium mb-3">Select Meditation Type</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  {meditationTypes.map(type => (
                    <Button
                      key={type.id}
                      onClick={() => setActiveMeditation(type.id)}
                      className={cn(
                        "h-auto py-3 flex flex-col items-center justify-center text-sm threed-button",
                        activeMeditation === type.id 
                          ? "bg-temple-gradient text-white" 
                          : "bg-secondary/60"
                      )}
                    >
                      <span className="font-medium">{type.name}</span>
                      <span className="text-xs opacity-80 mt-1">{type.duration}</span>
                    </Button>
                  ))}
                </div>
              </div>
              
              {/* Playback Controls */}
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium mb-3">Sound Controls</h3>
                  <div className="flex items-center gap-3">
                    <Button 
                      onClick={togglePlay} 
                      size="lg"
                      className={cn(
                        "rounded-full h-14 w-14 flex items-center justify-center hover-glow",
                        isPlaying ? "bg-temple-purple" : "bg-temple-gradient"
                      )}
                    >
                      {isPlaying ? <Pause size={24} /> : <Play size={24} />}
                    </Button>
                    
                    <div className="flex items-center gap-2 flex-1">
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        onClick={toggleMute}
                        className="hover:bg-accent/20"
                      >
                        {isMuted ? <VolumeX size={18} /> : <Volume2 size={18} />}
                      </Button>
                      <Slider 
                        value={[volume]} 
                        max={100} 
                        step={1} 
                        className="flex-1"
                        onValueChange={handleVolumeChange}
                      />
                    </div>
                  </div>
                </div>
                
                {/* Selected Meditation Info */}
                <div className="bg-secondary/40 p-4 rounded-lg">
                  <h3 className="font-medium">
                    {meditationTypes.find(t => t.id === activeMeditation)?.name || "Om Chanting"}
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    {meditationTypes.find(t => t.id === activeMeditation)?.description || "Traditional Om mantra for deep meditation"}
                  </p>
                </div>
                
                <div className="text-sm text-muted-foreground">
                  <p className="telugu-text">ధ్యానం మీ జీవితంలో శాంతిని మరియు సంతోషాన్ని తీసుకురావాలి</p>
                  <p className="mt-1 text-xs">Meditation brings peace and happiness to your life</p>
                </div>
              </div>
            </div>
          </div>
          
          {/* Benefits of Meditation */}
          <div className="mt-16">
            <h2 className="text-3xl font-heading text-center font-bold mb-10 text-gradient">Benefits of Meditation</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {[
                {
                  title: "Mental Clarity",
                  description: "Regular meditation reduces mental clutter and brings clarity of thought.",
                  icon: "🧠"
                },
                {
                  title: "Stress Reduction",
                  description: "Meditation helps your body and mind release stress and tension.",
                  icon: "😌"
                },
                {
                  title: "Spiritual Growth",
                  description: "Connect with your inner divinity and expand your spiritual awareness.",
                  icon: "✨"
                }
              ].map((benefit, index) => (
                <div key={index} className="premium-card threed-card">
                  <div className="text-4xl mb-4">{benefit.icon}</div>
                  <h3 className="text-xl font-heading font-semibold mb-2">{benefit.title}</h3>
                  <p className="text-muted-foreground">{benefit.description}</p>
                </div>
              ))}
            </div>
          </div>
          
          {/* Meditation Tips */}
          <div className="mt-16 text-center">
            <h2 className="text-3xl font-heading font-bold mb-6">Meditation Tips</h2>
            <div className="max-w-2xl mx-auto">
              <p className="mb-4 text-lg">Find a quiet place, sit in a comfortable position, and focus on your breath.</p>
              <p className="telugu-text text-lg mb-6">నిశ్శబ్దమైన ప్రదేశాన్ని కనుగొని, సౌకర్యవంతమైన స్థానంలో కూర్చుని, మీ శ్వాసపై దృష్టి పెట్టండి.</p>
              <Button className="button-gradient">Download Meditation Guide</Button>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Meditation;
