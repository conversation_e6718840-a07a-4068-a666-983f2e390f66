
import { Layout } from "@/components/Layout";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogClose } from "@/components/ui/dialog";
import { X, Play } from "lucide-react";

// Sample gallery items - replace with actual data from backend in production
const galleryItems = [
  {
    id: 1,
    title: "Temple Video Tour",
    category: "architecture",
    image: "/images/AS_Overview.jpg",
    type: "video",
    videoUrl: "/images/temple_video.mp4"
  },
  {
    id: 2,
    title: "Diwali Celebrations",
    category: "festivals",
    image: "/images/vasanthapanchami 1920x678.jpg",
    type: "image"
  },
  {
    id: 3,
    title: "Morning Aarti",
    category: "daily-rituals",
    image: "/images/500_yagnam1920x678_copy.jpg",
    type: "image"
  },
  {
    id: 4,
    title: "Temple Interior",
    category: "architecture",
    image: "/images/Home_Eng.jpg",
    type: "image"
  },
  {
    id: 5,
    title: "Holi Festival",
    category: "festivals",
    image: "/images/event.jpg.jpg",
    type: "image"
  },
  {
    id: 6,
    title: "Deity Decoration",
    category: "daily-rituals",
    image: "/images/img_2.png",
    type: "image"
  },
  {
    id: 7,
    title: "Cultural Program",
    category: "events",
    image: "/images/event_upcoming1.jpg",
    type: "image"
  },
  {
    id: 8,
    title: "Ganesh Chaturthi",
    category: "festivals",
    image: "/images/event_upcoming2.jpg",
    type: "image"
  },
  {
    id: 9,
    title: "Temple at Night",
    category: "architecture",
    image: "/images/a3.jpg",
    type: "image"
  },
  {
    id: 10,
    title: "Annual Procession",
    category: "events",
    image: "/images/a4.jpg",
    type: "image"
  },
  {
    id: 11,
    title: "Temple Towers",
    category: "architecture",
    image: "/images/as_1.jpg",
    type: "image"
  },
  {
    id: 12,
    title: "Sacred Rituals",
    category: "daily-rituals",
    image: "/images/Founder (Custom).jpg",
    type: "image"
  }
];

const Gallery = () => {
  const [activeCategory, setActiveCategory] = useState("all");
  const [selectedItem, setSelectedItem] = useState<null | typeof galleryItems[0]>(null);

  const filteredItems = activeCategory === "all"
    ? galleryItems
    : galleryItems.filter(item => item.category === activeCategory);

  return (
    <Layout>
      {/* Hero Section */}
      <div
        className="h-[40vh] bg-cover bg-center flex items-center justify-center"
        style={{ backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.7)), url('/images/AS_Overview.jpg')` }}
      >
        <div className="text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-temple-ivory mb-4 font-heading tracking-wider">
            Temple Gallery
          </h1>
          <p className="text-xl text-temple-ivory/90 max-w-2xl mx-auto px-4">
            Visual journey through our temple's sacred spaces and events
          </p>
        </div>
      </div>

      {/* Gallery Section */}
      <div className="py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl font-heading text-temple-maroon dark:text-temple-gold mb-6 text-center">
              Explore Our Gallery
            </h2>

            <div className="temple-divider w-32 mx-auto mb-8"></div>

            {/* Category Filters */}
            <div className="flex flex-wrap justify-center gap-2 mb-10">
              <Button
                onClick={() => setActiveCategory("all")}
                variant={activeCategory === "all" ? "default" : "outline"}
                className={activeCategory === "all"
                  ? "bg-temple-maroon hover:bg-temple-dark-maroon text-white"
                  : "border-temple-maroon/20 hover:border-temple-maroon dark:border-temple-gold/20 dark:hover:border-temple-gold"
                }
              >
                All
              </Button>
              <Button
                onClick={() => setActiveCategory("architecture")}
                variant={activeCategory === "architecture" ? "default" : "outline"}
                className={activeCategory === "architecture"
                  ? "bg-temple-maroon hover:bg-temple-dark-maroon text-white"
                  : "border-temple-maroon/20 hover:border-temple-maroon dark:border-temple-gold/20 dark:hover:border-temple-gold"
                }
              >
                Architecture
              </Button>
              <Button
                onClick={() => setActiveCategory("festivals")}
                variant={activeCategory === "festivals" ? "default" : "outline"}
                className={activeCategory === "festivals"
                  ? "bg-temple-maroon hover:bg-temple-dark-maroon text-white"
                  : "border-temple-maroon/20 hover:border-temple-maroon dark:border-temple-gold/20 dark:hover:border-temple-gold"
                }
              >
                Festivals
              </Button>
              <Button
                onClick={() => setActiveCategory("daily-rituals")}
                variant={activeCategory === "daily-rituals" ? "default" : "outline"}
                className={activeCategory === "daily-rituals"
                  ? "bg-temple-maroon hover:bg-temple-dark-maroon text-white"
                  : "border-temple-maroon/20 hover:border-temple-maroon dark:border-temple-gold/20 dark:hover:border-temple-gold"
                }
              >
                Daily Rituals
              </Button>
              <Button
                onClick={() => setActiveCategory("events")}
                variant={activeCategory === "events" ? "default" : "outline"}
                className={activeCategory === "events"
                  ? "bg-temple-maroon hover:bg-temple-dark-maroon text-white"
                  : "border-temple-maroon/20 hover:border-temple-maroon dark:border-temple-gold/20 dark:hover:border-temple-gold"
                }
              >
                Events
              </Button>
            </div>

            {/* Gallery Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {filteredItems.map(item => (
                <div
                  key={item.id}
                  onClick={() => setSelectedItem(item)}
                  className="cursor-pointer rounded-lg overflow-hidden hover-glow"
                >
                  <div className="relative aspect-square">
                    <img
                      src={item.image}
                      alt={item.title}
                      className="object-cover w-full h-full"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent flex flex-col justify-end p-4 opacity-0 hover:opacity-100 transition-opacity duration-300">
                      <h3 className="text-white font-medium">{item.title}</h3>
                    </div>
                    {item.type === 'video' && (
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="bg-black/50 rounded-full p-3 hover:bg-temple-gold/70 transition-colors">
                          <Play className="h-8 w-8 text-white" fill="white" />
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {/* Lightbox Dialog */}
            <Dialog open={!!selectedItem} onOpenChange={() => setSelectedItem(null)}>
              <DialogContent className="max-w-4xl p-0 bg-background border-none">
                <div className="relative">
                  {selectedItem?.type === 'video' ? (
                    <video
                      src={selectedItem.videoUrl}
                      controls
                      autoPlay
                      className="w-full h-auto"
                      poster={selectedItem.image}
                    />
                  ) : (
                    <img
                      src={selectedItem?.image}
                      alt={selectedItem?.title || "Gallery image"}
                      className="w-full h-auto"
                    />
                  )}

                  <DialogClose className="absolute top-2 right-2 p-2 rounded-full bg-black/50 hover:bg-black/70 text-white">
                    <X className="h-5 w-5" />
                    <span className="sr-only">Close</span>
                  </DialogClose>

                  <div className="absolute bottom-0 left-0 right-0 bg-black/50 p-4">
                    <h3 className="text-white text-lg font-medium">{selectedItem?.title}</h3>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Gallery;
