
import { useState, useEffect } from 'react';
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle, 
  CardDescription 
} from '@/components/ui/card';
import { 
  Table, 
  TableBody, 
  TableCaption, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { UserPlus, User } from 'lucide-react';

const UsersManager = () => {
  const [users, setUsers] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    setLoading(true);
    try {
      const { data: { users }, error } = await supabase.auth.admin.listUsers();
      
      if (error) throw error;
      setUsers(users || []);
    } catch (error) {
      console.error('Error fetching users:', error);
      toast.error('Failed to fetch users. You might not have admin privileges.');
      
      // Fallback to show the current user at least
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (user) {
          setUsers([user]);
        }
      } catch (innerError) {
        console.error('Error getting current user:', innerError);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-heading text-gradient">Users Manager</h1>
        <Button className="button-gradient">
          <UserPlus size={16} className="mr-2" /> Invite User
        </Button>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Users</CardTitle>
          <CardDescription>Manage website users and permissions</CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-2">
              {Array(3).fill(0).map((_, index) => (
                <div key={index} className="flex items-center space-x-4 py-3">
                  <div className="h-10 w-10 rounded-full bg-slate-200 dark:bg-slate-700 animate-pulse" />
                  <div className="space-y-2">
                    <div className="h-4 w-[250px] bg-slate-200 dark:bg-slate-700 animate-pulse rounded" />
                    <div className="h-3 w-[200px] bg-slate-200 dark:bg-slate-700 animate-pulse rounded" />
                  </div>
                </div>
              ))}
            </div>
          ) : users.length > 0 ? (
            <Table>
              <TableCaption>List of registered users</TableCaption>
              <TableHeader>
                <TableRow>
                  <TableHead>User</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {users.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <div className="h-10 w-10 rounded-full bg-temple-gradient flex items-center justify-center text-white font-medium">
                          {user.email?.charAt(0).toUpperCase() || <User size={16} />}
                        </div>
                        <div>
                          <p className="font-medium">{user.user_metadata?.full_name || "User"}</p>
                          <p className="text-xs text-muted-foreground">ID: {user.id.substring(0, 8)}...</p>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>{user.email}</TableCell>
                    <TableCell>
                      <span className="bg-temple-gold/10 text-temple-maroon dark:text-temple-gold px-2 py-1 rounded text-xs font-medium">
                        {user.role || "User"}
                      </span>
                    </TableCell>
                    <TableCell>
                      {user.email_confirmed_at ? 
                        <span className="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 px-2 py-1 rounded text-xs font-medium">
                          Active
                        </span> : 
                        <span className="bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-400 px-2 py-1 rounded text-xs font-medium">
                          Pending
                        </span>
                      }
                    </TableCell>
                    <TableCell className="text-right">
                      <Button variant="outline" size="sm">
                        Manage
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="text-center py-12">
              <h3 className="text-xl font-medium mb-2">No users found</h3>
              <p className="text-muted-foreground mb-6">
                There are no registered users in the system yet
              </p>
              <Button>
                <UserPlus size={16} className="mr-2" /> Invite User
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default UsersManager;
