import React from 'react';
import { <PERSON>, CardHeader, Card<PERSON><PERSON><PERSON>, CardContent, CardDescription } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

const palette = [
  { name: 'Temple Gold', color: '#DAA520' },
  { name: 'Temple Saffron', color: '#FFA500' },
  { name: 'Temple Maroon', color: '#990033' },
  { name: 'Temple Ivory', color: '#F5F5DC' },
];

const DesignSystem: React.FC = () => {
  return (
    <div className="container mx-auto py-8 space-y-8">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🎨 Design System <Badge variant="secondary">Beta</Badge>
          </CardTitle>
          <CardDescription>
            Manage and preview your website's color palette, typography, and design tokens.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <h3 className="font-semibold mb-2">Color Palette</h3>
          <div className="flex gap-6 mb-6">
            {palette.map((p) => (
              <div key={p.name} className="flex flex-col items-center">
                <div className="w-12 h-12 rounded-full border-2" style={{ background: p.color }} />
                <span className="mt-2 text-xs text-muted-foreground">{p.name}</span>
                <span className="text-xs">{p.color}</span>
              </div>
            ))}
          </div>
          <h3 className="font-semibold mb-2">Typography</h3>
          <div className="mb-4">
            <div className="font-serif text-2xl mb-1">Playfair Display (Headings)</div>
            <div className="font-sans text-base">Montserrat (Body)</div>
            <div className="font-sans text-base">Noto Sans Telugu (Telugu)</div>
          </div>
          <h3 className="font-semibold mb-2">Spacing & Layout</h3>
          <div className="text-sm text-muted-foreground mb-2">8px base unit, responsive grid, container max-widths</div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DesignSystem;
