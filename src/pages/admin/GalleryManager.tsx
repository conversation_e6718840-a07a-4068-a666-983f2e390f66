
import { useState, useEffect } from 'react';
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle,
  CardDescription 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Plus, Pencil, Trash2, ImageIcon } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

const GalleryManager = () => {
  const [galleryItems, setGalleryItems] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchGalleryItems();
  }, []);

  const fetchGalleryItems = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('gallery_items')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      setGalleryItems(data || []);
    } catch (error) {
      console.error('Error fetching gallery items:', error);
      toast.error('Failed to fetch gallery items');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-heading text-gradient">Gallery Manager</h1>
        <Button className="button-gradient">
          <Plus size={16} className="mr-2" /> Add New Gallery Item
        </Button>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Gallery Items</CardTitle>
          <CardDescription>Manage your temple gallery items</CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {Array(6).fill(0).map((_, index) => (
                <Card key={index} className="overflow-hidden">
                  <div className="aspect-video bg-slate-200 dark:bg-slate-700 animate-pulse flex items-center justify-center">
                    <ImageIcon className="h-10 w-10 text-slate-400" />
                  </div>
                  <CardContent className="p-4">
                    <div className="h-4 bg-slate-200 dark:bg-slate-700 animate-pulse rounded mb-2" />
                    <div className="h-3 bg-slate-200 dark:bg-slate-700 animate-pulse rounded w-2/3" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : galleryItems.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {galleryItems.map((item) => (
                <Card key={item.id} className="overflow-hidden">
                  <div 
                    className="aspect-video bg-cover bg-center"
                    style={{ backgroundImage: `url(${item.media_url})` }}
                  >
                    <div className="flex justify-end p-2">
                      <div className="flex gap-1">
                        <Button variant="secondary" size="icon" className="h-8 w-8">
                          <Pencil size={14} />
                        </Button>
                        <Button variant="destructive" size="icon" className="h-8 w-8">
                          <Trash2 size={14} />
                        </Button>
                      </div>
                    </div>
                  </div>
                  <CardContent className="p-4">
                    <h3 className="font-medium">{item.title}</h3>
                    <p className="text-sm text-muted-foreground mt-1">{item.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <h3 className="text-xl font-medium mb-2">No gallery items found</h3>
              <p className="text-muted-foreground mb-6">
                Get started by adding images or videos to your gallery
              </p>
              <Button>
                <Plus size={16} className="mr-2" /> Add Gallery Item
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default GalleryManager;
