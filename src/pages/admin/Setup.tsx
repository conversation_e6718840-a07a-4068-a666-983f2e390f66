import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { Shield, User, KeyRound } from 'lucide-react';
import { createInitialAdmin } from '@/utils/auth';

const AdminSetup = () => {
  const navigate = useNavigate();
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [accountCreated, setAccountCreated] = useState(false);

  useEffect(() => {
    // Check if admin account already exists
    const auth = localStorage.getItem(AUTH_STORAGE_KEY);
    if (auth) {
      toast.error('Admin account already exists');
      navigate('/admin/login');
    }
  }, [navigate]);

  const validateInputs = () => {
    if (!username.trim()) {
      toast.error('Please enter a username');
      return false;
    }
    
    if (!password.trim()) {
      toast.error('Please enter a password');
      return false;
    }
    
    if (password.length < 6) {
      toast.error('Password must be at least 6 characters long');
      return false;
    }
    
    if (password !== confirmPassword) {
      toast.error('Passwords do not match');
      return false;
    }
    
    return true;
  };

  const handleCreateAdmin = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateInputs()) return;
    
    try {
      setLoading(true);
      
      // Create the admin account using local storage
      await createInitialAdmin(username, password);
      
      toast.success('Admin account created successfully', {
        description: 'You can now log in with your credentials'
      });
      
      setAccountCreated(true);
      
    } catch (error: any) {
      console.error('Error creating admin account:', error);
      toast.error('Failed to create admin account', {
        description: error.message || 'Please try again'
      });
    } finally {
      setLoading(false);
    }
  };
  
  if (accountCreated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-r from-temple-maroon/10 to-temple-gold/10 px-4">
        <Card className="w-full max-w-md glass-card">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4">
              <div className="h-16 w-16 rounded-full bg-temple-gradient flex items-center justify-center mx-auto">
                <Shield className="h-8 w-8 text-white" />
              </div>
            </div>
            <CardTitle className="text-2xl font-heading text-gradient">Setup Complete</CardTitle>
            <CardDescription className="text-lg">
              Your admin account has been created successfully!
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4 text-center">
            <p>You can now log in to the admin dashboard with your credentials.</p>
          </CardContent>
          <CardFooter>
            <Button 
              className="w-full button-gradient"
              onClick={() => navigate('/admin/login')}
            >
              Go to Login Page
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-r from-temple-maroon/10 to-temple-gold/10 px-4">
      <Card className="w-full max-w-md glass-card">
        <CardHeader className="space-y-1 text-center">
          <div className="mx-auto mb-4">
            <div className="h-12 w-12 rounded-full bg-temple-gradient flex items-center justify-center mx-auto">
              <Shield className="h-6 w-6 text-white" />
            </div>
          </div>
          <CardTitle className="text-2xl font-heading text-gradient">Create Admin Account</CardTitle>
          <CardDescription>
            Set up the first administrator account for your temple website
          </CardDescription>
        </CardHeader>
        <form onSubmit={handleCreateAdmin}>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="username">Username</Label>
              <div className="relative">
                <Input
                  id="username"
                  placeholder="Choose a username"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  className="pl-10"
                  required
                />
                <User className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              </div>
            </div>
            
            <div className="space-y-2">
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <div className="relative">
                <Input
                  id="password"
                  type="password"
                  placeholder="••••••••"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="pl-10"
                  required
                />
                <KeyRound className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              </div>
              <p className="text-xs text-muted-foreground">Password must be at least 6 characters long</p>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Confirm Password</Label>
              <div className="relative">
                <Input
                  id="confirmPassword"
                  type="password"
                  placeholder="••••••••"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  className="pl-10"
                  required
                />
                <KeyRound className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button 
              type="submit" 
              className="w-full button-gradient"
              disabled={loading}
            >
              {loading ? (
                <>
                  <span className="h-4 w-4 rounded-full border-2 border-temple-ivory border-t-transparent animate-spin mr-2"></span>
                  Creating Account...
                </>
              ) : (
                "Create Admin Account"
              )}
            </Button>
          </CardFooter>
        </form>
        <div className="p-6 pt-0 text-center text-sm">
          <a href="/" className="temple-link">
            Return to website
          </a>
        </div>
      </Card>
    </div>
  );
};

export default AdminSetup;
