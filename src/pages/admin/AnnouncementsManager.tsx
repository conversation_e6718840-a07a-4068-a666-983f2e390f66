
import { useState, useEffect } from 'react';
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle, 
  CardDescription 
} from '@/components/ui/card';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogDescription,
  DialogFooter
} from '@/components/ui/dialog';
import { 
  Table, 
  TableBody, 
  TableCaption, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Calendar as CalendarIcon, Pencil, Plus, Trash2 } from 'lucide-react';
import { Calendar } from '@/components/ui/calendar';
import { format } from 'date-fns';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { 
  Popover, 
  PopoverContent, 
  PopoverTrigger 
} from '@/components/ui/popover';

const AnnouncementsManager = () => {
  const [announcements, setAnnouncements] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [currentAnnouncement, setCurrentAnnouncement] = useState<any | null>(null);
  const [formData, setFormData] = useState({
    content: '',
    expires_at: undefined as Date | undefined,
    is_active: true,
    priority: 0
  });

  useEffect(() => {
    fetchAnnouncements();
  }, []);

  const fetchAnnouncements = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('announcements')
        .select('*')
        .order('priority', { ascending: false });
      
      if (error) throw error;
      setAnnouncements(data || []);
    } catch (error) {
      console.error('Error fetching announcements:', error);
      toast.error('Failed to fetch announcements');
    } finally {
      setLoading(false);
    }
  };

  const handleAddAnnouncement = () => {
    setFormData({
      content: '',
      expires_at: undefined,
      is_active: true,
      priority: 0
    });
    setShowAddDialog(true);
  };

  const handleEditAnnouncement = (announcement: any) => {
    setCurrentAnnouncement(announcement);
    setFormData({
      content: announcement.content,
      expires_at: announcement.expires_at ? new Date(announcement.expires_at) : undefined,
      is_active: announcement.is_active,
      priority: announcement.priority || 0
    });
    setShowEditDialog(true);
  };

  const handleDeleteAnnouncement = async (id: string) => {
    if (confirm('Are you sure you want to delete this announcement?')) {
      try {
        const { error } = await supabase
          .from('announcements')
          .delete()
          .eq('id', id);

        if (error) throw error;
        toast.success('Announcement deleted successfully');
        fetchAnnouncements();
      } catch (error) {
        console.error('Error deleting announcement:', error);
        toast.error('Failed to delete announcement');
      }
    }
  };

  const handleFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'number' ? parseInt(value) : value
    });
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData({
      ...formData,
      [name]: checked
    });
  };

  const handleDateChange = (date: Date | undefined) => {
    setFormData({
      ...formData,
      expires_at: date
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.content) {
      toast.error('Announcement content is required');
      return;
    }

    try {
      const announcementData = {
        content: formData.content,
        expires_at: formData.expires_at ? formData.expires_at.toISOString() : null,
        is_active: formData.is_active,
        priority: formData.priority
      };

      if (currentAnnouncement) {
        // Update announcement
        const { error } = await supabase
          .from('announcements')
          .update(announcementData)
          .eq('id', currentAnnouncement.id);
        
        if (error) throw error;
        toast.success('Announcement updated successfully');
        setShowEditDialog(false);
      } else {
        // Add new announcement
        const { error } = await supabase
          .from('announcements')
          .insert([announcementData]);
        
        if (error) throw error;
        toast.success('Announcement created successfully');
        setShowAddDialog(false);
      }
      
      fetchAnnouncements();
    } catch (error) {
      console.error('Error saving announcement:', error);
      toast.error('Failed to save announcement');
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-heading text-gradient">Announcements Manager</h1>
        <Button className="button-gradient" onClick={handleAddAnnouncement}>
          <Plus size={16} className="mr-2" /> Add New Announcement
        </Button>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>All Announcements</CardTitle>
          <CardDescription>Manage announcements that appear on your website</CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-2">
              {Array(3).fill(0).map((_, index) => (
                <div key={index} className="py-3">
                  <div className="h-6 w-full bg-slate-200 dark:bg-slate-700 animate-pulse rounded mb-2" />
                  <div className="h-4 w-1/3 bg-slate-200 dark:bg-slate-700 animate-pulse rounded" />
                </div>
              ))}
            </div>
          ) : announcements.length > 0 ? (
            <Table>
              <TableCaption>List of all announcements</TableCaption>
              <TableHeader>
                <TableRow>
                  <TableHead>Content</TableHead>
                  <TableHead>Expiry</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Priority</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {announcements.map((announcement) => (
                  <TableRow key={announcement.id}>
                    <TableCell className="font-medium">{announcement.content}</TableCell>
                    <TableCell>
                      {announcement.expires_at
                        ? format(new Date(announcement.expires_at), 'PPP')
                        : 'Never'}
                    </TableCell>
                    <TableCell>
                      {announcement.is_active ? (
                        <span className="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 px-2 py-1 rounded text-xs font-medium">
                          Active
                        </span>
                      ) : (
                        <span className="bg-slate-100 text-slate-800 dark:bg-slate-900/30 dark:text-slate-400 px-2 py-1 rounded text-xs font-medium">
                          Inactive
                        </span>
                      )}
                    </TableCell>
                    <TableCell>{announcement.priority}</TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button 
                          variant="outline" 
                          size="sm" 
                          className="h-8 w-8 p-0" 
                          onClick={() => handleEditAnnouncement(announcement)}
                        >
                          <Pencil size={14} />
                        </Button>
                        <Button 
                          variant="destructive" 
                          size="sm" 
                          className="h-8 w-8 p-0" 
                          onClick={() => handleDeleteAnnouncement(announcement.id)}
                        >
                          <Trash2 size={14} />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="text-center py-12">
              <h3 className="text-xl font-medium mb-2">No announcements found</h3>
              <p className="text-muted-foreground mb-6">
                Get started by creating your first announcement
              </p>
              <Button onClick={handleAddAnnouncement}>
                <Plus size={16} className="mr-2" /> Add Announcement
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add Announcement Dialog */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Add New Announcement</DialogTitle>
            <DialogDescription>
              Create a new announcement to display on your website
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="content" className="text-right">Content</Label>
                <Textarea
                  id="content"
                  name="content"
                  value={formData.content}
                  onChange={handleFormChange}
                  className="col-span-3"
                  rows={4}
                  required
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="expires_at" className="text-right">Expiry Date</Label>
                <div className="col-span-3">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant={"outline"}
                        className="w-full justify-start text-left font-normal"
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {formData.expires_at 
                          ? format(formData.expires_at, 'PPP') 
                          : <span>No expiry date</span>}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={formData.expires_at}
                        onSelect={handleDateChange}
                        initialFocus
                      />
                      <div className="p-2 border-t border-border">
                        <Button
                          variant="ghost"
                          className="w-full"
                          onClick={() => handleDateChange(undefined)}
                        >
                          Clear Date
                        </Button>
                      </div>
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="priority" className="text-right">Priority</Label>
                <Input
                  id="priority"
                  name="priority"
                  type="number"
                  value={formData.priority}
                  onChange={handleFormChange}
                  className="col-span-1"
                  min={0}
                />
                <p className="text-sm text-muted-foreground col-span-2">
                  Higher numbers appear first
                </p>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label className="text-right">Status</Label>
                <div className="flex items-center col-span-3">
                  <input
                    type="checkbox"
                    id="is_active"
                    name="is_active"
                    checked={formData.is_active}
                    onChange={handleCheckboxChange}
                    className="mr-2 h-4 w-4"
                  />
                  <Label htmlFor="is_active">Active</Label>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setShowAddDialog(false)}>
                Cancel
              </Button>
              <Button type="submit">Create Announcement</Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Edit Announcement Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Edit Announcement</DialogTitle>
            <DialogDescription>
              Update announcement details
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-content" className="text-right">Content</Label>
                <Textarea
                  id="edit-content"
                  name="content"
                  value={formData.content}
                  onChange={handleFormChange}
                  className="col-span-3"
                  rows={4}
                  required
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-expires_at" className="text-right">Expiry Date</Label>
                <div className="col-span-3">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant={"outline"}
                        className="w-full justify-start text-left font-normal"
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {formData.expires_at 
                          ? format(formData.expires_at, 'PPP') 
                          : <span>No expiry date</span>}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={formData.expires_at}
                        onSelect={handleDateChange}
                        initialFocus
                      />
                      <div className="p-2 border-t border-border">
                        <Button
                          variant="ghost"
                          className="w-full"
                          onClick={() => handleDateChange(undefined)}
                        >
                          Clear Date
                        </Button>
                      </div>
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-priority" className="text-right">Priority</Label>
                <Input
                  id="edit-priority"
                  name="priority"
                  type="number"
                  value={formData.priority}
                  onChange={handleFormChange}
                  className="col-span-1"
                  min={0}
                />
                <p className="text-sm text-muted-foreground col-span-2">
                  Higher numbers appear first
                </p>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label className="text-right">Status</Label>
                <div className="flex items-center col-span-3">
                  <input
                    type="checkbox"
                    id="edit-is_active"
                    name="is_active"
                    checked={formData.is_active}
                    onChange={handleCheckboxChange}
                    className="mr-2 h-4 w-4"
                  />
                  <Label htmlFor="edit-is_active">Active</Label>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setShowEditDialog(false)}>
                Cancel
              </Button>
              <Button type="submit">Save Changes</Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AnnouncementsManager;
