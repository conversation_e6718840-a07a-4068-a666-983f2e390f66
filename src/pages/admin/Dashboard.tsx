
import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { supabase } from '@/integrations/supabase/client';
import { Eye, Calendar, Image, MessagesSquare, Bell, FileText, BarChart3 } from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';
import { toast } from 'sonner';

const AdminDashboard = () => {
  const [stats, setStats] = useState({
    totalEvents: 0,
    activeAnnouncements: 0,
    galleryItems: 0,
    unreadMessages: 0,
  });
  const [recentActivity, setRecentActivity] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchDashboardData = async () => {
      setLoading(true);
      try {
        // Fetch events count
        const { count: eventsCount, error: eventsError } = await supabase
          .from('events')
          .select('*', { count: 'exact', head: true });
        
        if (eventsError) throw eventsError;

        // Fetch announcements count
        const { count: announcementsCount, error: announcementsError } = await supabase
          .from('announcements')
          .select('*', { count: 'exact', head: true })
          .eq('is_active', true);
        
        if (announcementsError) throw announcementsError;

        // Fetch gallery items count
        const { count: galleryCount, error: galleryError } = await supabase
          .from('gallery_items')
          .select('*', { count: 'exact', head: true });
        
        if (galleryError) throw galleryError;

        // Fetch unread messages count
        const { count: messagesCount, error: messagesError } = await supabase
          .from('contact_messages')
          .select('*', { count: 'exact', head: true })
          .eq('is_read', false);
        
        if (messagesError) throw messagesError;

        // Update stats
        setStats({
          totalEvents: eventsCount || 0,
          activeAnnouncements: announcementsCount || 0,
          galleryItems: galleryCount || 0,
          unreadMessages: messagesCount || 0,
        });

        // Fetch recent activities (combine from different tables)
        const fetchRecentActivities = async () => {
          // Get recent events
          const { data: recentEvents, error: recentEventsError } = await supabase
            .from('events')
            .select('id, title, created_at')
            .order('created_at', { ascending: false })
            .limit(2);
          
          if (recentEventsError) throw recentEventsError;
          
          // Get recent gallery uploads
          const { data: recentGallery, error: recentGalleryError } = await supabase
            .from('gallery_items')
            .select('id, title, created_at')
            .order('created_at', { ascending: false })
            .limit(2);
          
          if (recentGalleryError) throw recentGalleryError;
          
          // Get recent messages
          const { data: recentMessages, error: recentMessagesError } = await supabase
            .from('contact_messages')
            .select('id, name, email, created_at')
            .order('created_at', { ascending: false })
            .limit(2);
          
          if (recentMessagesError) throw recentMessagesError;
          
          // Combine and format activities
          const activities = [
            ...(recentEvents || []).map(event => ({
              action: "New event added",
              target: event.title,
              time: formatTimeAgo(new Date(event.created_at)),
              type: 'event',
              id: event.id
            })),
            ...(recentGallery || []).map(item => ({
              action: "Gallery updated",
              target: item.title,
              time: formatTimeAgo(new Date(item.created_at)),
              type: 'gallery',
              id: item.id
            })),
            ...(recentMessages || []).map(message => ({
              action: "New contact message",
              target: `From: ${message.name} (${message.email})`,
              time: formatTimeAgo(new Date(message.created_at)),
              type: 'message',
              id: message.id
            }))
          ];
          
          // Sort by most recent
          activities.sort((a, b) => {
            return new Date(b.time).getTime() - new Date(a.time).getTime();
          });
          
          setRecentActivity(activities.slice(0, 4));
        };
        
        await fetchRecentActivities();
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        toast.error('Failed to load dashboard data');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) return `${diffInSeconds} seconds ago`;
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    return `${Math.floor(diffInSeconds / 86400)} days ago`;
  };

  const handleQuickAction = (action: string) => {
    switch(action) {
      case 'create-event':
        navigate('/admin/events');
        break;
      case 'upload-images':
        navigate('/admin/gallery');
        break;
      case 'post-announcement':
        navigate('/admin/announcements');
        break;
      case 'edit-content':
        navigate('/admin/content');
        break;
      case 'view-analytics':
        toast.info('Analytics feature coming soon');
        break;
      default:
        break;
    }
  };

  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-heading text-gradient">Dashboard</h1>
        <Button className="button-gradient" onClick={() => window.open('/', '_blank')}>
          <Eye size={16} className="mr-2" /> View Website
        </Button>
      </div>
      
      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="hover-float">
          <CardHeader className="pb-2">
            <CardDescription>Total Events</CardDescription>
            <div className="flex items-center justify-between">
              {loading ? (
                <div className="h-8 w-20 bg-slate-200 dark:bg-slate-700 animate-pulse rounded"></div>
              ) : (
                <CardTitle className="text-3xl">{stats.totalEvents}</CardTitle>
              )}
              <Calendar className="h-8 w-8 text-temple-maroon dark:text-temple-gold opacity-80" />
            </div>
          </CardHeader>
          <CardContent>
            <Link to="/admin/events" className="temple-link text-sm flex items-center">
              Manage Events <Eye size={14} className="ml-1" />
            </Link>
          </CardContent>
        </Card>

        <Card className="hover-float">
          <CardHeader className="pb-2">
            <CardDescription>Active Announcements</CardDescription>
            <div className="flex items-center justify-between">
              {loading ? (
                <div className="h-8 w-20 bg-slate-200 dark:bg-slate-700 animate-pulse rounded"></div>
              ) : (
                <CardTitle className="text-3xl">{stats.activeAnnouncements}</CardTitle>
              )}
              <Bell className="h-8 w-8 text-temple-maroon dark:text-temple-gold opacity-80" />
            </div>
          </CardHeader>
          <CardContent>
            <Link to="/admin/announcements" className="temple-link text-sm flex items-center">
              Manage Announcements <Eye size={14} className="ml-1" />
            </Link>
          </CardContent>
        </Card>

        <Card className="hover-float">
          <CardHeader className="pb-2">
            <CardDescription>Gallery Items</CardDescription>
            <div className="flex items-center justify-between">
              {loading ? (
                <div className="h-8 w-20 bg-slate-200 dark:bg-slate-700 animate-pulse rounded"></div>
              ) : (
                <CardTitle className="text-3xl">{stats.galleryItems}</CardTitle>
              )}
              <Image className="h-8 w-8 text-temple-maroon dark:text-temple-gold opacity-80" />
            </div>
          </CardHeader>
          <CardContent>
            <Link to="/admin/gallery" className="temple-link text-sm flex items-center">
              Manage Gallery <Eye size={14} className="ml-1" />
            </Link>
          </CardContent>
        </Card>

        <Card className="hover-float">
          <CardHeader className="pb-2">
            <CardDescription>Unread Messages</CardDescription>
            <div className="flex items-center justify-between">
              {loading ? (
                <div className="h-8 w-20 bg-slate-200 dark:bg-slate-700 animate-pulse rounded"></div>
              ) : (
                <CardTitle className="text-3xl">{stats.unreadMessages}</CardTitle>
              )}
              <MessagesSquare className="h-8 w-8 text-temple-maroon dark:text-temple-gold opacity-80" />
            </div>
          </CardHeader>
          <CardContent>
            <Link to="/admin/messages" className="temple-link text-sm flex items-center">
              View Messages <Eye size={14} className="ml-1" />
            </Link>
          </CardContent>
        </Card>
      </div>
      
      {/* Quick Actions */}
      <h2 className="text-xl font-heading border-b border-slate-200 dark:border-slate-700 pb-2 text-temple-maroon dark:text-temple-gold">
        Quick Actions
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Button variant="outline" className="hover-glow flex items-center justify-start gap-2" onClick={() => handleQuickAction('create-event')}>
          <Calendar size={18} /> Create New Event
        </Button>
        <Button variant="outline" className="hover-glow flex items-center justify-start gap-2" onClick={() => handleQuickAction('upload-images')}>
          <Image size={18} /> Upload Gallery Images
        </Button>
        <Button variant="outline" className="hover-glow flex items-center justify-start gap-2" onClick={() => handleQuickAction('post-announcement')}>
          <Bell size={18} /> Post Announcement
        </Button>
        <Button variant="outline" className="hover-glow flex items-center justify-start gap-2" onClick={() => handleQuickAction('edit-content')}>
          <FileText size={18} /> Edit Home Page Content
        </Button>
        <Button variant="outline" className="hover-glow flex items-center justify-start gap-2" onClick={() => handleQuickAction('view-analytics')}>
          <BarChart3 size={18} /> View Analytics
        </Button>
      </div>
      
      {/* Recent Activity */}
      <h2 className="text-xl font-heading border-b border-slate-200 dark:border-slate-700 pb-2 mt-4 text-temple-maroon dark:text-temple-gold">
        Recent Activity
      </h2>
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow p-4 space-y-4">
        {loading ? (
          Array(4).fill(0).map((_, index) => (
            <div key={index} className="flex justify-between items-center py-2 border-b border-slate-200 dark:border-slate-700 last:border-0">
              <div className="space-y-2">
                <div className="h-5 w-40 bg-slate-200 dark:bg-slate-700 animate-pulse rounded"></div>
                <div className="h-4 w-60 bg-slate-200 dark:bg-slate-700 animate-pulse rounded"></div>
              </div>
              <div className="h-4 w-20 bg-slate-200 dark:bg-slate-700 animate-pulse rounded"></div>
            </div>
          ))
        ) : recentActivity.length > 0 ? (
          recentActivity.map((activity, index) => (
            <div 
              key={index} 
              className="flex justify-between items-center py-2 border-b border-slate-200 dark:border-slate-700 last:border-0"
            >
              <div>
                <p className="font-medium">{activity.action}</p>
                <p className="text-sm text-slate-500 dark:text-slate-400">{activity.target}</p>
              </div>
              <span className="text-xs text-slate-500 dark:text-slate-400">
                {activity.time}
              </span>
            </div>
          ))
        ) : (
          <div className="text-center py-6 text-slate-500">
            No recent activity found
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminDashboard;
