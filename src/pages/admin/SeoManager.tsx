import React from 'react';
import { Card, Card<PERSON>eader, Card<PERSON>itle, CardContent, CardDescription } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

const SeoManager: React.FC = () => {
  return (
    <div className="container mx-auto py-8 space-y-8">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🌐 SEO & Meta <Badge variant="secondary">Beta</Badge>
          </CardTitle>
          <CardDescription>
            Manage your website's SEO metadata, page titles, and descriptions.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <h3 className="font-semibold mb-2">SEO Settings</h3>
          <div className="text-muted-foreground mb-4">
            <ul className="list-disc pl-6">
              <li>Set site-wide meta title and description</li>
              <li>Manage Open Graph and Twitter Card tags</li>
              <li>Preview how your site appears in search results</li>
              <li>Future: Per-page SEO customization</li>
            </ul>
          </div>
          <div className="text-sm text-muted-foreground mb-2">SEO management features coming soon...</div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SeoManager;
