
import { useState, useEffect } from 'react';
import { 
  Card, 
  Card<PERSON>ontent, 
  CardHeader, 
  CardTitle, 
  CardDescription 
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON>alogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from '@/components/ui/button';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { Mail, Eye, Trash2 } from 'lucide-react';
import { format } from 'date-fns';

interface Message {
  id: string;
  name: string;
  email: string;
  phone?: string;
  message: string;
  is_read: boolean;
  created_at: string;
}

const MessagesManager = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedMessage, setSelectedMessage] = useState<Message | null>(null);
  const [viewMessageOpen, setViewMessageOpen] = useState(false);

  useEffect(() => {
    fetchMessages();
  }, []);

  const fetchMessages = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('contact_messages')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      setMessages(data || []);
    } catch (error) {
      console.error('Error fetching messages:', error);
      toast.error('Failed to fetch contact messages');
    } finally {
      setLoading(false);
    }
  };

  const handleViewMessage = async (message: Message) => {
    setSelectedMessage(message);
    setViewMessageOpen(true);
    
    if (!message.is_read) {
      try {
        const { error } = await supabase
          .from('contact_messages')
          .update({ is_read: true })
          .eq('id', message.id);
        
        if (error) throw error;
        
        // Update local state
        setMessages(messages.map(m => 
          m.id === message.id ? { ...m, is_read: true } : m
        ));
      } catch (error) {
        console.error('Error marking message as read:', error);
      }
    }
  };

  const handleDeleteMessage = async (id: string) => {
    if (confirm('Are you sure you want to delete this message?')) {
      try {
        const { error } = await supabase
          .from('contact_messages')
          .delete()
          .eq('id', id);

        if (error) throw error;
        
        toast.success('Message deleted successfully');
        setMessages(messages.filter(m => m.id !== id));
        
        if (selectedMessage?.id === id) {
          setSelectedMessage(null);
          setViewMessageOpen(false);
        }
      } catch (error) {
        console.error('Error deleting message:', error);
        toast.error('Failed to delete message');
      }
    }
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'PPP p');
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-heading text-gradient">Messages</h1>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Contact Messages</CardTitle>
          <CardDescription>View and manage messages from your contact form</CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-2">
              {Array(3).fill(0).map((_, index) => (
                <div key={index} className="py-3">
                  <div className="h-6 w-full bg-slate-200 dark:bg-slate-700 animate-pulse rounded mb-2" />
                  <div className="h-4 w-1/3 bg-slate-200 dark:bg-slate-700 animate-pulse rounded" />
                </div>
              ))}
            </div>
          ) : messages.length > 0 ? (
            <Table>
              <TableCaption>List of contact messages</TableCaption>
              <TableHeader>
                <TableRow>
                  <TableHead>From</TableHead>
                  <TableHead>Message</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {messages.map((message) => (
                  <TableRow key={message.id} className={message.is_read ? '' : 'bg-muted/30 font-medium'}>
                    <TableCell>
                      <div>
                        <p className="font-medium">{message.name}</p>
                        <p className="text-sm text-muted-foreground">{message.email}</p>
                        {message.phone && <p className="text-xs text-muted-foreground">{message.phone}</p>}
                      </div>
                    </TableCell>
                    <TableCell>
                      <p className="truncate max-w-[300px]">
                        {message.message}
                      </p>
                    </TableCell>
                    <TableCell>{formatDate(message.created_at)}</TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-8 w-8 p-0"
                          onClick={() => handleViewMessage(message)}
                        >
                          <Eye size={14} />
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          className="h-8 w-8 p-0"
                          onClick={() => handleDeleteMessage(message.id)}
                        >
                          <Trash2 size={14} />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="text-center py-12">
              <div className="mx-auto h-12 w-12 rounded-full bg-muted/80 flex items-center justify-center mb-4">
                <Mail className="h-6 w-6 text-muted-foreground" />
              </div>
              <h3 className="text-xl font-medium mb-2">No messages yet</h3>
              <p className="text-muted-foreground">
                When visitors submit the contact form, their messages will appear here.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* View Message Dialog */}
      <Dialog open={viewMessageOpen} onOpenChange={setViewMessageOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Message from {selectedMessage?.name}</DialogTitle>
          </DialogHeader>
          
          {selectedMessage && (
            <div className="space-y-4">
              <div className="flex flex-col space-y-1">
                <p className="text-sm font-medium text-muted-foreground">From:</p>
                <div className="bg-muted/50 p-3 rounded-md">
                  <p className="font-medium">{selectedMessage.name}</p>
                  <p>{selectedMessage.email}</p>
                  {selectedMessage.phone && <p>{selectedMessage.phone}</p>}
                </div>
              </div>
              
              <div className="flex flex-col space-y-1">
                <p className="text-sm font-medium text-muted-foreground">Date:</p>
                <p>{formatDate(selectedMessage.created_at)}</p>
              </div>
              
              <div className="flex flex-col space-y-1">
                <p className="text-sm font-medium text-muted-foreground">Message:</p>
                <div className="bg-muted/50 p-3 rounded-md whitespace-pre-wrap">
                  {selectedMessage.message}
                </div>
              </div>
            </div>
          )}
          
          <DialogFooter className="flex justify-between">
            <Button
              variant="destructive"
              onClick={() => selectedMessage && handleDeleteMessage(selectedMessage.id)}
            >
              <Trash2 className="mr-2 h-4 w-4" /> Delete Message
            </Button>
            <Button onClick={() => setViewMessageOpen(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default MessagesManager;
