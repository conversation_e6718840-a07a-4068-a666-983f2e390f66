import React, { useState } from 'react';
import { useContent } from '@/contexts/ContentContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { toast } from 'sonner';
import {
  Save,
  RefreshCw,
  Settings,
  Image,
  MessageSquare,
  Info,
  Globe,
  MapPin,
  Phone,
  Mail,
  Sparkles,
  Eye,
  Edit3,
  Plus,
  Trash2,
  Upload,
  Download
} from 'lucide-react';

const AdminPanel: React.FC = () => {
  const { content, loading, updateContent, refreshContent } = useContent();
  const [activeTab, setActiveTab] = useState('site-info');
  const [saving, setSaving] = useState(false);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-temple-gold mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading dynamic content system...</p>
        </div>
      </div>
    );
  }

  if (!content) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6 text-center">
            <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <RefreshCw className="w-6 h-6 text-red-600" />
            </div>
            <h3 className="font-semibold mb-2">Failed to load content</h3>
            <p className="text-muted-foreground mb-4">Unable to load the dynamic content system</p>
            <Button onClick={refreshContent} className="w-full">
              <RefreshCw className="w-4 h-4 mr-2" />
              Retry Loading
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const handleSave = async (section: string, data: any) => {
    setSaving(true);
    try {
      await updateContent({ [section]: data });
      toast.success(`${section} updated successfully!`);
    } catch (error) {
      toast.error(`Failed to update ${section}`);
    } finally {
      setSaving(false);
    }
  };

  const SiteInfoTab = () => {
    const [siteInfo, setSiteInfo] = useState(content.siteInfo);

    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Info className="h-5 w-5" />
            Site Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="siteName">Site Name</Label>
            <Input
              id="siteName"
              value={siteInfo.name}
              onChange={(e) => setSiteInfo({ ...siteInfo, name: e.target.value })}
            />
          </div>
          <div>
            <Label htmlFor="tagline">Tagline</Label>
            <Input
              id="tagline"
              value={siteInfo.tagline}
              onChange={(e) => setSiteInfo({ ...siteInfo, tagline: e.target.value })}
            />
          </div>
          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={siteInfo.description}
              onChange={(e) => setSiteInfo({ ...siteInfo, description: e.target.value })}
              rows={3}
            />
          </div>
          <div>
            <Label htmlFor="logo">Logo Path</Label>
            <Input
              id="logo"
              value={siteInfo.logo}
              onChange={(e) => setSiteInfo({ ...siteInfo, logo: e.target.value })}
            />
          </div>
          <Button 
            onClick={() => handleSave('siteInfo', siteInfo)}
            disabled={saving}
            className="w-full"
          >
            <Save className="h-4 w-4 mr-2" />
            {saving ? 'Saving...' : 'Save Site Info'}
          </Button>
        </CardContent>
      </Card>
    );
  };

  const ContactTab = () => {
    const [contactInfo, setContactInfo] = useState(content.contact);

    return (
      <Card>
        <CardHeader>
          <CardTitle>Contact Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="phone">Phone</Label>
            <Input
              id="phone"
              value={contactInfo.phone}
              onChange={(e) => setContactInfo({ ...contactInfo, phone: e.target.value })}
            />
          </div>
          <div>
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={contactInfo.email}
              onChange={(e) => setContactInfo({ ...contactInfo, email: e.target.value })}
            />
          </div>
          <div>
            <Label htmlFor="address1">Address Line 1</Label>
            <Input
              id="address1"
              value={contactInfo.address.line1}
              onChange={(e) => setContactInfo({ 
                ...contactInfo, 
                address: { ...contactInfo.address, line1: e.target.value }
              })}
            />
          </div>
          <div>
            <Label htmlFor="address2">Address Line 2</Label>
            <Input
              id="address2"
              value={contactInfo.address.line2}
              onChange={(e) => setContactInfo({ 
                ...contactInfo, 
                address: { ...contactInfo.address, line2: e.target.value }
              })}
            />
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="lat">Latitude</Label>
              <Input
                id="lat"
                type="number"
                step="any"
                value={contactInfo.coordinates.lat}
                onChange={(e) => setContactInfo({ 
                  ...contactInfo, 
                  coordinates: { ...contactInfo.coordinates, lat: parseFloat(e.target.value) }
                })}
              />
            </div>
            <div>
              <Label htmlFor="lng">Longitude</Label>
              <Input
                id="lng"
                type="number"
                step="any"
                value={contactInfo.coordinates.lng}
                onChange={(e) => setContactInfo({ 
                  ...contactInfo, 
                  coordinates: { ...contactInfo.coordinates, lng: parseFloat(e.target.value) }
                })}
              />
            </div>
          </div>
          <Button 
            onClick={() => handleSave('contact', contactInfo)}
            disabled={saving}
            className="w-full"
          >
            <Save className="h-4 w-4 mr-2" />
            {saving ? 'Saving...' : 'Save Contact Info'}
          </Button>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <div className="flex items-center gap-2 mb-2">
            <div className="w-8 h-8 bg-gradient-to-br from-temple-gold to-temple-saffron rounded-lg flex items-center justify-center">
              <Sparkles className="w-5 h-5 text-white" />
            </div>
            <h1 className="text-2xl font-bold">Dynamic Content Manager</h1>
            <Badge variant="secondary" className="bg-green-100 text-green-800">
              Live System
            </Badge>
          </div>
          <p className="text-muted-foreground">
            Manage all website content dynamically without code changes
          </p>
        </div>

        <div className="flex items-center gap-2">
          <Button onClick={refreshContent} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Reset to Default
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export Content
          </Button>
          <Button variant="outline" size="sm">
            <Upload className="h-4 w-4 mr-2" />
            Import Content
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Sections</p>
                <p className="text-2xl font-bold">8</p>
              </div>
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <Settings className="w-4 h-4 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Hero Slides</p>
                <p className="text-2xl font-bold">{content.hero?.slides?.length || 0}</p>
              </div>
              <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                <Image className="w-4 h-4 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Announcements</p>
                <p className="text-2xl font-bold">{content.announcements?.length || 0}</p>
              </div>
              <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <MessageSquare className="w-4 h-4 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Last Updated</p>
                <p className="text-sm font-medium">Just now</p>
              </div>
              <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                <Eye className="w-4 h-4 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Content Management Tabs */}
      <Card>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Edit3 className="w-5 h-5" />
                  Content Sections
                </CardTitle>
                <p className="text-sm text-muted-foreground mt-1">
                  Edit different sections of your website content
                </p>
              </div>
              <Badge variant="outline" className="text-xs">
                {activeTab.replace('-', ' ').toUpperCase()}
              </Badge>
            </div>
          </CardHeader>

          <CardContent>
            <TabsList className="grid w-full grid-cols-2 lg:grid-cols-4 mb-6">
              <TabsTrigger value="site-info" className="flex items-center gap-2">
                <Info className="h-4 w-4" />
                <span className="hidden sm:inline">Site Info</span>
              </TabsTrigger>
              <TabsTrigger value="contact" className="flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                <span className="hidden sm:inline">Contact</span>
              </TabsTrigger>
              <TabsTrigger value="hero" className="flex items-center gap-2">
                <Image className="h-4 w-4" />
                <span className="hidden sm:inline">Hero</span>
              </TabsTrigger>
              <TabsTrigger value="announcements" className="flex items-center gap-2">
                <MessageSquare className="h-4 w-4" />
                <span className="hidden sm:inline">Announcements</span>
              </TabsTrigger>
            </TabsList>

          <TabsContent value="site-info">
            <SiteInfoTab />
          </TabsContent>

          <TabsContent value="contact">
            <ContactTab />
          </TabsContent>

          <TabsContent value="hero">
            <Card>
              <CardHeader>
                <CardTitle>Hero Section</CardTitle>
              </CardHeader>
              <CardContent>
                <p>Hero section management coming soon...</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="announcements">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Manage Announcements</h3>
                <Button size="sm">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Announcement
                </Button>
              </div>
              <Card>
                <CardContent className="p-6">
                  <div className="text-center py-8">
                    <MessageSquare className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="font-semibold mb-2">Announcements Management</h3>
                    <p className="text-muted-foreground mb-4">
                      Advanced announcement management coming soon
                    </p>
                    <Badge variant="secondary">Coming Soon</Badge>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          </CardContent>
        </Tabs>
      </Card>
    </div>
  );
};

export default AdminPanel;
