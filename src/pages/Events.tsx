
import { Layout } from "@/components/Layout";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Calendar } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useState } from "react";

// Sample events data - replace with actual data from backend in production
const upcomingEvents = [
  {
    id: 1,
    title: "504th Sri Saraswati Yagnam",
    date: "May 25, 2025",
    time: "8:00 AM - 1:00 PM",
    description: "Join us for the sacred 504th Sri Saraswati Yagnam ceremony and receive divine blessings. This auspicious event includes special rituals, prayers, and prasadam distribution.",
    image: "/images/events/yagnam.jpg",
    category: "yagnam"
  }
];

const pastEvents = [
  {
    id: 2,
    title: "Navaratri Celebrations",
    date: "October 15-24, 2023",
    time: "6:00 PM - 9:00 PM",
    description: "Nine nights of devotion to the divine mother with special pujas, music, and cultural programs.",
    image: "/images/event.jpg.jpg",
    category: "festival"
  },
  {
    id: 3,
    title: "Ganesha Chaturthi",
    date: "August 20, 2023",
    time: "7:00 AM - 8:00 PM",
    description: "Celebrate the birth of Lord Ganesha with special abhishekam and prasadam distribution.",
    image: "/images/event_upcoming1.jpg",
    category: "festival"
  },
  {
    id: 4,
    title: "Annual Temple Anniversary",
    date: "March 15, 2023",
    time: "9:00 AM - 8:00 PM",
    description: "Celebration of our temple's anniversary with special pujas and cultural programs.",
    image: "/images/event_upcoming2.jpg",
    category: "festival"
  }
];

const Events = () => {
  const [filter, setFilter] = useState("all");

  // No RSVP functionality needed

  // Filter for past events
  const filteredPastEvents = filter === "all"
    ? pastEvents
    : pastEvents.filter(event => event.category === filter);

  return (
    <Layout>
      {/* Hero Section */}
      <div
        className="h-[40vh] bg-cover bg-center flex items-center justify-center"
        style={{ backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.7)), url('/images/AS_Overview.jpg')` }}
      >
        <div className="text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-temple-ivory mb-4 font-heading tracking-wider">
            Events & Announcements
          </h1>
          <p className="text-xl text-temple-ivory/90 max-w-2xl mx-auto px-4">
            Stay updated with our temple's activities and celebrations
          </p>
        </div>
      </div>

      {/* Events Section */}
      <div className="py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto mb-10">
            <h2 className="text-3xl font-heading text-temple-maroon dark:text-temple-gold mb-6 text-center">
              Upcoming Events
            </h2>

            <div className="temple-divider w-32 mx-auto mb-8"></div>

            {upcomingEvents.length > 0 && (
              <div className="max-w-4xl mx-auto">
                {upcomingEvents.map(event => (
                  <Card
                    key={event.id}
                    className="hover-glow border-temple-gold/20 overflow-hidden group transition-all duration-300 hover:shadow-lg mb-8"
                  >
                    <div className="w-full overflow-hidden">
                      <img
                        src={event.image}
                        alt={event.title}
                        className="w-full object-contain max-h-[400px]"
                      />
                    </div>
                    <CardHeader>
                      <CardTitle className="font-heading text-temple-maroon dark:text-temple-gold text-xl md:text-2xl">
                        {event.title}
                      </CardTitle>
                      <div className="flex items-center text-sm text-muted-foreground mt-2">
                        <Calendar className="h-4 w-4 mr-2 text-temple-gold" />
                        <span className="font-medium">{event.date}</span>
                      </div>
                    </CardHeader>
                    <CardContent className="pb-6">
                      <p className="text-sm text-muted-foreground">
                        {event.description}
                      </p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Previous Events */}
      <div className="py-12 bg-secondary/50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h3 className="text-2xl font-heading text-temple-maroon dark:text-temple-gold text-center mb-4">
              Previous Events
            </h3>

            <div className="temple-divider w-24 mx-auto mb-8"></div>

            <p className="text-muted-foreground text-center mb-8">
              Browse through our gallery of past events and celebrations at Anantasagar Kshetramu.
            </p>

            <div className="flex justify-end mb-6">
              <div className="w-48">
                <Select value={filter} onValueChange={setFilter}>
                  <SelectTrigger className="border-temple-gold/20">
                    <SelectValue placeholder="Filter by type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Events</SelectItem>
                    <SelectItem value="yagnam">Yagnam</SelectItem>
                    <SelectItem value="festival">Festivals</SelectItem>
                    <SelectItem value="puja">Pujas</SelectItem>
                    <SelectItem value="cultural">Cultural</SelectItem>
                    <SelectItem value="workshop">Workshops</SelectItem>
                    <SelectItem value="educational">Educational</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Past Events Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
              {filteredPastEvents.map(event => (
                <Card key={event.id} className="hover-glow border-temple-gold/20 overflow-hidden group transition-all duration-300 hover:shadow-lg">
                  <div className="w-full overflow-hidden">
                    <img
                      src={event.image}
                      alt={event.title}
                      className="w-full h-48 object-cover transition-transform duration-500 group-hover:scale-105"
                    />
                  </div>
                  <CardHeader className="pb-2">
                    <CardTitle className="font-heading text-temple-maroon dark:text-temple-gold text-lg">
                      {event.title}
                    </CardTitle>
                    <div className="flex items-center text-xs text-muted-foreground mt-1">
                      <Calendar className="h-3 w-3 mr-1 text-temple-gold" />
                      <span className="font-medium">{event.date}</span>
                    </div>
                  </CardHeader>
                </Card>
              ))}
            </div>

            {/* Past Events Gallery */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-8">
              <div className="aspect-square overflow-hidden rounded-lg">
                <img src="/images/event.jpg.jpg" alt="Previous Event" className="w-full h-full object-cover hover:scale-110 transition-transform duration-500" />
              </div>
              <div className="aspect-square overflow-hidden rounded-lg">
                <img src="/images/event_upcoming1.jpg" alt="Previous Event" className="w-full h-full object-cover hover:scale-110 transition-transform duration-500" />
              </div>
              <div className="aspect-square overflow-hidden rounded-lg">
                <img src="/images/event_upcoming2.jpg" alt="Previous Event" className="w-full h-full object-cover hover:scale-110 transition-transform duration-500" />
              </div>
              <div className="aspect-square overflow-hidden rounded-lg">
                <img src="/images/AS_Overview.jpg" alt="Previous Event" className="w-full h-full object-cover hover:scale-110 transition-transform duration-500" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Events;
