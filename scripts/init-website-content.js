// Script to initialize website content
import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fetch from 'node-fetch';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const envPath = join(__dirname, '..', '.env');

config({ path: envPath });

// Function to validate auth token
async function validateAuth(url, key) {
  try {
    const response = await fetch(`${url}/auth/v1/user`, {
      headers: {
        'Authorization': `Bearer ${key}`,
        'apikey': key
      }
    });
    return response.status !== 401;
  } catch (error) {
    console.error('Auth validation error:', error);
    return false;
  }
}

const SUPABASE_URL = process.env.VITE_SUPABASE_URL;
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;
const SUPABASE_ANON_KEY = process.env.VITE_SUPABASE_ANON_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
  console.error('Missing environment variables. Please check your .env file.');
  process.exit(1);
}

// Try both service role and anon key
const serviceRoleValid = await validateAuth(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);
const anonKeyValid = await validateAuth(SUPABASE_URL, SUPABASE_ANON_KEY);

const ACTIVE_KEY = serviceRoleValid ? SUPABASE_SERVICE_ROLE_KEY : 
                  anonKeyValid ? SUPABASE_ANON_KEY : null;

if (!ACTIVE_KEY) {
  console.error('Neither service role key nor anon key is valid');
  process.exit(1);
}

console.log('Using key type:', serviceRoleValid ? 'service_role' : 'anon');

const supabase = createClient(SUPABASE_URL, ACTIVE_KEY, {
  auth: { persistSession: false },
  db: { schema: 'public' }
});

// Initial default content
const defaultContent = {
  hero: {
    slides: [],
    autoplaySpeed: 5000,
    isAutoplay: true
  },
  announcements: [],
  navigation: {
    mainMenu: [],
    footerMenu: []
  },
  reviews: [],
  locations: [],
  socialMedia: [],
  settings: {
    isMaintenanceMode: false,
    maintenanceMessage: '',
    popups: {
      isEnabled: false,
      title: '',
      content: '',
      showDelay: 5000
    }
  },
  about: {
    hero: {
      showContent: true,
      title: 'About Us',
      subtitle: 'Welcome to Anantasagar Kshetramu',
      backgroundImage: ''
    },
    sections: []
  },
  gallery: [],
  footer: {
    text: '',
    links: [],
    logo: '',
    backgroundImage: ''
  }
};

// Helper function to ensure the website_content table exists
async function ensureTableExists() {
  try {
    await supabase.rpc('create_website_content_if_not_exists');
  } catch (error) {
    console.warn('Note: RPC not available, table might already exist:', error.message);
  }
}

async function initializeWebsiteContent() {
  try {
    await ensureTableExists();
    
    // Check if table has any content
    const { data, error: fetchError } = await supabase
      .from('website_content')
      .select('*')
      .limit(1);

    if (fetchError) {
      throw new Error('Error fetching content: ' + fetchError.message);
    }

    if (data && data.length > 0) {
      console.log('Website content already exists. Checking for schema updates...');
      // Update existing content with any missing fields from default content
      const existingContent = data[0].data;
      const updatedContent = mergeWithDefaults(existingContent, defaultContent);
      
      const { error: updateError } = await supabase
        .from('website_content')
        .update({ data: updatedContent })
        .eq('id', data[0].id);

      if (updateError) {
        throw new Error('Error updating content: ' + updateError.message);
      }
      console.log('Content schema updated successfully');
    } else {
      // Create new content
      const { error: insertError } = await supabase
        .from('website_content')
        .insert([{ data: defaultContent }]);

      if (insertError) {
        throw new Error('Error inserting content: ' + insertError.message);
      }
      console.log('Default content initialized successfully');
    }

  } catch (error) {
    console.error('Failed to initialize website content:', error);
    process.exit(1);
  }
}

// Helper function to merge existing content with defaults
function mergeWithDefaults(existing, defaults) {
  const result = { ...defaults };
  
  for (const [key, value] of Object.entries(existing)) {
    if (value !== null && typeof value === 'object' && !Array.isArray(value)) {
      result[key] = mergeWithDefaults(value, defaults[key] || {});
    } else {
      result[key] = value;
    }
  }
  
  return result;
}

initializeWebsiteContent().then(() => {
  console.log('Content initialization complete');
  process.exit(0);
});
