import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Paths
const rootDir = path.resolve(__dirname, '..');
const distDir = path.join(rootDir, 'dist');
const deployDir = path.join(rootDir, 'deploy');

// Create deploy directory if it doesn't exist
if (!fs.existsSync(deployDir)) {
  fs.mkdirSync(deployDir);
}

// Create cPanel and Netlify directories
const cpanelDir = path.join(deployDir, 'cpanel');
const netlifyDir = path.join(deployDir, 'netlify');

if (!fs.existsSync(cpanelDir)) {
  fs.mkdirSync(cpanelDir);
}

if (!fs.existsSync(netlifyDir)) {
  fs.mkdirSync(netlifyDir);
}

// Function to copy directory recursively
function copyDirectory(source, destination) {
  if (!fs.existsSync(destination)) {
    fs.mkdirSync(destination);
  }

  const files = fs.readdirSync(source);

  for (const file of files) {
    const sourcePath = path.join(source, file);
    const destPath = path.join(destination, file);
    
    const stat = fs.statSync(sourcePath);
    
    if (stat.isDirectory()) {
      copyDirectory(sourcePath, destPath);
    } else {
      fs.copyFileSync(sourcePath, destPath);
    }
  }
}

// Copy dist to both deployment directories
copyDirectory(distDir, cpanelDir);
copyDirectory(distDir, netlifyDir);

// Copy netlify.toml to Netlify directory
fs.copyFileSync(path.join(rootDir, 'netlify.toml'), path.join(netlifyDir, 'netlify.toml'));

console.log('Deployment files prepared successfully!');
console.log(`cPanel files: ${cpanelDir}`);
console.log(`Netlify files: ${netlifyDir}`);
console.log('\nTo deploy:');
console.log('- For cPanel: Upload all files from the "deploy/cpanel" directory to your cPanel hosting');
console.log('- For Netlify: Upload the "deploy/netlify" directory or connect your repository to Netlify');
