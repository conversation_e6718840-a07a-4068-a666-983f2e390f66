// <PERSON>ript to apply database schema
import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { readFileSync } from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const envPath = join(__dirname, '..', '.env');
const envLocalPath = join(__dirname, '..', '.env.local');

// Load both .env and .env.local files
config({ path: envPath });
config({ path: envLocalPath, override: true });

const SUPABASE_URL = process.env.VITE_SUPABASE_URL;
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

// Debug environment variables
console.log('Environment variables loaded from:', envPath, 'and', envLocalPath);

if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
  console.error('Missing environment variables. Please check your .env file.');
  process.exit(1);
}

console.log('Using Supabase URL:', SUPABASE_URL);

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, {
  auth: { persistSession: false },
  db: {
    schema: 'public'
  }
});

async function applySchema() {
  try {
    // First check if the website_content table exists
    const { data: tablesData, error: tablesError } = await supabase
      .from('website_content')
      .select('id')
      .limit(1);

    if (tablesError) {
      if (tablesError.message.includes('does not exist')) {
        console.log('Table does not exist, creating...');
        
        // Create the table using REST API
        const { error: createError } = await supabase
          .from('website_content')
          .insert([{ 
            data: {}, 
            id: 'init' 
          }])
          .select();

        if (createError && !createError.message.includes('already exists')) {
          throw createError;
        }
      } else {
        throw tablesError;
      }
    }

    console.log('Schema applied successfully');
  } catch (error) {
    console.error('Error applying schema:', error);
    process.exit(1);
  }
}

applySchema();
