// Script to verify Supabase connection
const { createClient } = require('@supabase/supabase-js');
const { config } = require('dotenv');
const { join } = require('path');

// __dirname is already available in CommonJS

// Load environment variables
config({ path: join(__dirname, '..', '.env') });
config({ path: join(__dirname, '..', '.env.local'), override: true });

const SUPABASE_URL = process.env.VITE_SUPABASE_URL;
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;
const SUPABASE_ANON_KEY = process.env.VITE_SUPABASE_ANON_KEY;

console.log('Checking Supabase configuration...');
console.log('URL:', SUPABASE_URL);
console.log('Service Role Key exists:', !!SUPABASE_SERVICE_ROLE_KEY);
console.log('Anon Key exists:', !!SUPABASE_ANON_KEY);

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, {
  auth: { persistSession: false }
});

async function verifyConnection() {
  try {
    // Try to fetch a simple query
    const { data, error } = await supabase.from('website_content').select('id').limit(1);
    
    if (error) {
      console.error('Error connecting to Supabase:', error.message);
      return;
    }
    
    console.log('Successfully connected to Supabase!');
    console.log('Test query result:', data);
  } catch (err) {
    console.error('Unexpected error:', err);
  }
}

verifyConnection();
