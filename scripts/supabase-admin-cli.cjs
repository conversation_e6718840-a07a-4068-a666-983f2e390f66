// Supabase Admin CLI Script
// Usage: node scripts/supabase-admin-cli.js <command> [args]
// Requires: npm install @supabase/supabase-js dotenv

require('dotenv').config({ path: '.env' });
const { createClient } = require('@supabase/supabase-js');

const SUPABASE_URL = process.env.VITE_SUPABASE_URL;
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY; // Use service role for admin ops
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, { auth: { persistSession: false } });

async function listUsers() {
  const { data, error } = await supabase.auth.admin.listUsers();
  if (error) throw error;
  console.log('Users:', data.users.map(u => ({ id: u.id, email: u.email, role: u.user_metadata?.role })));
}

async function setUserRole(userId, role) {
  const { data, error } = await supabase.auth.admin.updateUserById(userId, {
    user_metadata: { role }
  });
  if (error) throw error;
  console.log(`Set role for user ${userId} to ${role}`);
}

async function getContent() {
  const { data, error } = await supabase.from('website_content').select('*');
  if (error) throw error;
  console.log('Content:', data);
}

async function updateContent(rowId, newContent) {
  const { error } = await supabase.from('website_content').update({ data: newContent }).eq('id', rowId);
  if (error) throw error;
  console.log('Content updated for row', rowId);
}

async function uploadImage(filePath, destPath) {
  const fs = require('fs');
  const file = fs.readFileSync(filePath);
  const { data, error } = await supabase.storage.from('images').upload(destPath, file, { upsert: true });
  if (error) throw error;
  const publicUrl = supabase.storage.from('images').getPublicUrl(destPath).data.publicUrl;
  console.log('Image uploaded:', publicUrl);
}

async function main() {
  const [cmd, ...args] = process.argv.slice(2);
  try {
    if (cmd === 'list-users') await listUsers();
    else if (cmd === 'set-role') await setUserRole(args[0], args[1]);
    else if (cmd === 'get-content') await getContent();
    else if (cmd === 'update-content') await updateContent(args[0], JSON.parse(args[1]));
    else if (cmd === 'upload-image') await uploadImage(args[0], args[1]);
    else console.log('Commands: list-users | set-role <userId> <role> | get-content | update-content <rowId> <json> | upload-image <filePath> <destPath>');
  } catch (e) {
    console.error('Error:', e.message);
  }
}

main();
